{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 215, "timestamp": "2025-08-16 11:25:16,100", "message": "Backend server starting with WebSocket support.", "time": "2025-08-16T15:25:16.101002+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 29, "timestamp": "2025-08-16 11:25:16,103", "message": "Running periodic anomaly detection.", "time": "2025-08-16T15:25:16.103404+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 215, "timestamp": "2025-08-16 11:25:39,355", "message": "Backend server starting with WebSocket support.", "time": "2025-08-16T15:25:39.356065+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 29, "timestamp": "2025-08-16 11:25:39,357", "message": "Running periodic anomaly detection.", "time": "2025-08-16T15:25:39.357858+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 29, "timestamp": "2025-08-16 11:26:39,377", "message": "Running periodic anomaly detection.", "time": "2025-08-16T15:26:39.380932+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 29, "timestamp": "2025-08-16 11:27:39,684", "message": "Running periodic anomaly detection.", "time": "2025-08-16T15:27:39.686052+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 29, "timestamp": "2025-08-16 11:28:39,811", "message": "Running periodic anomaly detection.", "time": "2025-08-16T15:28:39.811746+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 29, "timestamp": "2025-08-16 11:29:39,908", "message": "Running periodic anomaly detection.", "time": "2025-08-16T15:29:39.910318+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 29, "timestamp": "2025-08-16 11:30:40,020", "message": "Running periodic anomaly detection.", "time": "2025-08-16T15:30:40.021417+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 29, "timestamp": "2025-08-16 11:31:40,122", "message": "Running periodic anomaly detection.", "time": "2025-08-16T15:31:40.122375+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 218, "timestamp": "2025-08-16 12:48:01,196", "message": "Backend server starting with WebSocket support.", "time": "2025-08-16T16:48:01.196411+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 29, "timestamp": "2025-08-16 12:48:01,199", "message": "Running periodic anomaly detection.", "time": "2025-08-16T16:48:01.199751+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 218, "timestamp": "2025-08-16 13:45:46,905", "message": "Backend server starting with WebSocket support.", "time": "2025-08-16T17:45:46.905702+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 29, "timestamp": "2025-08-16 13:45:46,907", "message": "Running periodic anomaly detection.", "time": "2025-08-16T17:45:46.908082+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 234, "timestamp": "2025-08-16 14:21:22,885", "message": "Backend server starting with WebSocket support.", "time": "2025-08-16T18:21:22.885895+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 30, "timestamp": "2025-08-16 14:21:22,887", "message": "Initial training of anomaly detection model...", "time": "2025-08-16T18:21:22.887673+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 32, "timestamp": "2025-08-16 14:21:23,009", "message": "Anomaly detection model training complete.", "time": "2025-08-16T18:21:23.009782+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 35, "timestamp": "2025-08-16 14:21:23,011", "message": "Running periodic anomaly detection.", "time": "2025-08-16T18:21:23.011109+00:00"}
{"name": "system_events", "levelname": "WARNING", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 38, "timestamp": "2025-08-16 14:21:23,041", "message": "Detected 1 potential anomalies.", "time": "2025-08-16T18:21:23.041804+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 256, "timestamp": "2025-08-16 15:14:54,661", "message": "Backend server starting with WebSocket support.", "time": "2025-08-16T19:14:54.661884+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 30, "timestamp": "2025-08-16 15:14:54,665", "message": "Initial training of anomaly detection model...", "time": "2025-08-16T19:14:54.665218+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 32, "timestamp": "2025-08-16 15:14:54,937", "message": "Anomaly detection model training complete.", "time": "2025-08-16T19:14:54.937447+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 35, "timestamp": "2025-08-16 15:14:54,938", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:14:54.938833+00:00"}
{"name": "system_events", "levelname": "WARNING", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 38, "timestamp": "2025-08-16 15:14:54,983", "message": "Detected 1 potential anomalies.", "time": "2025-08-16T19:14:54.984023+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 64, "timestamp": "2025-08-16 15:39:55,917", "message": "Backend server starting with WebSocket support.", "time": "2025-08-16T19:39:55.918023+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 38, "timestamp": "2025-08-16 15:39:55,919", "message": "Initial training of anomaly detection model...", "time": "2025-08-16T19:39:55.919816+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 40, "timestamp": "2025-08-16 15:39:56,029", "message": "Anomaly detection model training complete.", "time": "2025-08-16T19:39:56.029476+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:39:56,030", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:39:56.030798+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:40:56,063", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:40:56.064123+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:41:56,110", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:41:56.110973+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:42:56,163", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:42:56.163608+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:43:56,254", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:43:56.254486+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:44:56,300", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:44:56.300345+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:45:56,359", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:45:56.359982+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:46:56,454", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:46:56.454778+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:47:56,574", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:47:56.575032+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:48:56,626", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:48:56.627232+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:49:56,676", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:49:56.676845+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:50:56,728", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:50:56.728224+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:51:56,801", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:51:56.801397+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:52:56,858", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:52:56.859124+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:53:56,927", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:53:56.929123+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:54:56,983", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:54:56.984698+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:55:57,097", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:55:57.107338+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:56:57,237", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:56:57.238251+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:57:57,304", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:57:57.304250+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:58:57,376", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:58:57.376892+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 15:59:57,450", "message": "Running periodic anomaly detection.", "time": "2025-08-16T19:59:57.450265+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:00:57,506", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:00:57.507072+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:01:57,569", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:01:57.569671+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:02:57,631", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:02:57.632272+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:03:57,708", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:03:57.709449+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:04:57,776", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:04:57.777548+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:05:57,837", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:05:57.837513+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:06:57,905", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:06:57.905746+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:07:57,976", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:07:57.976588+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:08:58,066", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:08:58.066255+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:09:58,129", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:09:58.130221+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:10:58,192", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:10:58.192365+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:11:58,259", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:11:58.260340+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:12:58,333", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:12:58.333524+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:13:58,392", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:13:58.392397+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:14:58,463", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:14:58.463266+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:15:58,523", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:15:58.523757+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:16:58,588", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:16:58.589151+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:17:58,664", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:17:58.664910+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:18:58,746", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:18:58.746320+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:19:58,819", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:19:58.820160+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:20:58,892", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:20:58.892618+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:21:58,969", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:21:58.969547+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:22:59,036", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:22:59.037185+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:23:59,108", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:23:59.109435+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:24:59,251", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:24:59.252128+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:25:59,319", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:25:59.320529+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:26:59,393", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:26:59.393539+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:27:59,484", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:27:59.495362+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:28:59,569", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:28:59.588797+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:29:59,714", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:29:59.714837+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:30:59,793", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:30:59.793263+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:31:59,861", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:31:59.861779+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:32:59,936", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:32:59.936550+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:34:00,026", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:34:00.026983+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:35:00,107", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:35:00.108885+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:36:00,186", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:36:00.186410+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:37:00,264", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:37:00.264529+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:38:00,337", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:38:00.337593+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:39:00,418", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:39:00.418464+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:40:00,517", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:40:00.517913+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:41:00,606", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:41:00.606593+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:42:00,737", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:42:00.737841+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:43:00,815", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:43:00.815845+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:44:00,903", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:44:00.903665+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:45:01,025", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:45:01.025969+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:46:01,139", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:46:01.139386+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:47:01,257", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:47:01.258020+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:48:01,349", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:48:01.350238+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:49:01,444", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:49:01.445101+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:50:01,529", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:50:01.529603+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:51:01,611", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:51:01.611246+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:52:01,694", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:52:01.695145+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:53:01,785", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:53:01.785282+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:54:01,876", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:54:01.876267+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:55:01,971", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:55:01.971878+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:56:02,072", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:56:02.075899+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:57:02,523", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:57:02.523540+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:58:02,607", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:58:02.607458+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 16:59:02,726", "message": "Running periodic anomaly detection.", "time": "2025-08-16T20:59:02.737900+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:00:02,824", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:00:02.824898+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:01:02,908", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:01:02.908663+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:02:02,988", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:02:02.988926+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:03:03,093", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:03:03.093346+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:04:03,174", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:04:03.174902+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:05:03,265", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:05:03.265909+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:06:03,346", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:06:03.346986+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:07:03,428", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:07:03.429165+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:08:03,515", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:08:03.515887+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:09:03,595", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:09:03.595690+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:10:03,675", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:10:03.675852+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:11:03,763", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:11:03.763674+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:12:03,844", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:12:03.844446+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:13:03,916", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:13:03.916458+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:14:03,998", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:14:03.998719+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:15:04,080", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:15:04.080971+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:16:04,160", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:16:04.160805+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:17:04,264", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:17:04.265038+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:18:04,362", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:18:04.362977+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:19:04,458", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:19:04.458946+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:20:04,542", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:20:04.542349+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:21:04,637", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:21:04.637396+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:22:04,730", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:22:04.730321+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:23:04,811", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:23:04.811775+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:24:04,888", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:24:04.898605+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:25:04,989", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:25:04.990242+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:26:05,080", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:26:05.080516+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:27:05,159", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:27:05.159947+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:28:05,245", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:28:05.245228+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:29:05,324", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:29:05.325130+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:30:05,453", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:30:05.453609+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:31:05,540", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:31:05.541274+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:32:05,631", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:32:05.631427+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:33:05,726", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:33:05.727076+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:34:05,806", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:34:05.806933+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:35:05,889", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:35:05.890044+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:36:06,055", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:36:06.055453+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:37:06,157", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:37:06.157996+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:38:06,244", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:38:06.244617+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:39:06,324", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:39:06.324864+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:40:06,422", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:40:06.423286+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:41:06,511", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:41:06.511922+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:42:06,617", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:42:06.617402+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:43:06,703", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:43:06.703493+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:44:06,802", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:44:06.803293+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:45:06,880", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:45:06.880563+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:46:06,997", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:46:06.997580+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:47:07,084", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:47:07.084586+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:48:07,184", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:48:07.185653+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:49:07,255", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:49:07.255964+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:50:07,339", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:50:07.339382+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:51:07,425", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:51:07.425836+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:52:07,513", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:52:07.513264+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:53:07,594", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:53:07.595145+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:54:07,683", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:54:07.683397+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:55:07,770", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:55:07.771774+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:56:08,344", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:56:08.344872+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:57:08,426", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:57:08.426663+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:58:08,517", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:58:08.518261+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 17:59:08,605", "message": "Running periodic anomaly detection.", "time": "2025-08-16T21:59:08.606113+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:00:08,689", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:00:08.690273+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:01:08,788", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:01:08.789319+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:02:08,878", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:02:08.878978+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:03:08,959", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:03:08.959725+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:04:09,061", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:04:09.062553+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:05:09,151", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:05:09.151207+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:06:09,238", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:06:09.238755+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:07:09,340", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:07:09.349291+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:08:09,427", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:08:09.427664+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:09:09,512", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:09:09.521929+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:10:09,606", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:10:09.607058+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:11:09,731", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:11:09.732202+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:12:09,818", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:12:09.828527+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:13:09,919", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:13:09.928271+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:14:10,032", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:14:10.033487+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:15:10,136", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:15:10.136263+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:16:10,217", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:16:10.227523+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:17:10,334", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:17:10.334855+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:18:10,412", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:18:10.412328+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:19:10,497", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:19:10.498095+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:20:10,595", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:20:10.595996+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:21:10,694", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:21:10.694557+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:22:10,778", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:22:10.780181+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:23:10,902", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:23:10.902757+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:24:11,005", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:24:11.006188+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:25:11,254", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:25:11.254188+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:26:11,337", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:26:11.337580+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:27:11,431", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:27:11.431816+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:28:11,530", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:28:11.530658+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:29:11,615", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:29:11.615467+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:30:11,699", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:30:11.699538+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:31:11,778", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:31:11.779179+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:32:11,858", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:32:11.858918+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:33:11,960", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:33:11.961138+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:34:12,050", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:34:12.050173+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:35:12,134", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:35:12.134758+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:36:12,246", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:36:12.246638+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:37:12,343", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:37:12.344292+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:38:12,443", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:38:12.443605+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:39:12,536", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:39:12.536588+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:40:12,631", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:40:12.632085+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:41:12,724", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:41:12.725003+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:42:12,808", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:42:12.808874+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:43:12,915", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:43:12.915844+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:44:13,003", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:44:13.003722+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:45:13,081", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:45:13.081530+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:46:13,216", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:46:13.216470+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:47:13,320", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:47:13.320931+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:48:13,412", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:48:13.415814+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:49:13,535", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:49:13.535371+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:50:13,607", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:50:13.608332+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:51:13,693", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:51:13.693921+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:52:13,784", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:52:13.785330+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:53:13,872", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:53:13.873096+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:54:14,008", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:54:14.009026+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:55:14,099", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:55:14.099723+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:56:14,229", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:56:14.230158+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:57:14,349", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:57:14.349622+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:58:14,443", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:58:14.446351+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 18:59:14,539", "message": "Running periodic anomaly detection.", "time": "2025-08-16T22:59:14.539774+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:00:14,637", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:00:14.637205+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:01:14,729", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:01:14.729875+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:02:14,832", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:02:14.833257+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:03:14,989", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:03:14.989842+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:04:15,081", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:04:15.081858+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:05:15,186", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:05:15.197219+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:06:15,321", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:06:15.329063+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:07:15,439", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:07:15.440061+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:08:15,546", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:08:15.546805+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:09:15,663", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:09:15.663740+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:10:15,770", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:10:15.770553+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:11:15,882", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:11:15.882746+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:12:16,024", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:12:16.024987+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:13:16,139", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:13:16.139246+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:14:16,241", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:14:16.242113+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:15:16,337", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:15:16.337527+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:16:16,456", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:16:16.456771+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:17:16,540", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:17:16.541010+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:18:16,623", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:18:16.624020+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:19:16,709", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:19:16.710333+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:20:16,804", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:20:16.804386+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:21:16,891", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:21:16.892315+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:22:16,979", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:22:16.979468+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:23:17,059", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:23:17.059530+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:24:17,142", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:24:17.142694+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:25:17,224", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:25:17.224799+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:26:17,312", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:26:17.313005+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:27:17,396", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:27:17.396896+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:28:17,481", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:28:17.481373+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:29:17,562", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:29:17.562334+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:30:17,644", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:30:17.644847+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:31:17,729", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:31:17.730286+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:32:17,826", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:32:17.826750+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:33:17,917", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:33:17.917930+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:34:18,007", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:34:18.008136+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:35:18,092", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:35:18.092729+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:36:18,170", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:36:18.170521+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:37:18,268", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:37:18.268640+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:38:18,348", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:38:18.348967+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:39:18,432", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:39:18.432847+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:40:18,516", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:40:18.517455+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:41:18,600", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:41:18.601828+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:42:18,684", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:42:18.685218+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:43:18,766", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:43:18.766904+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:44:18,845", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:44:18.846809+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:45:18,930", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:45:18.930340+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:46:19,001", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:46:19.002369+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:47:19,084", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:47:19.084880+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:48:19,171", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:48:19.171980+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:49:19,253", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:49:19.254132+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:50:19,333", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:50:19.333994+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:51:19,426", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:51:19.426850+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:52:19,509", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:52:19.510266+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:53:19,588", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:53:19.589147+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:54:19,669", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:54:19.669501+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:55:19,753", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:55:19.753821+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:56:19,835", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:56:19.836121+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:57:19,917", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:57:19.917933+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:58:20,005", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:58:20.005928+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 19:59:20,095", "message": "Running periodic anomaly detection.", "time": "2025-08-16T23:59:20.096108+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:00:20,180", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:00:20.181325+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:01:20,263", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:01:20.263880+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:02:20,345", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:02:20.345737+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:03:20,425", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:03:20.426001+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:04:20,502", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:04:20.502375+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:05:20,614", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:05:20.614788+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:06:20,698", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:06:20.699025+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:07:20,777", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:07:20.777928+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:08:20,873", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:08:20.874349+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:09:20,957", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:09:20.957675+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:10:21,047", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:10:21.047829+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:11:21,142", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:11:21.142766+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:12:21,237", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:12:21.237579+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:13:21,327", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:13:21.327544+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:14:21,410", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:14:21.410903+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:15:21,490", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:15:21.490889+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:16:21,574", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:16:21.584124+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:17:21,667", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:17:21.667936+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:18:21,760", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:18:21.761077+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:19:21,841", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:19:21.842189+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:20:21,933", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:20:21.934351+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:21:22,021", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:21:22.022030+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:22:22,113", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:22:22.114221+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:23:22,195", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:23:22.196063+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:24:22,274", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:24:22.274692+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:25:22,364", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:25:22.364716+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:26:22,457", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:26:22.457436+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:27:22,541", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:27:22.542042+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:28:22,640", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:28:22.641068+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:29:22,734", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:29:22.734562+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:30:22,815", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:30:22.815771+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:31:22,894", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:31:22.894439+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:32:22,970", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:32:22.970684+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:33:23,054", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:33:23.054605+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:34:23,135", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:34:23.135433+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:35:23,215", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:35:23.215899+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:36:23,299", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:36:23.299812+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:37:23,386", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:37:23.386611+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:38:23,470", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:38:23.470794+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:39:23,553", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:39:23.553721+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:40:23,628", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:40:23.629454+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:41:23,723", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:41:23.723388+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:42:23,807", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:42:23.807890+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:43:23,900", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:43:23.900360+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:44:23,985", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:44:23.986022+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:45:24,062", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:45:24.063294+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:46:24,145", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:46:24.146103+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:47:24,231", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:47:24.232543+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:48:24,322", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:48:24.322862+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:49:24,425", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:49:24.425601+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:50:24,507", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:50:24.508806+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:51:24,592", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:51:24.592769+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:52:24,681", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:52:24.681337+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:53:24,774", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:53:24.775081+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:54:24,854", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:54:24.854738+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:55:24,934", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:55:24.935728+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:56:25,017", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:56:25.017910+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:57:25,103", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:57:25.103846+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:58:25,184", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:58:25.185126+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 20:59:25,267", "message": "Running periodic anomaly detection.", "time": "2025-08-17T00:59:25.268203+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:00:25,353", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:00:25.353953+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:01:25,434", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:01:25.434437+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:02:25,511", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:02:25.512151+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:03:25,595", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:03:25.596269+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:04:25,682", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:04:25.683218+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:05:25,766", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:05:25.767527+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:06:25,849", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:06:25.850553+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:07:25,932", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:07:25.932900+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:08:26,028", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:08:26.028950+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:09:26,115", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:09:26.116232+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:10:26,196", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:10:26.196830+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:11:26,286", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:11:26.287037+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:12:26,368", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:12:26.369037+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:13:26,452", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:13:26.452996+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:14:26,535", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:14:26.536110+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:15:26,619", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:15:26.620082+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:16:26,711", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:16:26.711803+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:17:26,795", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:17:26.796105+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:18:26,881", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:18:26.882499+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:19:26,983", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:19:26.984100+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:20:27,072", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:20:27.073095+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:21:27,155", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:21:27.156031+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:22:27,242", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:22:27.242644+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:23:27,326", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:23:27.326817+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:24:27,424", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:24:27.424681+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:25:27,513", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:25:27.513785+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:26:27,606", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:26:27.607254+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:27:27,692", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:27:27.692981+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:28:27,774", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:28:27.775135+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:29:27,854", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:29:27.855007+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:30:27,948", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:30:27.948652+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:31:28,040", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:31:28.041044+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:32:28,125", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:32:28.125510+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:33:28,215", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:33:28.216137+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:34:28,295", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:34:28.296189+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:35:28,389", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:35:28.390273+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:36:28,479", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:36:28.479982+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:37:28,559", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:37:28.560241+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:38:28,642", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:38:28.642397+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:39:28,731", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:39:28.732075+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:40:28,808", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:40:28.809481+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:41:28,901", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:41:28.901935+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:42:28,978", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:42:28.979106+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:43:29,075", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:43:29.075806+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:44:29,164", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:44:29.165133+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:45:29,256", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:45:29.257217+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:46:29,340", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:46:29.341221+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:47:29,426", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:47:29.427351+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:48:29,509", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:48:29.510202+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:49:29,601", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:49:29.601776+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:50:29,686", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:50:29.686738+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:51:29,769", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:51:29.769715+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:52:29,857", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:52:29.857600+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:53:29,940", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:53:29.941488+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:54:30,022", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:54:30.022912+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:55:30,107", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:55:30.108256+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:56:30,192", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:56:30.193204+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:57:30,280", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:57:30.280365+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:58:30,368", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:58:30.369207+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 21:59:30,447", "message": "Running periodic anomaly detection.", "time": "2025-08-17T01:59:30.447452+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:00:30,529", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:00:30.529801+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:01:30,605", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:01:30.605623+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:02:30,697", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:02:30.697557+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:03:30,782", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:03:30.783287+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:04:30,864", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:04:30.865225+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:05:30,948", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:05:30.949221+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:06:31,046", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:06:31.046987+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:07:31,130", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:07:31.131308+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:08:31,215", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:08:31.216291+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:09:31,298", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:09:31.298673+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:10:31,377", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:10:31.379002+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:11:31,475", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:11:31.475378+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:12:31,556", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:12:31.556711+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:13:31,629", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:13:31.629807+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:14:31,711", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:14:31.712280+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:15:31,795", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:15:31.795983+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:16:31,868", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:16:31.868731+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:17:31,954", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:17:31.955403+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:18:32,029", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:18:32.029591+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:19:32,113", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:19:32.113463+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:20:32,193", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:20:32.194114+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:21:32,272", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:21:32.273710+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:22:32,364", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:22:32.365262+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:23:32,445", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:23:32.446101+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:24:32,522", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:24:32.522551+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:25:32,605", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:25:32.605579+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:26:32,688", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:26:32.688686+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:27:32,770", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:27:32.770890+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:28:32,849", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:28:32.850069+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:29:32,926", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:29:32.926864+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:30:33,009", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:30:33.010417+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:31:33,093", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:31:33.093505+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:32:33,167", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:32:33.168030+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:33:33,251", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:33:33.252002+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:34:33,337", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:34:33.337746+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:35:33,426", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:35:33.427133+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:36:33,504", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:36:33.504979+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:37:33,588", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:37:33.588523+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:38:33,677", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:38:33.677444+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:39:33,767", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:39:33.768097+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:40:33,844", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:40:33.844442+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:41:33,928", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:41:33.929018+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:42:34,015", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:42:34.015607+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:43:34,099", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:43:34.100057+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:44:34,182", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:44:34.183249+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:45:34,263", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:45:34.263514+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:46:34,342", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:46:34.342697+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:47:34,433", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:47:34.434221+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:48:34,522", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:48:34.523026+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:49:34,614", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:49:34.614377+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:50:34,686", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:50:34.686748+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:51:34,775", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:51:34.775921+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:52:34,857", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:52:34.857724+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:53:34,939", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:53:34.939523+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:54:35,037", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:54:35.037884+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:55:35,120", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:55:35.121235+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:56:35,205", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:56:35.205954+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:57:35,286", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:57:35.287311+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:58:35,376", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:58:35.377013+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 22:59:35,465", "message": "Running periodic anomaly detection.", "time": "2025-08-17T02:59:35.465761+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:00:35,536", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:00:35.536649+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:01:35,619", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:01:35.620164+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:02:35,694", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:02:35.694968+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:03:35,770", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:03:35.770919+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:04:35,852", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:04:35.852475+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:05:35,939", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:05:35.939710+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:06:36,028", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:06:36.028779+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:07:36,112", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:07:36.112810+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:08:36,194", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:08:36.195366+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:09:36,275", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:09:36.276537+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:10:36,364", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:10:36.364921+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:11:36,435", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:11:36.436384+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:12:36,520", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:12:36.520841+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:13:36,599", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:13:36.600032+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:14:36,688", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:14:36.688405+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:15:36,775", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:15:36.776497+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:16:36,858", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:16:36.858419+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:17:36,930", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:17:36.930902+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:18:37,017", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:18:37.017782+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:19:37,094", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:19:37.095276+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:20:37,177", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:20:37.177862+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:21:37,257", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:21:37.257636+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:22:37,346", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:22:37.347359+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:23:37,431", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:23:37.431942+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:24:37,520", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:24:37.520682+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:25:37,605", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:25:37.606218+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:26:37,693", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:26:37.693468+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:27:37,775", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:27:37.775738+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:28:37,859", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:28:37.859915+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:29:37,942", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:29:37.942792+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:30:38,022", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:30:38.023156+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:31:38,103", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:31:38.104374+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:32:38,234", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:32:38.234663+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:33:38,318", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:33:38.318674+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:34:38,399", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:34:38.399758+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:35:38,479", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:35:38.480135+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:36:38,547", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:36:38.548199+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:37:38,632", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:37:38.633383+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:38:38,723", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:38:38.723420+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:39:38,812", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:39:38.813039+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:40:38,887", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:40:38.887297+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:41:38,967", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:41:38.968053+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:42:39,061", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:42:39.062085+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:43:39,143", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:43:39.143969+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:44:39,227", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:44:39.227911+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:45:39,325", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:45:39.325879+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:46:39,399", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:46:39.399933+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:47:39,481", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:47:39.482364+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:48:39,568", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:48:39.568491+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:49:39,661", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:49:39.661993+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:50:39,750", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:50:39.751469+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:51:39,821", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:51:39.821893+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:52:39,962", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:52:39.963090+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:53:40,039", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:53:40.039878+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:54:40,123", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:54:40.124087+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:55:40,210", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:55:40.210324+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:56:40,298", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:56:40.299293+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:57:40,378", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:57:40.378389+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:58:40,457", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:58:40.458339+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-16 23:59:40,557", "message": "Running periodic anomaly detection.", "time": "2025-08-17T03:59:40.557566+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:00:40,638", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:00:40.639061+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:01:40,731", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:01:40.731757+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:02:40,808", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:02:40.809048+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:03:40,896", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:03:40.896373+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:04:40,991", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:04:40.991693+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:05:41,085", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:05:41.085679+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:06:41,164", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:06:41.165201+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:07:41,305", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:07:41.305978+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:08:41,400", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:08:41.400506+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:09:41,479", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:09:41.489126+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:10:41,845", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:10:41.846310+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:11:41,953", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:11:41.953700+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:12:42,069", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:12:42.131980+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:13:42,497", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:13:42.507348+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:14:42,684", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:14:42.684635+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:15:43,079", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:15:43.081186+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:16:43,180", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:16:43.180550+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:17:44,168", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:17:44.168745+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:18:44,671", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:18:44.672070+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:19:45,133", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:19:45.134068+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:20:45,403", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:20:45.404523+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:21:46,002", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:21:46.003039+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:22:47,445", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:22:47.445771+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:23:47,532", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:23:47.532589+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:24:47,865", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:24:47.865659+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:25:47,973", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:25:47.974743+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:26:48,070", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:26:48.070910+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:27:48,914", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:27:48.914656+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:28:49,929", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:28:49.929543+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:29:50,420", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:29:50.420760+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:30:51,156", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:30:51.157474+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:31:51,763", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:31:51.763757+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:32:52,716", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:32:52.716910+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:33:53,360", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:33:53.360488+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:34:55,499", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:34:55.499317+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:35:55,703", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:35:55.704346+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:36:56,431", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:36:56.451100+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:37:57,237", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:37:57.237810+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:38:58,266", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:38:58.266946+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:39:58,385", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:39:58.385727+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:41:00,180", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:41:00.182220+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:42:00,749", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:42:00.750372+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:43:00,938", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:43:00.938383+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:44:01,598", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:44:01.599496+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:45:03,191", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:45:03.191256+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:46:03,382", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:46:03.391936+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:47:03,574", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:47:03.589675+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:48:04,571", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:48:04.573043+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:49:04,990", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:49:05.017048+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:50:05,248", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:50:05.257978+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:51:05,431", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:51:05.432574+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:52:05,724", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:52:05.743327+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:53:05,921", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:53:05.922222+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:54:06,114", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:54:06.114750+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:55:06,210", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:55:06.210956+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:56:08,271", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:56:08.271418+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:57:08,388", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:57:08.388952+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:58:08,994", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:58:08.994873+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 00:59:09,998", "message": "Running periodic anomaly detection.", "time": "2025-08-17T04:59:09.999141+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:00:10,087", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:00:10.088031+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:01:10,187", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:01:10.188671+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:02:10,270", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:02:10.270390+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:03:10,350", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:03:10.350741+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:04:10,444", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:04:10.444261+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:05:11,823", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:05:11.823284+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:06:12,133", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:06:12.133885+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:07:12,230", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:07:12.239971+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:08:12,325", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:08:12.325989+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:09:13,183", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:09:13.184151+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:10:13,727", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:10:13.728132+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:11:14,240", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:11:14.240948+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:12:14,828", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:12:14.828468+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:13:15,457", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:13:15.457496+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:14:16,271", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:14:16.271974+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:15:16,962", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:15:16.963227+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:16:17,629", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:16:17.630172+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:17:18,354", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:17:18.354334+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:18:18,889", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:18:18.889896+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:19:19,551", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:19:19.551804+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:20:19,768", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:20:19.778044+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:21:19,934", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:21:19.935667+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:22:20,030", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:22:20.030681+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:23:20,123", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:23:20.124075+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:24:20,214", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:24:20.214590+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:25:20,328", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:25:20.329510+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:26:20,432", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:26:20.433123+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:27:20,522", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:27:20.522534+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:28:20,607", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:28:20.608008+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:29:20,701", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:29:20.701404+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:30:20,777", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:30:20.778039+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:31:20,882", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:31:20.882830+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:32:20,968", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:32:20.969142+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:33:21,061", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:33:21.062116+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:34:21,149", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:34:21.149616+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:35:21,343", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:35:21.343396+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:36:21,432", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:36:21.433234+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:37:21,526", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:37:21.526607+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:38:21,655", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:38:21.655992+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:39:21,754", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:39:21.754809+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:40:21,878", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:40:21.879186+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:41:22,045", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:41:22.045357+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:42:22,177", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:42:22.177535+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:43:22,263", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:43:22.263789+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:44:22,353", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:44:22.353779+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:45:23,569", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:45:23.569964+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:46:23,801", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:46:23.802041+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:47:23,892", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:47:23.892266+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:48:24,197", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:48:24.199218+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:49:24,321", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:49:24.329878+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:50:24,493", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:50:24.494175+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:51:25,603", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:51:25.603919+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:52:26,930", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:52:26.930946+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:53:27,453", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:53:27.453388+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:54:28,887", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:54:28.888029+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:55:30,004", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:55:30.004646+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:56:30,594", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:56:30.594419+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:57:31,584", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:57:31.585007+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:58:31,685", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:58:31.685997+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 01:59:31,761", "message": "Running periodic anomaly detection.", "time": "2025-08-17T05:59:31.770644+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:00:33,611", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:00:33.612073+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:01:36,441", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:01:36.441552+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:02:38,463", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:02:38.464051+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:03:40,031", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:03:40.032045+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:04:42,596", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:04:42.597114+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:05:44,901", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:05:44.901975+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:06:45,600", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:06:45.610272+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:07:46,877", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:07:46.877746+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:08:47,736", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:08:47.736720+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:09:49,368", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:09:49.368323+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:10:49,917", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:10:49.918070+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:11:50,253", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:11:50.253580+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:12:50,626", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:12:50.626463+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:13:50,946", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:13:50.947211+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:14:51,024", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:14:51.024909+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:15:51,103", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:15:51.104113+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:16:51,264", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:16:51.264433+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:17:51,336", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:17:51.336809+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:18:51,403", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:18:51.404354+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:19:51,479", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:19:51.479859+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:20:51,569", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:20:51.570190+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:21:51,662", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:21:51.663345+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:22:51,786", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:22:51.786812+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:23:51,887", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:23:51.888241+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:24:51,974", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:24:51.975195+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:25:52,060", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:25:52.061013+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:26:52,153", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:26:52.154057+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:27:52,226", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:27:52.227491+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:28:52,317", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:28:52.317558+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:29:52,420", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:29:52.421178+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:30:52,523", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:30:52.523766+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:31:52,604", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:31:52.605118+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:32:52,693", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:32:52.694085+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:33:52,785", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:33:52.785883+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:34:52,868", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:34:52.869728+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:35:52,963", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:35:52.964315+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:36:53,049", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:36:53.049569+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:37:53,156", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:37:53.157709+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:38:53,255", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:38:53.255368+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:39:53,345", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:39:53.346050+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:40:53,431", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:40:53.431908+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:41:53,521", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:41:53.521924+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:42:53,600", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:42:53.601788+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:43:53,723", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:43:53.724226+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:44:53,801", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:44:53.802233+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:45:53,894", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:45:53.894885+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:46:53,991", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:46:53.992893+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:47:54,094", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:47:54.095006+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:48:54,175", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:48:54.176249+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:49:54,263", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:49:54.264185+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:50:54,352", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:50:54.352587+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:51:54,441", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:51:54.441416+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:52:54,536", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:52:54.537795+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:53:54,643", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:53:54.644037+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:54:54,732", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:54:54.733346+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:55:54,818", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:55:54.818689+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:56:54,909", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:56:54.910068+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:57:55,008", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:57:55.009812+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:58:55,101", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:58:55.101960+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 02:59:55,204", "message": "Running periodic anomaly detection.", "time": "2025-08-17T06:59:55.204931+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:00:55,302", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:00:55.302997+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:01:55,386", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:01:55.387486+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:02:55,481", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:02:55.482193+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:03:55,589", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:03:55.590285+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:04:55,665", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:04:55.665384+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:05:55,757", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:05:55.757694+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:06:55,857", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:06:55.857727+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:07:55,956", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:07:55.957249+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:08:56,043", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:08:56.044040+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:09:56,140", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:09:56.141122+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:10:56,244", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:10:56.245673+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:11:56,355", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:11:56.355968+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:12:56,443", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:12:56.444131+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:13:56,522", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:13:56.523321+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:14:56,624", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:14:56.624316+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:15:56,709", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:15:56.709998+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:16:56,806", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:16:56.806964+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:17:56,944", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:17:56.944523+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:18:57,089", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:18:57.089888+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:19:57,179", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:19:57.179413+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:20:57,297", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:20:57.297948+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:21:57,383", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:21:57.383487+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:22:57,475", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:22:57.477046+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:23:57,559", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:23:57.559837+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:24:57,651", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:24:57.652373+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:25:57,740", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:25:57.741068+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:26:57,819", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:26:57.819812+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:27:57,915", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:27:57.916006+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:28:58,004", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:28:58.005321+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:29:58,106", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:29:58.107130+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:30:58,214", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:30:58.214443+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:31:58,298", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:31:58.298867+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:32:58,386", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:32:58.386806+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:33:58,475", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:33:58.475305+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:34:58,563", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:34:58.563294+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:35:58,678", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:35:58.679138+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:36:58,770", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:36:58.770974+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:37:58,850", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:37:58.850838+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:38:58,947", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:38:58.947707+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:39:59,031", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:39:59.032151+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:40:59,136", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:40:59.137487+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:41:59,222", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:41:59.223360+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:42:59,315", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:42:59.316160+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:43:59,405", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:43:59.406094+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:44:59,487", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:44:59.488026+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:45:59,575", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:45:59.575797+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:46:59,670", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:46:59.670663+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:47:59,754", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:47:59.754575+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:48:59,844", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:48:59.844353+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:49:59,926", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:49:59.926263+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:51:00,018", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:51:00.018162+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:52:00,120", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:52:00.122205+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:53:00,209", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:53:00.210313+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:54:00,302", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:54:00.302873+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:55:00,392", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:55:00.393130+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:56:00,475", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:56:00.475818+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:57:00,561", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:57:00.561163+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:58:00,645", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:58:00.645706+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 03:59:00,743", "message": "Running periodic anomaly detection.", "time": "2025-08-17T07:59:00.744487+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:00:00,845", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:00:00.845994+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:01:01,068", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:01:01.068999+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:02:01,149", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:02:01.150579+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:03:01,236", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:03:01.237018+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:04:01,400", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:04:01.401276+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:05:01,486", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:05:01.487207+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:06:01,581", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:06:01.581783+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:07:01,670", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:07:01.670881+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:08:01,749", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:08:01.749665+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:09:01,839", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:09:01.839865+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:10:01,918", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:10:01.919146+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:11:02,001", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:11:02.002099+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:12:02,085", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:12:02.085952+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:13:02,165", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:13:02.165870+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:14:02,247", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:14:02.247258+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:15:02,348", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:15:02.349410+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:16:02,438", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:16:02.438610+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:17:02,517", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:17:02.518690+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:18:02,604", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:18:02.605074+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:19:02,689", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:19:02.689618+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:20:02,778", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:20:02.779398+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:21:02,863", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:21:02.864338+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:22:02,949", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:22:02.950394+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:23:03,035", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:23:03.036501+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:24:03,121", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:24:03.121706+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:25:03,209", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:25:03.210346+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:26:03,298", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:26:03.299098+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:27:03,379", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:27:03.380233+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:28:03,469", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:28:03.469587+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:29:03,577", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:29:03.578172+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:30:03,679", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:30:03.679516+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:31:03,773", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:31:03.773809+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:32:03,865", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:32:03.865295+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:33:04,016", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:33:04.016867+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:34:04,115", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:34:04.115882+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:35:04,213", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:35:04.214108+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:36:04,297", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:36:04.298224+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:37:04,393", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:37:04.393637+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:38:04,487", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:38:04.488335+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:39:04,585", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:39:04.585553+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:40:04,680", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:40:04.681100+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:41:04,775", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:41:04.776528+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:42:04,858", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:42:04.860002+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:43:04,939", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:43:04.939275+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:44:05,031", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:44:05.032283+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:45:05,115", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:45:05.115489+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:46:05,198", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:46:05.199352+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:47:05,332", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:47:05.332453+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:48:05,404", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:48:05.405064+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:49:05,498", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:49:05.499003+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:50:05,596", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:50:05.596425+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:51:05,698", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:51:05.698158+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:52:05,783", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:52:05.783638+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:53:05,870", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:53:05.870640+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:54:05,956", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:54:05.956353+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:55:06,042", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:55:06.042589+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:56:06,123", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:56:06.123435+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:57:06,222", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:57:06.223065+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:58:06,311", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:58:06.311964+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 04:59:06,399", "message": "Running periodic anomaly detection.", "time": "2025-08-17T08:59:06.400165+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:00:06,476", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:00:06.476712+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:01:06,579", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:01:06.580160+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:02:06,673", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:02:06.674109+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:03:06,768", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:03:06.768482+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:04:06,857", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:04:06.858196+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:05:06,950", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:05:06.951291+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:06:07,037", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:06:07.038374+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:07:07,126", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:07:07.126509+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:08:07,214", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:08:07.214838+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:09:07,306", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:09:07.306895+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:10:07,391", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:10:07.392477+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:11:07,475", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:11:07.476336+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:12:07,557", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:12:07.558002+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:13:07,645", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:13:07.646317+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:14:07,735", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:14:07.736221+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:15:07,828", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:15:07.829257+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:16:07,925", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:16:07.925414+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:17:08,005", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:17:08.005423+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:18:08,095", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:18:08.095911+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:19:08,163", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:19:08.163778+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:20:08,273", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:20:08.273670+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:21:08,342", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:21:08.342927+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:22:08,428", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:22:08.429619+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:23:08,528", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:23:08.529244+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:24:08,623", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:24:08.624038+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:25:08,710", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:25:08.711338+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:26:08,789", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:26:08.789353+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:27:08,882", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:27:08.882906+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:28:08,969", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:28:08.970320+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:29:09,059", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:29:09.059904+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:30:09,149", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:30:09.149869+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:31:09,220", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:31:09.221046+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:32:09,316", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:32:09.316455+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:33:09,418", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:33:09.418949+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:34:09,507", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:34:09.507979+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:35:09,612", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:35:09.612757+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:36:09,697", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:36:09.698180+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:37:09,781", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:37:09.792014+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:38:09,882", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:38:09.883042+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:39:09,978", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:39:09.979082+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:40:10,073", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:40:10.075557+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:41:10,165", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:41:10.165442+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:42:10,275", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:42:10.276173+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:43:10,379", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:43:10.379766+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:44:10,474", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:44:10.475252+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:45:10,554", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:45:10.554791+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:46:10,651", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:46:10.651916+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:47:10,730", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:47:10.730688+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:48:10,818", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:48:10.819117+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 05:49:10,914", "message": "Running periodic anomaly detection.", "time": "2025-08-17T09:49:10.914541+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:09:54,205", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:09:54.206195+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:10:54,308", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:10:54.328583+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:11:54,560", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:11:54.562490+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:12:54,649", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:12:54.650348+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:13:54,735", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:13:54.736250+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:14:54,821", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:14:54.823130+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:15:54,907", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:15:54.907929+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:16:54,996", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:16:54.997435+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:17:55,072", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:17:55.073478+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:18:55,172", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:18:55.173294+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:19:55,255", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:19:55.256034+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:20:55,370", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:20:55.386375+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:21:55,555", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:21:55.556697+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:22:55,648", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:22:55.648575+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:23:55,729", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:23:55.731123+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:24:55,827", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:24:55.828718+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:25:55,917", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:25:55.918454+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:26:56,022", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:26:56.023510+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:27:56,110", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:27:56.110568+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:28:56,195", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:28:56.196196+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:29:56,284", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:29:56.285449+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:30:56,385", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:30:56.386076+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:31:56,474", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:31:56.474621+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:32:56,560", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:32:56.560961+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:33:56,646", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:33:56.647161+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:34:56,731", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:34:56.732284+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:35:56,820", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:35:56.830025+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:36:56,995", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:36:56.996118+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:37:57,084", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:37:57.085523+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:38:57,175", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:38:57.175607+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:39:57,298", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:39:57.309203+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:40:57,413", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:40:57.413576+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:41:57,505", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:41:57.516011+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:42:57,597", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:42:57.597898+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:43:57,693", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:43:57.694550+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:44:57,788", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:44:57.788724+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:45:57,888", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:45:57.889275+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:46:57,991", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:46:57.992400+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:47:58,090", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:47:58.091011+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:48:58,182", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:48:58.183429+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:49:58,296", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:49:58.297587+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:50:58,389", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:50:58.390245+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:51:58,476", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:51:58.476432+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:52:58,560", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:52:58.560695+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:53:58,655", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:53:58.655731+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:54:58,742", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:54:58.742727+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:55:58,826", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:55:58.827257+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:56:58,909", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:56:58.909413+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:57:58,993", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:57:58.994165+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:58:59,079", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:58:59.079657+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 07:59:59,167", "message": "Running periodic anomaly detection.", "time": "2025-08-17T11:59:59.168561+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 08:00:59,265", "message": "Running periodic anomaly detection.", "time": "2025-08-17T12:00:59.265950+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 08:01:59,376", "message": "Running periodic anomaly detection.", "time": "2025-08-17T12:01:59.376590+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 08:02:59,462", "message": "Running periodic anomaly detection.", "time": "2025-08-17T12:02:59.462903+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 08:03:59,554", "message": "Running periodic anomaly detection.", "time": "2025-08-17T12:03:59.555054+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 08:04:59,727", "message": "Running periodic anomaly detection.", "time": "2025-08-17T12:04:59.727911+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 08:05:59,840", "message": "Running periodic anomaly detection.", "time": "2025-08-17T12:05:59.919774+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 08:07:01,271", "message": "Running periodic anomaly detection.", "time": "2025-08-17T12:07:01.381037+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 08:08:03,299", "message": "Running periodic anomaly detection.", "time": "2025-08-17T12:08:03.327955+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 08:09:03,706", "message": "Running periodic anomaly detection.", "time": "2025-08-17T12:09:03.707424+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 08:10:03,834", "message": "Running periodic anomaly detection.", "time": "2025-08-17T12:10:03.835929+00:00"}
{"name": "system_events", "levelname": "INFO", "pathname": "/mnt/d/offline_sentinel/backend/run.py", "lineno": 43, "timestamp": "2025-08-17 08:11:03,977", "message": "Running periodic anomaly detection.", "time": "2025-08-17T12:11:03.977671+00:00"}
{"timestamp": "2025-09-11T09:03:10.749818", "name": "system_events", "levelname": "INFO", "message": "Frontend dashboard loaded.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 55, "msg": "Frontend dashboard loaded.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "index", "created": 1757595790.7498183, "msecs": 749.0, "relativeCreated": 1099.9126434326172, "thread": 137276844023936, "threadName": "MainThread", "processName": "MainProcess", "process": 73425, "taskName": null}
{"timestamp": "2025-09-11T10:39:31.160599", "name": "system_events", "levelname": "INFO", "message": "Backend server starting with WebSocket support.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 67, "msg": "Backend server starting with WebSocket support.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "<module>", "created": 1757601571.1605995, "msecs": 160.0, "relativeCreated": 911.9830131530762, "thread": 134409552105600, "threadName": "MainThread", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:39:31.161088", "name": "system_events", "levelname": "INFO", "message": "Initial training of anomaly detection model...", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 41, "msg": "Initial training of anomaly detection model...", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757601571.1610882, "msecs": 161.0, "relativeCreated": 912.4717712402344, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:39:31.936655", "name": "system_events", "levelname": "INFO", "message": "Anomaly detection model training complete.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 43, "msg": "Anomaly detection model training complete.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757601571.9366546, "msecs": 936.0, "relativeCreated": 1688.0381107330322, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:39:31.936888", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757601571.9368882, "msecs": 936.0, "relativeCreated": 1688.2717609405518, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:39:32.026162", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757601572.0261617, "msecs": 26.0, "relativeCreated": 1777.5452136993408, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:39:57.518046", "name": "system_events", "levelname": "INFO", "message": "Frontend dashboard loaded.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 55, "msg": "Frontend dashboard loaded.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "index", "created": 1757601597.5180464, "msecs": 518.0, "relativeCreated": 27269.429922103882, "thread": 134408310003392, "threadName": "Thread-2 (process_request_thread)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:40:03.598275", "name": "system_events", "levelname": "INFO", "message": "Frontend dashboard loaded.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 55, "msg": "Frontend dashboard loaded.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "index", "created": 1757601603.5982754, "msecs": 598.0, "relativeCreated": 33349.65896606445, "thread": 134408310003392, "threadName": "Thread-3 (process_request_thread)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:40:32.026479", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757601632.0264785, "msecs": 26.0, "relativeCreated": 61777.86207199097, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:40:32.104668", "name": "system_events", "levelname": "WARNING", "message": "Detected 3 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 3 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757601632.1046681, "msecs": 104.0, "relativeCreated": 61856.0516834259, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:41:32.104996", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757601692.1049962, "msecs": 104.0, "relativeCreated": 121856.37974739075, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:41:32.182299", "name": "system_events", "levelname": "WARNING", "message": "Detected 4 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 4 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757601692.182299, "msecs": 182.0, "relativeCreated": 121933.68244171143, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:42:32.182637", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757601752.182637, "msecs": 182.0, "relativeCreated": 181934.0205192566, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:42:32.261654", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757601752.2616537, "msecs": 261.0, "relativeCreated": 182013.03720474243, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:43:32.261985", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757601812.261985, "msecs": 261.0, "relativeCreated": 242013.36860656738, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:43:32.341042", "name": "system_events", "levelname": "WARNING", "message": "Detected 6 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 6 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757601812.3410418, "msecs": 341.0, "relativeCreated": 242092.4253463745, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:44:32.341482", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757601872.3414822, "msecs": 341.0, "relativeCreated": 302092.8657054901, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:44:32.423833", "name": "system_events", "levelname": "WARNING", "message": "Detected 7 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 7 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757601872.4238327, "msecs": 423.0, "relativeCreated": 302175.21619796753, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:45:32.424179", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757601932.4241786, "msecs": 424.0, "relativeCreated": 362175.5621433258, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:45:32.502029", "name": "system_events", "levelname": "WARNING", "message": "Detected 8 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 8 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757601932.5020287, "msecs": 502.0, "relativeCreated": 362253.4122467041, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:46:32.502367", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757601992.5023668, "msecs": 502.0, "relativeCreated": 422253.75032424927, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:46:32.578107", "name": "system_events", "levelname": "WARNING", "message": "Detected 9 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 9 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757601992.5781069, "msecs": 578.0, "relativeCreated": 422329.4904232025, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:47:32.578411", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602052.5784113, "msecs": 578.0, "relativeCreated": 482329.794883728, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:47:32.655964", "name": "system_events", "levelname": "WARNING", "message": "Detected 10 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 10 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602052.6559637, "msecs": 655.0, "relativeCreated": 482407.347202301, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:48:32.656307", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602112.6563075, "msecs": 656.0, "relativeCreated": 542407.6910018921, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:48:32.938251", "name": "system_events", "levelname": "WARNING", "message": "Detected 11 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 11 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602112.9382515, "msecs": 938.0, "relativeCreated": 542689.6350383759, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:49:32.938578", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602172.9385784, "msecs": 938.0, "relativeCreated": 602689.9619102478, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:49:33.653632", "name": "system_events", "levelname": "WARNING", "message": "Detected 12 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 12 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602173.653632, "msecs": 653.0, "relativeCreated": 603405.0154685974, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:50:33.653999", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602233.6539986, "msecs": 653.0, "relativeCreated": 663405.3821563721, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:50:34.300354", "name": "system_events", "levelname": "WARNING", "message": "Detected 13 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 13 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602234.3003535, "msecs": 300.0, "relativeCreated": 664051.7370700836, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:51:34.300748", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602294.3007476, "msecs": 300.0, "relativeCreated": 724052.1311759949, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:51:34.410450", "name": "system_events", "levelname": "WARNING", "message": "Detected 14 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 14 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602294.4104505, "msecs": 410.0, "relativeCreated": 724161.8340015411, "thread": 134408319444672, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 156862, "taskName": null}
{"timestamp": "2025-09-11T10:51:52.512091", "name": "system_events", "levelname": "INFO", "message": "Backend server starting with WebSocket support.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 67, "msg": "Backend server starting with WebSocket support.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "<module>", "created": 1757602312.5120907, "msecs": 512.0, "relativeCreated": 2514.0583515167236, "thread": 134242221777024, "threadName": "MainThread", "processName": "MainProcess", "process": 174102, "taskName": null}
{"timestamp": "2025-09-11T10:51:52.513156", "name": "system_events", "levelname": "INFO", "message": "Initial training of anomaly detection model...", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 41, "msg": "Initial training of anomaly detection model...", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602312.5131557, "msecs": 513.0, "relativeCreated": 2515.1233673095703, "thread": 134240990156480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 174102, "taskName": null}
{"timestamp": "2025-09-11T10:51:53.734254", "name": "system_events", "levelname": "INFO", "message": "Anomaly detection model training complete.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 43, "msg": "Anomaly detection model training complete.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602313.7342536, "msecs": 734.0, "relativeCreated": 3736.2213134765625, "thread": 134240990156480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 174102, "taskName": null}
{"timestamp": "2025-09-11T10:51:53.734704", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602313.7347045, "msecs": 734.0, "relativeCreated": 3736.6721630096436, "thread": 134240990156480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 174102, "taskName": null}
{"timestamp": "2025-09-11T10:51:53.829205", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602313.8292053, "msecs": 829.0, "relativeCreated": 3831.1729431152344, "thread": 134240990156480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 174102, "taskName": null}
{"timestamp": "2025-09-11T10:52:53.829548", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602373.8295484, "msecs": 829.0, "relativeCreated": 63831.51602745056, "thread": 134240990156480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 174102, "taskName": null}
{"timestamp": "2025-09-11T10:52:53.907265", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602373.9072654, "msecs": 907.0, "relativeCreated": 63909.23309326172, "thread": 134240990156480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 174102, "taskName": null}
{"timestamp": "2025-09-11T10:53:02.560869", "name": "system_events", "levelname": "INFO", "message": "Frontend dashboard loaded.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 55, "msg": "Frontend dashboard loaded.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "index", "created": 1757602382.5608685, "msecs": 560.0, "relativeCreated": 72562.83617019653, "thread": 134240979666624, "threadName": "Thread-3 (process_request_thread)", "processName": "MainProcess", "process": 174102, "taskName": null}
{"timestamp": "2025-09-11T10:53:53.907582", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602433.907582, "msecs": 907.0, "relativeCreated": 123909.54971313477, "thread": 134240990156480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 174102, "taskName": null}
{"timestamp": "2025-09-11T10:53:53.983987", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602433.9839866, "msecs": 983.0, "relativeCreated": 123985.95428466797, "thread": 134240990156480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 174102, "taskName": null}
{"timestamp": "2025-09-11T10:54:12.589251", "name": "system_events", "levelname": "INFO", "message": "Backend server starting with WebSocket support.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 67, "msg": "Backend server starting with WebSocket support.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "<module>", "created": 1757602452.5892506, "msecs": 589.0, "relativeCreated": 856.4949035644531, "thread": 128248827875456, "threadName": "MainThread", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T10:54:12.589698", "name": "system_events", "levelname": "INFO", "message": "Initial training of anomaly detection model...", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 41, "msg": "Initial training of anomaly detection model...", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602452.5896978, "msecs": 589.0, "relativeCreated": 856.9421768188477, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T10:54:13.384442", "name": "system_events", "levelname": "INFO", "message": "Anomaly detection model training complete.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 43, "msg": "Anomaly detection model training complete.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602453.3844423, "msecs": 384.0, "relativeCreated": 1651.686668395996, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T10:54:13.384675", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602453.384675, "msecs": 384.0, "relativeCreated": 1651.9193649291992, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T10:54:13.470521", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602453.470521, "msecs": 470.0, "relativeCreated": 1737.7653121948242, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T10:55:13.470830", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602513.4708304, "msecs": 470.0, "relativeCreated": 61738.0747795105, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T10:55:13.548863", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602513.5488634, "msecs": 548.0, "relativeCreated": 61816.107749938965, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T10:56:13.549235", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602573.5492346, "msecs": 549.0, "relativeCreated": 121816.47896766663, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T10:56:13.625674", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602573.6256735, "msecs": 625.0, "relativeCreated": 121892.91787147522, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T10:56:31.116592", "name": "system_events", "levelname": "INFO", "message": "Frontend dashboard loaded.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 55, "msg": "Frontend dashboard loaded.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "index", "created": 1757602591.1165922, "msecs": 116.0, "relativeCreated": 139383.83650779724, "thread": 128247585752768, "threadName": "Thread-5 (process_request_thread)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T10:57:13.626007", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602633.6260066, "msecs": 626.0, "relativeCreated": 181893.25094223022, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T10:57:13.700884", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602633.7008843, "msecs": 700.0, "relativeCreated": 181968.12868118286, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T10:58:13.701222", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602693.7012217, "msecs": 701.0, "relativeCreated": 241968.4660434723, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T10:58:13.774452", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602693.7744517, "msecs": 774.0, "relativeCreated": 242041.69607162476, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T10:59:13.774786", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602753.774786, "msecs": 774.0, "relativeCreated": 302042.03033447266, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T10:59:13.849561", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602753.8495615, "msecs": 849.0, "relativeCreated": 302116.80579185486, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:00:13.849948", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602813.849948, "msecs": 849.0, "relativeCreated": 362117.1922683716, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:00:13.927163", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602813.9271631, "msecs": 927.0, "relativeCreated": 362194.40746307373, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:01:13.927492", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602873.9274921, "msecs": 927.0, "relativeCreated": 422194.7364807129, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:01:14.003269", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602874.0032687, "msecs": 3.0, "relativeCreated": 422270.51305770874, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:02:14.003591", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602934.003591, "msecs": 3.0, "relativeCreated": 482270.8353996277, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:02:14.076885", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602934.0768847, "msecs": 76.0, "relativeCreated": 482344.1290855408, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:03:14.077239", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602994.077239, "msecs": 77.0, "relativeCreated": 542344.4833755493, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:03:14.159963", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757602994.1599627, "msecs": 159.0, "relativeCreated": 542427.206993103, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:04:14.160414", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603054.1604135, "msecs": 160.0, "relativeCreated": 602427.6578426361, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:04:14.233628", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603054.2336276, "msecs": 233.0, "relativeCreated": 602500.8718967438, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:05:14.233948", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603114.2339485, "msecs": 233.0, "relativeCreated": 662501.1928081512, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:05:14.307700", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603114.3077002, "msecs": 307.0, "relativeCreated": 662574.9444961548, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:06:14.308007", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603174.3080075, "msecs": 308.0, "relativeCreated": 722575.2518177032, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:06:14.380449", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603174.3804493, "msecs": 380.0, "relativeCreated": 722647.6936340332, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:07:14.380761", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603234.3807614, "msecs": 380.0, "relativeCreated": 782648.0057239532, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:07:14.456005", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603234.456005, "msecs": 456.0, "relativeCreated": 782723.2494354248, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:08:14.456335", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603294.4563348, "msecs": 456.0, "relativeCreated": 842723.5791683197, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:08:14.529679", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603294.5296786, "msecs": 529.0, "relativeCreated": 842796.9229221344, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:09:14.529996", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603354.529996, "msecs": 529.0, "relativeCreated": 902797.2402572632, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:09:14.605131", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603354.6051311, "msecs": 605.0, "relativeCreated": 902872.3754882812, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:10:14.605439", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603414.6054387, "msecs": 605.0, "relativeCreated": 962872.6830482483, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:10:14.680035", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603414.6800354, "msecs": 680.0, "relativeCreated": 962947.2796916962, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:11:14.680342", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603474.6803417, "msecs": 680.0, "relativeCreated": 1022947.5860595703, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:11:14.754575", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603474.7545745, "msecs": 754.0, "relativeCreated": 1023021.8188762665, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:12:14.754888", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603534.7548883, "msecs": 754.0, "relativeCreated": 1083022.1326351166, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:12:14.828237", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603534.8282373, "msecs": 828.0, "relativeCreated": 1083095.48163414, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:13:14.828542", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603594.8285422, "msecs": 828.0, "relativeCreated": 1143095.7865715027, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:13:14.899529", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603594.8995295, "msecs": 899.0, "relativeCreated": 1143166.7737960815, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:14:14.899816", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603654.8998158, "msecs": 899.0, "relativeCreated": 1203167.060136795, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:14:14.971426", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603654.9714265, "msecs": 971.0, "relativeCreated": 1203238.6708259583, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:15:14.971731", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603714.971731, "msecs": 971.0, "relativeCreated": 1263238.9752864838, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:15:15.041887", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603715.041887, "msecs": 41.0, "relativeCreated": 1263309.1313838959, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:16:15.042188", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603775.042188, "msecs": 42.0, "relativeCreated": 1323309.4322681427, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:16:15.112939", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603775.1129394, "msecs": 112.0, "relativeCreated": 1323380.1836967468, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:17:15.113270", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603835.1132703, "msecs": 113.0, "relativeCreated": 1383380.5146217346, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:17:15.186581", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603835.1865811, "msecs": 186.0, "relativeCreated": 1383453.8254737854, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:18:15.186896", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603895.1868958, "msecs": 186.0, "relativeCreated": 1443454.1401863098, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:18:15.256435", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603895.256435, "msecs": 256.0, "relativeCreated": 1443523.6792564392, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:19:15.256721", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603955.2567213, "msecs": 256.0, "relativeCreated": 1503523.9655971527, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:19:15.326108", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757603955.3261077, "msecs": 326.0, "relativeCreated": 1503593.3520793915, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:20:15.326409", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604015.326409, "msecs": 326.0, "relativeCreated": 1563593.6534404755, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:20:15.400746", "name": "system_events", "levelname": "WARNING", "message": "Detected 5 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 5 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604015.4007459, "msecs": 400.0, "relativeCreated": 1563667.9902076721, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:21:15.401066", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604075.4010658, "msecs": 401.0, "relativeCreated": 1623668.3101654053, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:21:15.473338", "name": "system_events", "levelname": "WARNING", "message": "Detected 3 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 3 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604075.4733381, "msecs": 473.0, "relativeCreated": 1623740.5824661255, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:22:15.473650", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604135.4736495, "msecs": 473.0, "relativeCreated": 1683740.8938407898, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:22:15.546572", "name": "system_events", "levelname": "WARNING", "message": "Detected 3 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 3 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604135.5465722, "msecs": 546.0, "relativeCreated": 1683813.8165473938, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:23:15.546921", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604195.5469208, "msecs": 546.0, "relativeCreated": 1743814.1651153564, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:23:15.619683", "name": "system_events", "levelname": "WARNING", "message": "Detected 3 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 3 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604195.6196828, "msecs": 619.0, "relativeCreated": 1743886.9271278381, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:24:15.620012", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604255.6200116, "msecs": 620.0, "relativeCreated": 1803887.2559070587, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:24:15.691731", "name": "system_events", "levelname": "WARNING", "message": "Detected 3 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 3 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604255.691731, "msecs": 691.0, "relativeCreated": 1803958.975315094, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:25:15.692048", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604315.6920478, "msecs": 692.0, "relativeCreated": 1863959.2921733856, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:25:15.761912", "name": "system_events", "levelname": "WARNING", "message": "Detected 3 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 3 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604315.761912, "msecs": 761.0, "relativeCreated": 1864029.156446457, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:26:15.762227", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604375.762227, "msecs": 762.0, "relativeCreated": 1924029.4713974, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:26:15.832814", "name": "system_events", "levelname": "WARNING", "message": "Detected 3 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 3 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604375.8328135, "msecs": 832.0, "relativeCreated": 1924100.0578403473, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:27:15.833122", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604435.8331218, "msecs": 833.0, "relativeCreated": 1984100.36611557, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:27:15.905030", "name": "system_events", "levelname": "WARNING", "message": "Detected 3 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 3 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604435.9050295, "msecs": 905.0, "relativeCreated": 1984172.2738742828, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:27:30.971963", "name": "system_events", "levelname": "INFO", "message": "Frontend dashboard loaded.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 55, "msg": "Frontend dashboard loaded.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "index", "created": 1757604450.9719632, "msecs": 971.0, "relativeCreated": 1999239.2075061798, "thread": 128247585752768, "threadName": "Thread-8 (process_request_thread)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:28:15.905351", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604495.9053512, "msecs": 905.0, "relativeCreated": 2044172.595500946, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:28:15.979153", "name": "system_events", "levelname": "WARNING", "message": "Detected 3 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 3 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604495.9791532, "msecs": 979.0, "relativeCreated": 2044246.3974952698, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:29:15.979488", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604555.9794884, "msecs": 979.0, "relativeCreated": 2104246.732711792, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:29:16.049626", "name": "system_events", "levelname": "WARNING", "message": "Detected 3 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 3 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604556.0496256, "msecs": 49.0, "relativeCreated": 2104316.8699741364, "thread": 128247596242624, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 178267, "taskName": null}
{"timestamp": "2025-09-11T11:29:54.928447", "name": "system_events", "levelname": "INFO", "message": "Backend server starting with WebSocket support.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 67, "msg": "Backend server starting with WebSocket support.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "<module>", "created": 1757604594.9284475, "msecs": 928.0, "relativeCreated": 1204.0519714355469, "thread": 134594277081216, "threadName": "MainThread", "processName": "MainProcess", "process": 229322, "taskName": null}
{"timestamp": "2025-09-11T11:29:54.928869", "name": "system_events", "levelname": "INFO", "message": "Initial training of anomaly detection model...", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 41, "msg": "Initial training of anomaly detection model...", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604594.9288695, "msecs": 928.0, "relativeCreated": 1204.4739723205566, "thread": 134593044428480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 229322, "taskName": null}
{"timestamp": "2025-09-11T11:29:55.772745", "name": "system_events", "levelname": "INFO", "message": "Anomaly detection model training complete.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 43, "msg": "Anomaly detection model training complete.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604595.7727447, "msecs": 772.0, "relativeCreated": 2048.349142074585, "thread": 134593044428480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 229322, "taskName": null}
{"timestamp": "2025-09-11T11:29:55.772983", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604595.7729828, "msecs": 772.0, "relativeCreated": 2048.5873222351074, "thread": 134593044428480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 229322, "taskName": null}
{"timestamp": "2025-09-11T11:29:55.843600", "name": "system_events", "levelname": "WARNING", "message": "Detected 4 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 4 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604595.8436, "msecs": 843.0, "relativeCreated": 2119.204521179199, "thread": 134593044428480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 229322, "taskName": null}
{"timestamp": "2025-09-11T11:30:55.843922", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604655.843922, "msecs": 843.0, "relativeCreated": 62119.526386260986, "thread": 134593044428480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 229322, "taskName": null}
{"timestamp": "2025-09-11T11:30:55.916792", "name": "system_events", "levelname": "WARNING", "message": "Detected 3 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 3 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604655.916792, "msecs": 916.0, "relativeCreated": 62192.39640235901, "thread": 134593044428480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 229322, "taskName": null}
{"timestamp": "2025-09-11T11:31:06.049563", "name": "system_events", "levelname": "INFO", "message": "Frontend dashboard loaded.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 55, "msg": "Frontend dashboard loaded.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "index", "created": 1757604666.0495627, "msecs": 49.0, "relativeCreated": 72325.16717910767, "thread": 134593034987200, "threadName": "Thread-9 (process_request_thread)", "processName": "MainProcess", "process": 229322, "taskName": null}
{"timestamp": "2025-09-11T11:31:55.917130", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604715.9171298, "msecs": 917.0, "relativeCreated": 122192.7342414856, "thread": 134593044428480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 229322, "taskName": null}
{"timestamp": "2025-09-11T11:31:55.991606", "name": "system_events", "levelname": "WARNING", "message": "Detected 3 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 3 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604715.9916055, "msecs": 991.0, "relativeCreated": 122267.21000671387, "thread": 134593044428480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 229322, "taskName": null}
{"timestamp": "2025-09-11T11:32:55.991919", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604775.9919188, "msecs": 991.0, "relativeCreated": 182267.5232887268, "thread": 134593044428480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 229322, "taskName": null}
{"timestamp": "2025-09-11T11:32:56.064062", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604776.0640616, "msecs": 64.0, "relativeCreated": 182339.66612815857, "thread": 134593044428480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 229322, "taskName": null}
{"timestamp": "2025-09-11T11:33:56.064377", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604836.064377, "msecs": 64.0, "relativeCreated": 242339.98155593872, "thread": 134593044428480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 229322, "taskName": null}
{"timestamp": "2025-09-11T11:33:56.137668", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604836.1376681, "msecs": 137.0, "relativeCreated": 242413.27261924744, "thread": 134593044428480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 229322, "taskName": null}
{"timestamp": "2025-09-11T11:34:56.138001", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604896.138001, "msecs": 138.0, "relativeCreated": 302413.60545158386, "thread": 134593044428480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 229322, "taskName": null}
{"timestamp": "2025-09-11T11:34:56.210217", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604896.210217, "msecs": 210.0, "relativeCreated": 302485.8214855194, "thread": 134593044428480, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 229322, "taskName": null}
{"timestamp": "2025-09-11T11:35:28.520517", "name": "system_events", "levelname": "INFO", "message": "Backend server starting with WebSocket support.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 67, "msg": "Backend server starting with WebSocket support.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "<module>", "created": 1757604928.5205169, "msecs": 520.0, "relativeCreated": 1014.324426651001, "thread": 125369429024896, "threadName": "MainThread", "processName": "MainProcess", "process": 236294, "taskName": null}
{"timestamp": "2025-09-11T11:35:28.520951", "name": "system_events", "levelname": "INFO", "message": "Initial training of anomaly detection model...", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 41, "msg": "Initial training of anomaly detection model...", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604928.5209513, "msecs": 520.0, "relativeCreated": 1014.758825302124, "thread": 125368196323008, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 236294, "taskName": null}
{"timestamp": "2025-09-11T11:35:29.380329", "name": "system_events", "levelname": "INFO", "message": "Anomaly detection model training complete.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 43, "msg": "Anomaly detection model training complete.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604929.3803291, "msecs": 380.0, "relativeCreated": 1874.1366863250732, "thread": 125368196323008, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 236294, "taskName": null}
{"timestamp": "2025-09-11T11:35:29.380559", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604929.3805594, "msecs": 380.0, "relativeCreated": 1874.3669986724854, "thread": 125368196323008, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 236294, "taskName": null}
{"timestamp": "2025-09-11T11:35:56.094896", "name": "system_events", "levelname": "INFO", "message": "Frontend dashboard loaded.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 55, "msg": "Frontend dashboard loaded.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "index", "created": 1757604956.0948958, "msecs": 94.0, "relativeCreated": 28588.703393936157, "thread": 125368186881728, "threadName": "Thread-3 (process_request_thread)", "processName": "MainProcess", "process": 236294, "taskName": null}
{"timestamp": "2025-09-11T11:36:29.453990", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757604989.4539902, "msecs": 453.0, "relativeCreated": 61947.797775268555, "thread": 125368196323008, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 236294, "taskName": null}
{"timestamp": "2025-09-11T11:37:29.527575", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757605049.5275745, "msecs": 527.0, "relativeCreated": 122021.38209342957, "thread": 125368196323008, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 236294, "taskName": null}
{"timestamp": "2025-09-11T11:38:29.600381", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757605109.600381, "msecs": 600.0, "relativeCreated": 182094.18845176697, "thread": 125368196323008, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 236294, "taskName": null}
{"timestamp": "2025-09-11T11:39:29.672225", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757605169.6722255, "msecs": 672.0, "relativeCreated": 242166.03302955627, "thread": 125368196323008, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 236294, "taskName": null}
{"timestamp": "2025-09-11T11:40:29.743631", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757605229.743631, "msecs": 743.0, "relativeCreated": 302237.4384403229, "thread": 125368196323008, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 236294, "taskName": null}
{"timestamp": "2025-09-11T11:41:29.815580", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757605289.8155797, "msecs": 815.0, "relativeCreated": 362309.38720703125, "thread": 125368196323008, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 236294, "taskName": null}
{"timestamp": "2025-09-11T11:42:37.826012", "name": "system_events", "levelname": "INFO", "message": "Backend server starting with WebSocket support.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 67, "msg": "Backend server starting with WebSocket support.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "<module>", "created": 1757605357.8260124, "msecs": 826.0, "relativeCreated": 1183.070182800293, "thread": 134140490899584, "threadName": "MainThread", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:42:37.826467", "name": "system_events", "levelname": "INFO", "message": "Initial training of anomaly detection model...", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 41, "msg": "Initial training of anomaly detection model...", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757605357.826467, "msecs": 826.0, "relativeCreated": 1183.5248470306396, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:42:38.706032", "name": "system_events", "levelname": "INFO", "message": "Anomaly detection model training complete.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 43, "msg": "Anomaly detection model training complete.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757605358.7060318, "msecs": 706.0, "relativeCreated": 2063.089609146118, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:42:38.706269", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757605358.706269, "msecs": 706.0, "relativeCreated": 2063.326835632324, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:43:26.029747", "name": "system_events", "levelname": "INFO", "message": "Frontend dashboard loaded.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 55, "msg": "Frontend dashboard loaded.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "index", "created": 1757605406.0297468, "msecs": 29.0, "relativeCreated": 49386.80458068848, "thread": 134139248789184, "threadName": "Thread-5 (process_request_thread)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:43:38.778393", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757605418.7783933, "msecs": 778.0, "relativeCreated": 62135.45107841492, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:44:38.862692", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757605478.8626916, "msecs": 862.0, "relativeCreated": 122219.7494506836, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:45:38.937585", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757605538.9375849, "msecs": 937.0, "relativeCreated": 182294.64268684387, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:46:39.014135", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757605599.0141354, "msecs": 14.0, "relativeCreated": 242371.19317054749, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:47:39.084964", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757605659.0849643, "msecs": 84.0, "relativeCreated": 302442.0220851898, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:48:39.157029", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757605719.157029, "msecs": 157.0, "relativeCreated": 362514.08672332764, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:49:39.232327", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757605779.2323267, "msecs": 232.0, "relativeCreated": 422589.38455581665, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:50:39.307810", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757605839.30781, "msecs": 307.0, "relativeCreated": 482664.8678779602, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:51:39.383336", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757605899.3833358, "msecs": 383.0, "relativeCreated": 542740.3936386108, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:52:00.511523", "name": "system_events", "levelname": "INFO", "message": "Frontend dashboard loaded.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 55, "msg": "Frontend dashboard loaded.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "index", "created": 1757605920.5115228, "msecs": 511.0, "relativeCreated": 563868.5805797577, "thread": 134139248789184, "threadName": "Thread-67 (process_request_thread)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:52:39.457022", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757605959.4570217, "msecs": 457.0, "relativeCreated": 602814.0795230865, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:52:40.335485", "name": "system_events", "levelname": "INFO", "message": "Frontend dashboard loaded.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 55, "msg": "Frontend dashboard loaded.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "index", "created": 1757605960.335485, "msecs": 335.0, "relativeCreated": 603692.5427913666, "thread": 134139248789184, "threadName": "Thread-69 (process_request_thread)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:53:39.532449", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606019.5324488, "msecs": 532.0, "relativeCreated": 662889.5065784454, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:54:39.603799", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606079.6037989, "msecs": 603.0, "relativeCreated": 722960.8566761017, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:55:39.676278", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606139.676278, "msecs": 676.0, "relativeCreated": 783033.3359241486, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:56:39.750060", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606199.7500596, "msecs": 750.0, "relativeCreated": 843107.1174144745, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:57:39.822003", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606259.822003, "msecs": 822.0, "relativeCreated": 903179.0606975555, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:58:39.896278", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606319.8962777, "msecs": 896.0, "relativeCreated": 963253.3354759216, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T11:59:39.967874", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606379.9678738, "msecs": 967.0, "relativeCreated": 1023324.9316215515, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T12:00:40.041466", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606440.0414655, "msecs": 41.0, "relativeCreated": 1083398.5233306885, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T12:01:40.112900", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606500.1128995, "msecs": 112.0, "relativeCreated": 1143469.9573516846, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T12:02:40.184540", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606560.1845403, "msecs": 184.0, "relativeCreated": 1203541.5980815887, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T12:03:40.257438", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606620.2574382, "msecs": 257.0, "relativeCreated": 1263614.4959926605, "thread": 134139258230464, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 245113, "taskName": null}
{"timestamp": "2025-09-11T12:04:28.517885", "name": "system_events", "levelname": "INFO", "message": "Backend server starting with WebSocket support.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 67, "msg": "Backend server starting with WebSocket support.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "<module>", "created": 1757606668.5178852, "msecs": 517.0, "relativeCreated": 1276.2243747711182, "thread": 135176635478144, "threadName": "MainThread", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:04:28.518471", "name": "system_events", "levelname": "INFO", "message": "Initial training of anomaly detection model...", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 41, "msg": "Initial training of anomaly detection model...", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606668.5184715, "msecs": 518.0, "relativeCreated": 1276.810646057129, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:04:29.402110", "name": "system_events", "levelname": "INFO", "message": "Anomaly detection model training complete.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 43, "msg": "Anomaly detection model training complete.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606669.4021099, "msecs": 402.0, "relativeCreated": 2160.4490280151367, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:04:29.402343", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606669.4023426, "msecs": 402.0, "relativeCreated": 2160.68172454834, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:04:29.473825", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606669.4738252, "msecs": 473.0, "relativeCreated": 2232.1643829345703, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:05:10.036479", "name": "system_events", "levelname": "INFO", "message": "Frontend dashboard loaded.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 55, "msg": "Frontend dashboard loaded.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "index", "created": 1757606710.0364785, "msecs": 36.0, "relativeCreated": 42794.81768608093, "thread": 135175392306880, "threadName": "Thread-4 (process_request_thread)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:05:29.474167", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606729.4741669, "msecs": 474.0, "relativeCreated": 62232.50603675842, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:05:29.551625", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606729.5516253, "msecs": 551.0, "relativeCreated": 62309.964418411255, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:06:29.551953", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606789.5519533, "msecs": 551.0, "relativeCreated": 122310.2924823761, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:06:29.623518", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606789.6235178, "msecs": 623.0, "relativeCreated": 122381.85691833496, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:07:29.623832", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606849.623832, "msecs": 623.0, "relativeCreated": 182382.17115402222, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:07:29.696014", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606849.6960142, "msecs": 696.0, "relativeCreated": 182454.35333251953, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:08:29.696331", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606909.696331, "msecs": 696.0, "relativeCreated": 242454.67019081116, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:08:29.766800", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606909.7668004, "msecs": 766.0, "relativeCreated": 242525.1395702362, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:09:29.767156", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606969.7671564, "msecs": 767.0, "relativeCreated": 302525.4955291748, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:09:29.840097", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757606969.8400972, "msecs": 840.0, "relativeCreated": 302598.4363555908, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:10:29.840409", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757607029.8404086, "msecs": 840.0, "relativeCreated": 362598.7477302551, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:10:29.912573", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757607029.912573, "msecs": 912.0, "relativeCreated": 362670.9122657776, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:11:29.912901", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757607089.9129014, "msecs": 912.0, "relativeCreated": 422671.240568161, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:11:30.024121", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757607090.024121, "msecs": 24.0, "relativeCreated": 422782.4602127075, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:12:30.024550", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757607150.0245502, "msecs": 24.0, "relativeCreated": 482782.8893661499, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:12:30.131294", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757607150.1312938, "msecs": 131.0, "relativeCreated": 482889.63294029236, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:13:30.131695", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757607210.1316948, "msecs": 131.0, "relativeCreated": 542890.0339603424, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:13:30.243426", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757607210.2434256, "msecs": 243.0, "relativeCreated": 543001.7647743225, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:14:30.243833", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757607270.2438326, "msecs": 243.0, "relativeCreated": 603002.171754837, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:14:30.382874", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757607270.3828735, "msecs": 382.0, "relativeCreated": 603141.2127017975, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:15:30.383200", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757607330.3831997, "msecs": 383.0, "relativeCreated": 663141.5388584137, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:15:30.489270", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757607330.4892702, "msecs": 489.0, "relativeCreated": 663247.6093769073, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:16:30.489782", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757607390.489782, "msecs": 489.0, "relativeCreated": 723248.1212615967, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:16:30.594080", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757607390.5940804, "msecs": 594.0, "relativeCreated": 723352.4196147919, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:17:30.594510", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757607450.5945098, "msecs": 594.0, "relativeCreated": 783352.8490066528, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:17:30.700792", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757607450.7007923, "msecs": 700.0, "relativeCreated": 783459.1314792633, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:18:30.701478", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757607510.7014782, "msecs": 701.0, "relativeCreated": 843459.8174095154, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-11T12:18:30.842697", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757607510.8426967, "msecs": 842.0, "relativeCreated": 843601.0358333588, "thread": 135175402796736, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 278305, "taskName": null}
{"timestamp": "2025-09-12T08:37:57.496219", "name": "system_events", "levelname": "INFO", "message": "Backend server starting with WebSocket support.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 67, "msg": "Backend server starting with WebSocket support.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "<module>", "created": 1757680677.4962194, "msecs": 496.0, "relativeCreated": 1051.9583225250244, "thread": 135128921964672, "threadName": "MainThread", "processName": "MainProcess", "process": 24512, "taskName": null}
{"timestamp": "2025-09-12T08:37:57.496825", "name": "system_events", "levelname": "INFO", "message": "Initial training of anomaly detection model...", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 41, "msg": "Initial training of anomaly detection model...", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757680677.4968247, "msecs": 496.0, "relativeCreated": 1052.5636672973633, "thread": 135127689295552, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 24512, "taskName": null}
{"timestamp": "2025-09-12T08:37:58.450973", "name": "system_events", "levelname": "INFO", "message": "Anomaly detection model training complete.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 43, "msg": "Anomaly detection model training complete.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757680678.450973, "msecs": 450.0, "relativeCreated": 2006.7119598388672, "thread": 135127689295552, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 24512, "taskName": null}
{"timestamp": "2025-09-12T08:37:58.451203", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757680678.451203, "msecs": 451.0, "relativeCreated": 2006.9420337677002, "thread": 135127689295552, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 24512, "taskName": null}
{"timestamp": "2025-09-12T08:37:58.523562", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757680678.5235617, "msecs": 523.0, "relativeCreated": 2079.30064201355, "thread": 135127689295552, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 24512, "taskName": null}
{"timestamp": "2025-09-12T08:38:58.523874", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757680738.5238743, "msecs": 523.0, "relativeCreated": 62079.61320877075, "thread": 135127689295552, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 24512, "taskName": null}
{"timestamp": "2025-09-12T08:38:58.596562", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757680738.5965617, "msecs": 596.0, "relativeCreated": 62152.30059623718, "thread": 135127689295552, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 24512, "taskName": null}
{"timestamp": "2025-09-12T08:39:21.220681", "name": "system_events", "levelname": "INFO", "message": "Frontend dashboard loaded.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 55, "msg": "Frontend dashboard loaded.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "index", "created": 1757680761.2206807, "msecs": 220.0, "relativeCreated": 84776.4196395874, "thread": 135127678805696, "threadName": "Thread-4 (process_request_thread)", "processName": "MainProcess", "process": 24512, "taskName": null}
{"timestamp": "2025-09-12T08:40:16.003020", "name": "system_events", "levelname": "INFO", "message": "Backend server starting with WebSocket support.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 67, "msg": "Backend server starting with WebSocket support.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "<module>", "created": 1757680816.00302, "msecs": 3.0, "relativeCreated": 835.7279300689697, "thread": 131799585235072, "threadName": "MainThread", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:40:16.003434", "name": "system_events", "levelname": "INFO", "message": "Initial training of anomaly detection model...", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 41, "msg": "Initial training of anomaly detection model...", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757680816.0034344, "msecs": 3.0, "relativeCreated": 836.1423015594482, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:40:16.897612", "name": "system_events", "levelname": "INFO", "message": "Anomaly detection model training complete.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 43, "msg": "Anomaly detection model training complete.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757680816.8976119, "msecs": 897.0, "relativeCreated": 1730.3197383880615, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:40:16.897835", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757680816.8978345, "msecs": 897.0, "relativeCreated": 1730.5424213409424, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:40:16.968333", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757680816.9683328, "msecs": 968.0, "relativeCreated": 1801.0406494140625, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:40:58.799524", "name": "system_events", "levelname": "INFO", "message": "Frontend dashboard loaded.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 55, "msg": "Frontend dashboard loaded.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "index", "created": 1757680858.7995236, "msecs": 799.0, "relativeCreated": 43632.23147392273, "thread": 131798342067904, "threadName": "Thread-5 (process_request_thread)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:41:16.968653", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757680876.9686532, "msecs": 968.0, "relativeCreated": 61801.361083984375, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:41:17.038466", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757680877.038466, "msecs": 38.0, "relativeCreated": 61871.17385864258, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:42:17.038766", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757680937.038766, "msecs": 38.0, "relativeCreated": 121871.47378921509, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:42:17.114711", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757680937.1147113, "msecs": 114.0, "relativeCreated": 121947.41916656494, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:43:17.115036", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757680997.1150355, "msecs": 115.0, "relativeCreated": 181947.74341583252, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:43:17.185823", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757680997.1858234, "msecs": 185.0, "relativeCreated": 182018.53132247925, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:44:17.186135", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681057.1861353, "msecs": 186.0, "relativeCreated": 242018.8431739807, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:44:17.256540", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681057.25654, "msecs": 256.0, "relativeCreated": 242089.24794197083, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:45:17.256848", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681117.2568483, "msecs": 256.0, "relativeCreated": 302089.5562171936, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:45:17.328014", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681117.3280137, "msecs": 328.0, "relativeCreated": 302160.72154045105, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:46:17.328322", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681177.3283217, "msecs": 328.0, "relativeCreated": 362161.02957725525, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:46:17.400469", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681177.4004693, "msecs": 400.0, "relativeCreated": 362233.1771850586, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:47:17.400787", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681237.4007866, "msecs": 400.0, "relativeCreated": 422233.4945201874, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:47:17.476168", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681237.4761677, "msecs": 476.0, "relativeCreated": 422308.8755607605, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:48:17.476491", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681297.4764907, "msecs": 476.0, "relativeCreated": 482309.1986179352, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:48:17.549889", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681297.5498893, "msecs": 549.0, "relativeCreated": 482382.5972080231, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:49:17.550193", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681357.550193, "msecs": 550.0, "relativeCreated": 542382.9009532928, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:49:17.623013", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681357.6230128, "msecs": 623.0, "relativeCreated": 542455.7206630707, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:50:17.623315", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681417.6233146, "msecs": 623.0, "relativeCreated": 602456.0225009918, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:50:17.694618", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681417.6946182, "msecs": 694.0, "relativeCreated": 602527.3261070251, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:51:17.694923", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681477.6949232, "msecs": 694.0, "relativeCreated": 662527.6310443878, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:51:17.767277", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681477.7672775, "msecs": 767.0, "relativeCreated": 662599.9853610992, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:52:17.767601", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681537.7676013, "msecs": 767.0, "relativeCreated": 722600.3091335297, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:52:17.840411", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681537.8404107, "msecs": 840.0, "relativeCreated": 722673.1185913086, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:53:17.840735", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681597.8407347, "msecs": 840.0, "relativeCreated": 782673.4426021576, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:53:17.911025", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681597.911025, "msecs": 911.0, "relativeCreated": 782743.7329292297, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:54:17.911331", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681657.9113312, "msecs": 911.0, "relativeCreated": 842744.0390586853, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:54:17.982065", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681657.982065, "msecs": 982.0, "relativeCreated": 842814.7728443146, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:55:17.982370", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681717.98237, "msecs": 982.0, "relativeCreated": 902815.0777816772, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:55:18.053129", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681718.0531287, "msecs": 53.0, "relativeCreated": 902885.8366012573, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:56:18.053482", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681778.0534823, "msecs": 53.0, "relativeCreated": 962886.1901760101, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:56:18.124101", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681778.1241014, "msecs": 124.0, "relativeCreated": 962956.8092823029, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:57:18.124411", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681838.1244106, "msecs": 124.0, "relativeCreated": 1022957.1185112, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:57:18.197094", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681838.197094, "msecs": 197.0, "relativeCreated": 1023029.8018455505, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:58:18.197403", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681898.1974032, "msecs": 197.0, "relativeCreated": 1083030.1110744476, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:58:18.268861", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681898.2688606, "msecs": 268.0, "relativeCreated": 1083101.5684604645, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:59:18.269167", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681958.269167, "msecs": 269.0, "relativeCreated": 1143101.8748283386, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T08:59:18.341208", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757681958.3412085, "msecs": 341.0, "relativeCreated": 1143173.9163398743, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T09:00:18.341535", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682018.3415346, "msecs": 341.0, "relativeCreated": 1203174.2424964905, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T09:00:18.413218", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682018.413218, "msecs": 413.0, "relativeCreated": 1203245.9259033203, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T09:01:18.413552", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682078.4135523, "msecs": 413.0, "relativeCreated": 1263246.2601661682, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T09:01:18.484841", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682078.484841, "msecs": 484.0, "relativeCreated": 1263317.5489902496, "thread": 131798352557760, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 27720, "taskName": null}
{"timestamp": "2025-09-12T09:01:53.033446", "name": "system_events", "levelname": "INFO", "message": "Backend server starting with WebSocket support.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 67, "msg": "Backend server starting with WebSocket support.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "<module>", "created": 1757682113.0334458, "msecs": 33.0, "relativeCreated": 865.8349514007568, "thread": 132701173080192, "threadName": "MainThread", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:01:53.033875", "name": "system_events", "levelname": "INFO", "message": "Initial training of anomaly detection model...", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 41, "msg": "Initial training of anomaly detection model...", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682113.0338755, "msecs": 33.0, "relativeCreated": 866.2645816802979, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:01:53.984257", "name": "system_events", "levelname": "INFO", "message": "Anomaly detection model training complete.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 43, "msg": "Anomaly detection model training complete.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682113.984257, "msecs": 984.0, "relativeCreated": 1816.6460990905762, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:01:53.984506", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682113.9845064, "msecs": 984.0, "relativeCreated": 1816.8954849243164, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:01:54.055717", "name": "system_events", "levelname": "WARNING", "message": "Detected 3 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 3 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682114.0557168, "msecs": 55.0, "relativeCreated": 1888.105869293213, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:02:40.539467", "name": "system_events", "levelname": "INFO", "message": "Frontend dashboard loaded.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 55, "msg": "Frontend dashboard loaded.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "index", "created": 1757682160.539467, "msecs": 539.0, "relativeCreated": 48371.85621261597, "thread": 132699859711680, "threadName": "Thread-5 (process_request_thread)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:02:54.056053", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682174.0560527, "msecs": 56.0, "relativeCreated": 61888.44180107117, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:02:54.126316", "name": "system_events", "levelname": "WARNING", "message": "Detected 3 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 3 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682174.1263156, "msecs": 126.0, "relativeCreated": 61958.704710006714, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:03:54.126648", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682234.126648, "msecs": 126.0, "relativeCreated": 121959.03706550598, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:03:54.198365", "name": "system_events", "levelname": "WARNING", "message": "Detected 3 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 3 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682234.1983652, "msecs": 198.0, "relativeCreated": 122030.75432777405, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:04:24.672824", "name": "system_events", "levelname": "INFO", "message": "Frontend dashboard loaded.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 55, "msg": "Frontend dashboard loaded.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "index", "created": 1757682264.6728241, "msecs": 672.0, "relativeCreated": 152505.21326065063, "thread": 132699859711680, "threadName": "Thread-8 (process_request_thread)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:04:54.198696", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682294.1986961, "msecs": 198.0, "relativeCreated": 182031.08525276184, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:04:54.271870", "name": "system_events", "levelname": "WARNING", "message": "Detected 3 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 3 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682294.2718701, "msecs": 271.0, "relativeCreated": 182104.25925254822, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:05:33.383728", "name": "system_events", "levelname": "INFO", "message": "Frontend dashboard loaded.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 55, "msg": "Frontend dashboard loaded.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "index", "created": 1757682333.3837283, "msecs": 383.0, "relativeCreated": 221216.11738204956, "thread": 132699859711680, "threadName": "Thread-11 (process_request_thread)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:05:54.272232", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682354.2722323, "msecs": 272.0, "relativeCreated": 242104.62141036987, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:05:54.344231", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682354.344231, "msecs": 344.0, "relativeCreated": 242176.62000656128, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:06:54.344572", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682414.3445723, "msecs": 344.0, "relativeCreated": 302176.96142196655, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:06:54.417192", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682414.4171915, "msecs": 417.0, "relativeCreated": 302249.58062171936, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:07:54.417499", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682474.4174986, "msecs": 417.0, "relativeCreated": 362249.88770484924, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:07:54.487133", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682474.4871328, "msecs": 487.0, "relativeCreated": 362319.5219039917, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:08:54.487471", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682534.487471, "msecs": 487.0, "relativeCreated": 422319.86021995544, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:08:54.556721", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682534.5567214, "msecs": 556.0, "relativeCreated": 422389.11056518555, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:09:54.557031", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682594.5570312, "msecs": 557.0, "relativeCreated": 482389.4202709198, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:09:54.636434", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682594.636434, "msecs": 636.0, "relativeCreated": 482468.8231945038, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:10:54.636788", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682654.6367884, "msecs": 636.0, "relativeCreated": 542469.1774845123, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:10:54.706963", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682654.706963, "msecs": 706.0, "relativeCreated": 542539.3521785736, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:11:54.707277", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682714.707277, "msecs": 707.0, "relativeCreated": 602539.6661758423, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:11:54.780144", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682714.780144, "msecs": 780.0, "relativeCreated": 602612.5330924988, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:12:54.780459", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682774.780459, "msecs": 780.0, "relativeCreated": 662612.8480434418, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:12:54.852479", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682774.852479, "msecs": 852.0, "relativeCreated": 662684.8680973053, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:13:54.852783", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682834.852783, "msecs": 852.0, "relativeCreated": 722685.1720809937, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:13:54.925903", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682834.9259028, "msecs": 925.0, "relativeCreated": 722758.2919597626, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:14:54.926223", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682894.9262226, "msecs": 926.0, "relativeCreated": 782758.6116790771, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:14:54.999112", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682894.999112, "msecs": 999.0, "relativeCreated": 782831.5010070801, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:15:54.999419", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682954.9994187, "msecs": 999.0, "relativeCreated": 842831.8078517914, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:15:55.073178", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757682955.073178, "msecs": 73.0, "relativeCreated": 842905.5671691895, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:16:55.073492", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683015.073492, "msecs": 73.0, "relativeCreated": 902905.8811664581, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:16:55.144718", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683015.1447182, "msecs": 144.0, "relativeCreated": 902977.1072864532, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:17:55.145039", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683075.1450393, "msecs": 145.0, "relativeCreated": 962977.4284362793, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:17:55.216397", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683075.2163966, "msecs": 216.0, "relativeCreated": 963048.7856864929, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:18:55.216717", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683135.2167165, "msecs": 216.0, "relativeCreated": 1023049.1056442261, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:18:55.288834", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683135.288834, "msecs": 288.0, "relativeCreated": 1023121.2232112885, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:19:55.289150", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683195.2891498, "msecs": 289.0, "relativeCreated": 1083121.5388774872, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:19:55.360263", "name": "system_events", "levelname": "WARNING", "message": "Detected 2 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 2 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683195.3602629, "msecs": 360.0, "relativeCreated": 1083192.6519870758, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:20:55.360598", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683255.3605978, "msecs": 360.0, "relativeCreated": 1143192.9869651794, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:20:55.432737", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683255.4327374, "msecs": 432.0, "relativeCreated": 1143265.126466751, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:21:55.433060", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683315.43306, "msecs": 433.0, "relativeCreated": 1203265.4490470886, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:21:55.504721", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683315.504721, "msecs": 504.0, "relativeCreated": 1203337.110042572, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:22:55.505035", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683375.505035, "msecs": 505.0, "relativeCreated": 1263337.4240398407, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:22:55.577865", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683375.5778646, "msecs": 577.0, "relativeCreated": 1263410.2537631989, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:23:55.578193", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683435.5781927, "msecs": 578.0, "relativeCreated": 1323410.5818271637, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:23:55.649467", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683435.6494672, "msecs": 649.0, "relativeCreated": 1323481.8563461304, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:24:55.649781", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683495.6497815, "msecs": 649.0, "relativeCreated": 1383482.1705818176, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:24:55.720481", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683495.7204807, "msecs": 720.0, "relativeCreated": 1383552.869796753, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:25:55.720770", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683555.72077, "msecs": 720.0, "relativeCreated": 1443553.1589984894, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:25:55.791448", "name": "system_events", "levelname": "WARNING", "message": "Detected 1 potential anomalies.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 49, "msg": "Detected 1 potential anomalies.", "args": [], "levelno": 30, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683555.7914476, "msecs": 791.0, "relativeCreated": 1443623.8367557526, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:26:55.791762", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683615.7917616, "msecs": 791.0, "relativeCreated": 1503624.1507530212, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:27:55.864743", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683675.864743, "msecs": 864.0, "relativeCreated": 1563697.1321105957, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:28:55.937306", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683735.937306, "msecs": 937.0, "relativeCreated": 1623769.6950435638, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:29:56.011183", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683796.011183, "msecs": 11.0, "relativeCreated": 1683843.57213974, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:30:56.086158", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683856.0861576, "msecs": 86.0, "relativeCreated": 1743918.5466766357, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:31:56.160247", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683916.1602468, "msecs": 160.0, "relativeCreated": 1803992.6359653473, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:32:56.232371", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757683976.232371, "msecs": 232.0, "relativeCreated": 1864064.7602081299, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:33:56.302884", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757684036.3028836, "msecs": 302.0, "relativeCreated": 1924135.2727413177, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:34:56.373800", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757684096.3737996, "msecs": 373.0, "relativeCreated": 1984206.1886787415, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:35:56.447854", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757684156.4478536, "msecs": 447.0, "relativeCreated": 2044280.2426815033, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:36:56.523450", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757684216.5234497, "msecs": 523.0, "relativeCreated": 2104355.8387756348, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:37:56.596501", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757684276.5965014, "msecs": 596.0, "relativeCreated": 2164428.89046669, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:38:56.669012", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757684336.669012, "msecs": 669.0, "relativeCreated": 2224501.4011859894, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:39:56.742781", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757684396.7427814, "msecs": 742.0, "relativeCreated": 2284575.170516968, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:40:56.817323", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757684456.8173232, "msecs": 817.0, "relativeCreated": 2344649.7123241425, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:41:56.889284", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757684516.8892844, "msecs": 889.0, "relativeCreated": 2404721.673488617, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
{"timestamp": "2025-09-12T09:42:56.961926", "name": "system_events", "levelname": "INFO", "message": "Running periodic anomaly detection.", "pathname": "/home/<USER>/offline_sentinel/backend/run.py", "lineno": 46, "msg": "Running periodic anomaly detection.", "args": [], "levelno": 20, "filename": "run.py", "module": "run", "exc_info": null, "exc_text": null, "stack_info": null, "funcName": "run_anomaly_detection_periodically", "created": 1757684576.961926, "msecs": 961.0, "relativeCreated": 2464794.315099716, "thread": 132699940378304, "threadName": "Thread-1 (run_anomaly_detection_periodically)", "processName": "MainProcess", "process": 41710, "taskName": null}
