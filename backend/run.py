from flask import Flask, render_template
from flask_socketio import Socket<PERSON>
from flask_cors import CORS # Import CORS
import os
import threading
import time

from .logging_config import initialize_loggers, system_logger
from .anomaly_detection_service import anomaly_detector

# Import Blueprints
from .routes.log_routes import log_bp
from .routes.agent_routes import agent_bp
from .routes.file_routes import file_bp
from .routes.voice_routes import voice_bp
from .routes.chat_routes import chat_bp
from .routes.pdf_routes import pdf_bp

app = Flask(__name__,
                template_folder=os.path.join(os.path.dirname(__file__), '../05_frontend/templates'),
                static_folder=os.path.join(os.path.dirname(__file__), '../05_frontend'),
                static_url_path='/static')
CORS(app) # Enable CORS for the Flask app
socketio = SocketIO(app, cors_allowed_origins="*")

# Initialize loggers with the SocketIO instance
initialize_loggers(socketio)
from .logging_config import system_logger

# Register Blueprints
app.register_blueprint(log_bp)
app.register_blueprint(agent_bp)
app.register_blueprint(file_bp)
app.register_blueprint(voice_bp)
app.register_blueprint(chat_bp)
app.register_blueprint(pdf_bp)

# Ensure uploads directory exists
UPLOAD_FOLDER = os.path.join(os.path.dirname(__file__), '../uploads')
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

def run_anomaly_detection_periodically():
    """Runs anomaly detection in a background thread every 60 seconds."""
    with app.app_context():
        # Initial training
        system_logger.info("Initial training of anomaly detection model...")
        anomaly_detector.train_model()
        system_logger.info("Anomaly detection model training complete.")

        while True:
            system_logger.info("Running periodic anomaly detection.")
            anomalies = anomaly_detector.detect_anomalies()
            if anomalies:
                system_logger.warning(f"Detected {len(anomalies)} potential anomalies.")
                socketio.emit('new_anomaly', {'anomalies': anomalies})
            time.sleep(60)

@app.route('/')
def index():
    system_logger.info('Frontend dashboard loaded.')
    return render_template('index.html')

@socketio.on('connect')
def handle_connect():
    system_logger.info('Dashboard client connected.')

@socketio.on('disconnect')
def handle_disconnect():
    system_logger.info('Dashboard client disconnected.')

if __name__ == '__main__':
    system_logger.info("Backend server starting with WebSocket support.")
    # Start the anomaly detection thread
    anomaly_thread = threading.Thread(target=run_anomaly_detection_periodically, daemon=True)
    anomaly_thread.start()
    socketio.run(app, host='0.0.0.0', port=8001, debug=False, allow_unsafe_werkzeug=True)

