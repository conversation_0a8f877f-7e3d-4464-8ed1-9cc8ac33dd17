#!/usr/bin/env python3
"""
Create a simple icon for the Offline Sentinel application
"""

from PyQt6.QtGui import QPixmap, QPainter, QColor, QFont, QBrush
from PyQt6.QtCore import Qt

def create_sentinel_icon():
    """Create a simple 'S' icon for Sentinel"""
    # Create a 64x64 pixmap
    pixmap = QPixmap(64, 64)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    # Create painter
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # Draw background circle
    painter.setBrush(QBrush(QColor(59, 130, 246)))  # Blue background
    painter.setPen(Qt.PenStyle.NoPen)
    painter.drawEllipse(4, 4, 56, 56)
    
    # Draw 'S' text
    painter.setPen(QColor(255, 255, 255))  # White text
    font = QFont("Arial", 32, QFont.Weight.Bold)
    painter.setFont(font)
    painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "S")
    
    painter.end()
    
    return pixmap

if __name__ == "__main__":
    from PyQt6.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    
    # Create and save icon
    icon = create_sentinel_icon()
    icon.save("assets/sentinel_icon.png")
    
    print("✅ Icon created successfully: assets/sentinel_icon.png")
    
    app.quit()
