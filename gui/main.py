#!/usr/bin/env python3
"""
Offline Sentinel - PyQt6 Desktop Application
Main application entry point and window management
"""

import sys
import os
import json
import requests
from datetime import datetime
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QStackedWidget, QLabel, QPushButton, QFrame, QScrollArea,
    QGridLayout, QTextEdit, QProgressBar, QTabWidget, QSplitter,
    QSystemTrayIcon, QMenu, QMessageBox, QStatusBar
)
from PyQt6.QtCore import (
    Qt, QTimer, QThread, pyqtSignal, QPropertyAnimation, QEasingCurve,
    QRect, QSize, QUrl
)
from PyQt6.QtGui import (
    QFont, QPixmap, QIcon, QPalette, QColor, QLinearGradient,
    QBrush, QPainter, QAction
)

# Import custom widgets
from widgets.dashboard_widget import DashboardWidget
from widgets.agents_widget import AgentsWidget
from widgets.research_widget import ResearchWidget
from widgets.system_widget import SystemWidget
from widgets.network_widget import NetworkWidget
from widgets.chat_widget import ChatWidget
from widgets.settings_widget import SettingsWidget
from utils.api_client import APIClient
from utils.theme_manager import ThemeManager
from utils.config_manager import ConfigManager


class SentinelMainWindow(QMainWindow):
    """Main application window for Offline Sentinel"""
    
    def __init__(self):
        super().__init__()
        self.api_client = APIClient()
        self.theme_manager = ThemeManager()
        self.config_manager = ConfigManager()
        
        # Initialize UI
        self.init_ui()
        self.setup_system_tray()
        self.setup_timers()
        self.apply_theme()
        
        # Start live updates
        self.start_live_updates()
        
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("Offline Sentinel - AI Command Center")
        self.setMinimumSize(1400, 900)
        self.resize(1600, 1000)
        
        # Set window icon
        self.setWindowIcon(QIcon("assets/sentinel_icon.png"))
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Create sidebar
        self.create_sidebar()
        
        # Create main content area
        self.create_main_content()
        
        # Add to main layout
        main_layout.addWidget(self.sidebar_frame)
        main_layout.addWidget(self.main_content_widget, 1)
        
        # Create status bar
        self.create_status_bar()
        
    def create_sidebar(self):
        """Create the navigation sidebar"""
        self.sidebar_frame = QFrame()
        self.sidebar_frame.setFixedWidth(250)
        self.sidebar_frame.setObjectName("sidebar")
        
        sidebar_layout = QVBoxLayout(self.sidebar_frame)
        sidebar_layout.setContentsMargins(20, 20, 20, 20)
        sidebar_layout.setSpacing(10)
        
        # Logo section
        logo_widget = QWidget()
        logo_layout = QHBoxLayout(logo_widget)
        logo_layout.setContentsMargins(0, 0, 0, 0)
        
        logo_icon = QLabel("S")
        logo_icon.setObjectName("logo-icon")
        logo_icon.setFixedSize(40, 40)
        logo_icon.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        logo_text = QLabel("Sentinel")
        logo_text.setObjectName("logo-text")
        logo_text.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        
        logo_layout.addWidget(logo_icon)
        logo_layout.addWidget(logo_text)
        logo_layout.addStretch()
        
        sidebar_layout.addWidget(logo_widget)
        sidebar_layout.addSpacing(30)
        
        # Navigation buttons
        self.nav_buttons = {}
        nav_items = [
            ("🏠", "Dashboard", "dashboard"),
            ("🤖", "Agents", "agents"),
            ("🔬", "Research", "research"),
            ("⚙️", "System", "system"),
            ("📡", "Network", "network"),
            ("💬", "Chat", "chat"),
            ("🔧", "Settings", "settings")
        ]
        
        for icon, text, key in nav_items:
            btn = QPushButton(f"{icon}  {text}")
            btn.setObjectName("nav-button")
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, k=key: self.switch_page(k))
            self.nav_buttons[key] = btn
            sidebar_layout.addWidget(btn)
        
        # Set dashboard as active by default
        self.nav_buttons["dashboard"].setChecked(True)
        
        sidebar_layout.addStretch()
        
        # Live indicator
        self.live_indicator = QLabel("🟢 LIVE")
        self.live_indicator.setObjectName("live-indicator")
        self.live_indicator.setAlignment(Qt.AlignmentFlag.AlignCenter)
        sidebar_layout.addWidget(self.live_indicator)
        
    def create_main_content(self):
        """Create the main content area with stacked widgets"""
        self.main_content_widget = QWidget()
        main_content_layout = QVBoxLayout(self.main_content_widget)
        main_content_layout.setContentsMargins(0, 0, 0, 0)
        
        # Header
        self.create_header()
        main_content_layout.addWidget(self.header_widget)
        
        # Stacked widget for different pages
        self.stacked_widget = QStackedWidget()
        
        # Create page widgets
        self.dashboard_widget = DashboardWidget(self.api_client)
        self.agents_widget = AgentsWidget(self.api_client)
        self.research_widget = ResearchWidget(self.api_client)
        self.system_widget = SystemWidget(self.api_client)
        self.network_widget = NetworkWidget(self.api_client)
        self.chat_widget = ChatWidget(self.api_client)
        self.settings_widget = SettingsWidget(self.config_manager)
        
        # Add pages to stacked widget
        self.stacked_widget.addWidget(self.dashboard_widget)
        self.stacked_widget.addWidget(self.agents_widget)
        self.stacked_widget.addWidget(self.research_widget)
        self.stacked_widget.addWidget(self.system_widget)
        self.stacked_widget.addWidget(self.network_widget)
        self.stacked_widget.addWidget(self.chat_widget)
        self.stacked_widget.addWidget(self.settings_widget)
        
        main_content_layout.addWidget(self.stacked_widget, 1)
        
    def create_header(self):
        """Create the header with title and status"""
        self.header_widget = QWidget()
        self.header_widget.setObjectName("header")
        self.header_widget.setFixedHeight(80)
        
        header_layout = QHBoxLayout(self.header_widget)
        header_layout.setContentsMargins(30, 20, 30, 20)
        
        # Left side - title and subtitle
        title_widget = QWidget()
        title_layout = QVBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(5)
        
        self.page_title = QLabel("AI Command Center")
        self.page_title.setObjectName("page-title")
        self.page_title.setFont(QFont("Arial", 24, QFont.Weight.Bold))
        
        self.page_subtitle = QLabel("Multi-agent system running locally • All systems operational")
        self.page_subtitle.setObjectName("page-subtitle")
        self.page_subtitle.setFont(QFont("Arial", 12))
        
        title_layout.addWidget(self.page_title)
        title_layout.addWidget(self.page_subtitle)
        
        # Right side - status info
        status_widget = QWidget()
        status_layout = QVBoxLayout(status_widget)
        status_layout.setContentsMargins(0, 0, 0, 0)
        status_layout.setAlignment(Qt.AlignmentFlag.AlignRight)
        
        self.last_update_label = QLabel("Last updated: --:--:--")
        self.last_update_label.setObjectName("status-text")
        
        self.next_update_label = QLabel("Next update in: 5s")
        self.next_update_label.setObjectName("status-text")
        
        status_layout.addWidget(self.last_update_label)
        status_layout.addWidget(self.next_update_label)
        
        header_layout.addWidget(title_widget)
        header_layout.addStretch()
        header_layout.addWidget(status_widget)
        
    def create_status_bar(self):
        """Create the status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Add permanent widgets to status bar
        self.connection_status = QLabel("🟢 Connected")
        self.agent_count_status = QLabel("4 Agents Active")
        self.system_health_status = QLabel("System Health: 94%")
        
        self.status_bar.addPermanentWidget(self.connection_status)
        self.status_bar.addPermanentWidget(self.agent_count_status)
        self.status_bar.addPermanentWidget(self.system_health_status)
        
        self.status_bar.showMessage("Offline Sentinel initialized successfully")
        
    def setup_system_tray(self):
        """Setup system tray icon and menu"""
        if QSystemTrayIcon.isSystemTrayAvailable():
            self.tray_icon = QSystemTrayIcon(self)
            self.tray_icon.setIcon(QIcon("assets/sentinel_icon.png"))
            
            # Create tray menu
            tray_menu = QMenu()
            
            show_action = QAction("Show Sentinel", self)
            show_action.triggered.connect(self.show)
            
            hide_action = QAction("Hide Sentinel", self)
            hide_action.triggered.connect(self.hide)
            
            quit_action = QAction("Quit", self)
            quit_action.triggered.connect(QApplication.instance().quit)
            
            tray_menu.addAction(show_action)
            tray_menu.addAction(hide_action)
            tray_menu.addSeparator()
            tray_menu.addAction(quit_action)
            
            self.tray_icon.setContextMenu(tray_menu)
            self.tray_icon.show()
            
            # Handle tray icon activation
            self.tray_icon.activated.connect(self.tray_icon_activated)
        
    def setup_timers(self):
        """Setup update timers"""
        # Main update timer (5 seconds)
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_live_data)
        
        # Countdown timer (1 second)
        self.countdown_timer = QTimer()
        self.countdown_timer.timeout.connect(self.update_countdown)
        self.countdown_value = 5
        
        # Live indicator animation timer
        self.live_animation_timer = QTimer()
        self.live_animation_timer.timeout.connect(self.animate_live_indicator)
        
    def apply_theme(self):
        """Apply the dark theme styling"""
        self.setStyleSheet(self.theme_manager.get_stylesheet())
        
    def switch_page(self, page_key):
        """Switch to a different page"""
        # Update navigation buttons
        for key, btn in self.nav_buttons.items():
            btn.setChecked(key == page_key)
        
        # Update page content
        page_index = {
            "dashboard": 0,
            "agents": 1,
            "research": 2,
            "system": 3,
            "network": 4,
            "chat": 5,
            "settings": 6
        }.get(page_key, 0)
        
        self.stacked_widget.setCurrentIndex(page_index)
        
        # Update header titles
        titles = {
            "dashboard": ("AI Command Center", "Multi-agent system running locally • All systems operational"),
            "agents": ("Agent Network", "Multi-agent coordination and management • 4 agents online"),
            "research": ("Research Center", "AI-powered analysis and threat intelligence • Research Agent active"),
            "system": ("System Monitor", "Performance metrics and system health • All systems green"),
            "network": ("Network Operations", "Network monitoring and security • Connections secure"),
            "chat": ("AI Chat Interface", "Interactive communication with AI agents • Live TTS enabled"),
            "settings": ("System Settings", "Configuration and preferences • Customize your experience")
        }
        
        title, subtitle = titles.get(page_key, ("AI Command Center", "Multi-agent system running locally"))
        self.page_title.setText(title)
        self.page_subtitle.setText(subtitle)
        
    def start_live_updates(self):
        """Start the live update timers"""
        self.update_timer.start(5000)  # 5 seconds
        self.countdown_timer.start(1000)  # 1 second
        self.live_animation_timer.start(2000)  # 2 seconds
        
        # Initial update
        self.update_live_data()
        
    def update_live_data(self):
        """Update all live data from APIs"""
        try:
            # Update current page
            current_widget = self.stacked_widget.currentWidget()
            if hasattr(current_widget, 'update_data'):
                current_widget.update_data()
            
            # Update status bar
            self.update_status_bar()
            
            # Update last update time
            now = datetime.now()
            self.last_update_label.setText(f"Last updated: {now.strftime('%H:%M:%S')}")
            
            # Reset countdown
            self.countdown_value = 5
            
            # Flash live indicator
            self.flash_live_indicator()
            
        except Exception as e:
            print(f"Error updating live data: {e}")
            
    def update_countdown(self):
        """Update the countdown timer"""
        self.next_update_label.setText(f"Next update in: {self.countdown_value}s")
        self.countdown_value -= 1
        if self.countdown_value < 0:
            self.countdown_value = 5
            
    def update_status_bar(self):
        """Update status bar information"""
        try:
            # Get system status
            agents_data = self.api_client.get_agents()
            diagnostics_data = self.api_client.get_diagnostics()
            
            # Update status widgets
            active_agents = len([a for a in agents_data if a.get('status') == 'active'])
            self.agent_count_status.setText(f"{active_agents} Agents Active")
            
            health = diagnostics_data.get('health', 94)
            self.system_health_status.setText(f"System Health: {health}%")
            
            # Update connection status
            self.connection_status.setText("🟢 Connected")
            
        except Exception as e:
            self.connection_status.setText("🔴 Disconnected")
            print(f"Error updating status bar: {e}")
            
    def animate_live_indicator(self):
        """Animate the live indicator"""
        current_text = self.live_indicator.text()
        if "🟢" in current_text:
            self.live_indicator.setText("🔵 LIVE")
        else:
            self.live_indicator.setText("🟢 LIVE")
            
    def flash_live_indicator(self):
        """Flash the live indicator to show activity"""
        self.live_indicator.setText("🔵 LIVE")
        QTimer.singleShot(200, lambda: self.live_indicator.setText("🟢 LIVE"))
        
    def tray_icon_activated(self, reason):
        """Handle system tray icon activation"""
        if reason == QSystemTrayIcon.ActivationReason.Trigger:
            if self.isVisible():
                self.hide()
            else:
                self.show()
                self.raise_()
                self.activateWindow()
                
    def closeEvent(self, event):
        """Handle window close event"""
        if QSystemTrayIcon.isSystemTrayAvailable() and self.tray_icon.isVisible():
            QMessageBox.information(
                self, "Offline Sentinel",
                "The application will keep running in the system tray. "
                "To terminate the application, choose 'Quit' in the context menu of the system tray entry."
            )
            self.hide()
            event.ignore()
        else:
            event.accept()


def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    app.setApplicationName("Offline Sentinel")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Sentinel Systems")
    
    # Set application icon
    app.setWindowIcon(QIcon("assets/sentinel_icon.png"))
    
    # Create and show main window
    window = SentinelMainWindow()
    window.show()
    
    # Start event loop
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
