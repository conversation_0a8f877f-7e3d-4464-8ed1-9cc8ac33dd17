"""
Enhanced PDF Processing Widget for Offline Sentinel
Comprehensive PDF upload, analysis, and AI-powered document processing
"""

import os
import json
import hashlib
import threading
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QScrollArea, QFrame, QProgressBar, QListWidget, QListWidgetItem,
    QTextEdit, QSplitter, QGroupBox, QGridLayout, QFileDialog,
    QMessageBox, QTabWidget, QComboBox, QSpinBox, QCheckBox,
    QTreeWidget, QTreeWidgetItem, QTableWidget, QTableWidgetItem
)
from PyQt6.QtCore import (
    Qt, QTimer, pyqtSignal, QThread, QObject, QMimeData, QUrl
)
from PyQt6.QtGui import (
    QFont, QPixmap, QPainter, QColor, QDragEnterEvent, QDropEvent,
    QIcon, QStandardItemModel, QStandardItem
)

try:
    import PyPDF2
    import fitz  # PyMuPDF
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    print("⚠️ PDF libraries not available. Install PyPDF2 and PyMuPDF for full functionality.")

try:
    from PIL import Image
    import pytesseract
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    print("⚠️ OCR libraries not available. Install Pillow and pytesseract for OCR functionality.")


class PDFProcessor(QObject):
    """PDF processing engine with advanced analysis capabilities"""
    
    processing_started = pyqtSignal(str)  # file_path
    processing_progress = pyqtSignal(str, int)  # file_path, progress
    processing_completed = pyqtSignal(str, dict)  # file_path, results
    processing_error = pyqtSignal(str, str)  # file_path, error
    
    def __init__(self):
        super().__init__()
        self.processing_queue = []
        self.is_processing = False
        
    def process_pdf(self, file_path: str, options: Dict[str, Any] = None):
        """Process a PDF file with specified options"""
        if not PDF_AVAILABLE:
            self.processing_error.emit(file_path, "PDF libraries not available")
            return
            
        options = options or {}
        self.processing_queue.append((file_path, options))
        
        if not self.is_processing:
            self._process_next()
            
    def _process_next(self):
        """Process next PDF in queue"""
        if not self.processing_queue:
            self.is_processing = False
            return
            
        self.is_processing = True
        file_path, options = self.processing_queue.pop(0)
        
        # Start processing in separate thread
        thread = threading.Thread(
            target=self._process_pdf_thread,
            args=(file_path, options),
            daemon=True
        )
        thread.start()
        
    def _process_pdf_thread(self, file_path: str, options: Dict[str, Any]):
        """Process PDF in separate thread"""
        try:
            self.processing_started.emit(file_path)
            
            results = {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'file_size': os.path.getsize(file_path),
                'processed_at': datetime.now().isoformat(),
                'metadata': {},
                'content': {},
                'analysis': {},
                'pages': []
            }
            
            # Extract metadata and content
            self.processing_progress.emit(file_path, 20)
            metadata, content = self._extract_pdf_content(file_path)
            results['metadata'] = metadata
            results['content'] = content
            
            # Perform analysis
            self.processing_progress.emit(file_path, 60)
            analysis = self._analyze_content(content, options)
            results['analysis'] = analysis
            
            # Extract page information
            self.processing_progress.emit(file_path, 80)
            pages = self._extract_page_info(file_path)
            results['pages'] = pages
            
            # OCR if requested and available
            if options.get('enable_ocr', False) and OCR_AVAILABLE:
                self.processing_progress.emit(file_path, 90)
                ocr_text = self._perform_ocr(file_path)
                results['content']['ocr_text'] = ocr_text
            
            self.processing_progress.emit(file_path, 100)
            self.processing_completed.emit(file_path, results)
            
        except Exception as e:
            self.processing_error.emit(file_path, str(e))
        finally:
            # Process next in queue
            QTimer.singleShot(100, self._process_next)
            
    def _extract_pdf_content(self, file_path: str):
        """Extract metadata and text content from PDF"""
        metadata = {}
        content = {'text': '', 'images': [], 'links': []}
        
        try:
            # Using PyMuPDF for better extraction
            doc = fitz.open(file_path)
            
            # Extract metadata
            metadata = {
                'title': doc.metadata.get('title', ''),
                'author': doc.metadata.get('author', ''),
                'subject': doc.metadata.get('subject', ''),
                'creator': doc.metadata.get('creator', ''),
                'producer': doc.metadata.get('producer', ''),
                'creation_date': doc.metadata.get('creationDate', ''),
                'modification_date': doc.metadata.get('modDate', ''),
                'page_count': doc.page_count,
                'encrypted': doc.is_encrypted
            }
            
            # Extract text content
            full_text = []
            for page_num in range(doc.page_count):
                page = doc[page_num]
                text = page.get_text()
                full_text.append(text)
                
                # Extract links
                links = page.get_links()
                for link in links:
                    if 'uri' in link:
                        content['links'].append({
                            'page': page_num + 1,
                            'uri': link['uri'],
                            'rect': link['from']
                        })
            
            content['text'] = '\n'.join(full_text)
            doc.close()
            
        except Exception as e:
            # Fallback to PyPDF2
            try:
                with open(file_path, 'rb') as file:
                    reader = PyPDF2.PdfReader(file)
                    
                    # Extract metadata
                    if reader.metadata:
                        metadata = {
                            'title': reader.metadata.get('/Title', ''),
                            'author': reader.metadata.get('/Author', ''),
                            'subject': reader.metadata.get('/Subject', ''),
                            'creator': reader.metadata.get('/Creator', ''),
                            'producer': reader.metadata.get('/Producer', ''),
                            'page_count': len(reader.pages)
                        }
                    
                    # Extract text
                    text_content = []
                    for page in reader.pages:
                        text_content.append(page.extract_text())
                    
                    content['text'] = '\n'.join(text_content)
                    
            except Exception as e2:
                raise Exception(f"Failed to extract PDF content: {e2}")
        
        return metadata, content
        
    def _analyze_content(self, content: Dict[str, Any], options: Dict[str, Any]):
        """Analyze extracted content"""
        text = content.get('text', '')
        
        analysis = {
            'word_count': len(text.split()),
            'character_count': len(text),
            'line_count': len(text.split('\n')),
            'language': 'unknown',
            'keywords': [],
            'summary': '',
            'topics': [],
            'sentiment': 'neutral'
        }
        
        if text:
            # Basic keyword extraction (simple frequency analysis)
            words = text.lower().split()
            word_freq = {}
            for word in words:
                if len(word) > 3:  # Filter short words
                    word_freq[word] = word_freq.get(word, 0) + 1
            
            # Get top keywords
            sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
            analysis['keywords'] = [word for word, freq in sorted_words[:20]]
            
            # Simple summary (first few sentences)
            sentences = text.split('.')[:3]
            analysis['summary'] = '. '.join(sentences).strip()
            
        return analysis
        
    def _extract_page_info(self, file_path: str):
        """Extract detailed page information"""
        pages = []
        
        try:
            doc = fitz.open(file_path)
            
            for page_num in range(doc.page_count):
                page = doc[page_num]
                
                page_info = {
                    'page_number': page_num + 1,
                    'width': page.rect.width,
                    'height': page.rect.height,
                    'rotation': page.rotation,
                    'text_length': len(page.get_text()),
                    'image_count': len(page.get_images()),
                    'link_count': len(page.get_links())
                }
                
                pages.append(page_info)
                
            doc.close()
            
        except Exception as e:
            print(f"Error extracting page info: {e}")
            
        return pages
        
    def _perform_ocr(self, file_path: str):
        """Perform OCR on PDF images"""
        if not OCR_AVAILABLE:
            return ""
            
        try:
            doc = fitz.open(file_path)
            ocr_text = []
            
            for page_num in range(min(5, doc.page_count)):  # Limit to first 5 pages
                page = doc[page_num]
                
                # Convert page to image
                mat = fitz.Matrix(2, 2)  # Zoom factor
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")
                
                # Save temporary image
                temp_path = f"/tmp/page_{page_num}.png"
                with open(temp_path, "wb") as f:
                    f.write(img_data)
                
                # Perform OCR
                try:
                    text = pytesseract.image_to_string(Image.open(temp_path))
                    ocr_text.append(text)
                except:
                    pass
                finally:
                    # Clean up
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
            
            doc.close()
            return '\n'.join(ocr_text)
            
        except Exception as e:
            print(f"OCR error: {e}")
            return ""


class PDFDropZone(QFrame):
    """Drag and drop zone for PDF files"""
    
    files_dropped = pyqtSignal(list)  # List of file paths
    
    def __init__(self):
        super().__init__()
        self.setAcceptDrops(True)
        self.setObjectName("pdf-drop-zone")
        self.setup_ui()
        
    def setup_ui(self):
        """Setup drop zone UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(16)
        
        # Icon
        icon_label = QLabel("📄")
        icon_label.setFont(QFont("Segoe UI", 48))
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setObjectName("drop-icon")
        
        # Title
        title_label = QLabel("Drop PDF files here")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setObjectName("drop-title")
        
        # Description
        desc_label = QLabel("Or click to browse files\nSupported: PDF, up to 100MB each")
        desc_label.setFont(QFont("Segoe UI", 12))
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setObjectName("drop-description")
        
        # Browse button
        browse_btn = QPushButton("📁 Browse Files")
        browse_btn.setObjectName("browse-btn")
        browse_btn.clicked.connect(self.browse_files)
        
        layout.addWidget(icon_label)
        layout.addWidget(title_label)
        layout.addWidget(desc_label)
        layout.addWidget(browse_btn)
        
        # Apply styling
        self.setStyleSheet("""
            QFrame#pdf-drop-zone {
                background: #1c1c1e;
                border: 2px dashed #3a3a3c;
                border-radius: 16px;
                min-height: 200px;
            }
            QFrame#pdf-drop-zone:hover {
                border-color: #007aff;
                background: rgba(0, 122, 255, 0.05);
            }
            QLabel#drop-icon {
                color: #8e8e93;
            }
            QLabel#drop-title {
                color: #ffffff;
            }
            QLabel#drop-description {
                color: #8e8e93;
            }
            QPushButton#browse-btn {
                background: #007aff;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                color: white;
                font-weight: bold;
                max-width: 200px;
            }
            QPushButton#browse-btn:hover {
                background: #0056cc;
            }
        """)
        
    def dragEnterEvent(self, event: QDragEnterEvent):
        """Handle drag enter event"""
        if event.mimeData().hasUrls():
            # Check if any files are PDFs
            urls = event.mimeData().urls()
            pdf_files = [url.toLocalFile() for url in urls 
                        if url.toLocalFile().lower().endswith('.pdf')]
            
            if pdf_files:
                event.acceptProposedAction()
                self.setStyleSheet(self.styleSheet() + """
                    QFrame#pdf-drop-zone {
                        border-color: #007aff !important;
                        background: rgba(0, 122, 255, 0.1) !important;
                    }
                """)
        
    def dragLeaveEvent(self, event):
        """Handle drag leave event"""
        self.setStyleSheet(self.styleSheet().replace("""
            QFrame#pdf-drop-zone {
                border-color: #007aff !important;
                background: rgba(0, 122, 255, 0.1) !important;
            }
        """, ""))
        
    def dropEvent(self, event: QDropEvent):
        """Handle drop event"""
        urls = event.mimeData().urls()
        pdf_files = [url.toLocalFile() for url in urls 
                    if url.toLocalFile().lower().endswith('.pdf')]
        
        if pdf_files:
            self.files_dropped.emit(pdf_files)
            
        # Reset styling
        self.dragLeaveEvent(event)
        
    def browse_files(self):
        """Open file browser"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "Select PDF Files",
            "",
            "PDF Files (*.pdf)"
        )
        
        if files:
            self.files_dropped.emit(files)
            
    def mousePressEvent(self, event):
        """Handle mouse press for click to browse"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.browse_files()


class PDFDocumentViewer(QFrame):
    """PDF document viewer and analysis display"""

    def __init__(self):
        super().__init__()
        self.current_document = None
        self.setObjectName("pdf-viewer")
        self.setup_ui()

    def setup_ui(self):
        """Setup document viewer UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)

        # Header
        header_layout = QHBoxLayout()

        self.doc_title = QLabel("No document selected")
        self.doc_title.setFont(QFont("Segoe UI", 16, QFont.Weight.Bold))
        self.doc_title.setObjectName("doc-title")

        self.doc_status = QLabel("")
        self.doc_status.setFont(QFont("Segoe UI", 10))
        self.doc_status.setObjectName("doc-status")

        header_layout.addWidget(self.doc_title)
        header_layout.addStretch()
        header_layout.addWidget(self.doc_status)

        # Tabs for different views
        self.tabs = QTabWidget()
        self.tabs.setObjectName("doc-tabs")

        # Overview tab
        overview_tab = self.create_overview_tab()
        self.tabs.addTab(overview_tab, "📊 Overview")

        # Content tab
        content_tab = self.create_content_tab()
        self.tabs.addTab(content_tab, "📄 Content")

        # Analysis tab
        analysis_tab = self.create_analysis_tab()
        self.tabs.addTab(analysis_tab, "🔍 Analysis")

        # Pages tab
        pages_tab = self.create_pages_tab()
        self.tabs.addTab(pages_tab, "📑 Pages")

        layout.addLayout(header_layout)
        layout.addWidget(self.tabs)

        # Apply styling
        self.setStyleSheet("""
            QFrame#pdf-viewer {
                background: #1c1c1e;
                border-radius: 12px;
                border: 1px solid #3a3a3c;
            }
            QLabel#doc-title {
                color: #ffffff;
            }
            QLabel#doc-status {
                color: #8e8e93;
            }
            QTabWidget#doc-tabs {
                background: transparent;
            }
            QTabWidget#doc-tabs::pane {
                border: 1px solid #3a3a3c;
                border-radius: 8px;
                background: #2c2c2e;
            }
            QTabWidget#doc-tabs::tab-bar {
                alignment: left;
            }
            QTabBar::tab {
                background: #2c2c2e;
                border: 1px solid #3a3a3c;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                color: #8e8e93;
            }
            QTabBar::tab:selected {
                background: #007aff;
                color: white;
            }
            QTabBar::tab:hover {
                background: #3a3a3c;
                color: #ffffff;
            }
        """)

    def create_overview_tab(self):
        """Create overview tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)

        # Document info
        info_group = QGroupBox("Document Information")
        info_group.setObjectName("info-group")
        info_layout = QGridLayout(info_group)

        self.info_labels = {}
        info_fields = [
            ("File Name:", "file_name"),
            ("File Size:", "file_size"),
            ("Pages:", "page_count"),
            ("Title:", "title"),
            ("Author:", "author"),
            ("Created:", "creation_date"),
            ("Modified:", "modification_date")
        ]

        for i, (label, key) in enumerate(info_fields):
            label_widget = QLabel(label)
            label_widget.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
            value_widget = QLabel("-")
            value_widget.setObjectName("info-value")

            info_layout.addWidget(label_widget, i, 0)
            info_layout.addWidget(value_widget, i, 1)
            self.info_labels[key] = value_widget

        # Processing status
        status_group = QGroupBox("Processing Status")
        status_group.setObjectName("status-group")
        status_layout = QVBoxLayout(status_group)

        self.processing_progress = QProgressBar()
        self.processing_progress.setObjectName("processing-progress")
        self.processing_progress.setVisible(False)

        self.processing_status = QLabel("Ready")
        self.processing_status.setObjectName("processing-status")

        status_layout.addWidget(self.processing_progress)
        status_layout.addWidget(self.processing_status)

        layout.addWidget(info_group)
        layout.addWidget(status_group)
        layout.addStretch()

        return tab

    def create_content_tab(self):
        """Create content tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)

        # Content display
        self.content_text = QTextEdit()
        self.content_text.setObjectName("content-text")
        self.content_text.setReadOnly(True)
        self.content_text.setFont(QFont("Segoe UI", 10))

        # Search in content
        search_layout = QHBoxLayout()
        search_label = QLabel("Search:")
        self.search_input = QLineEdit()
        self.search_input.setObjectName("search-input")
        self.search_input.setPlaceholderText("Search in document...")

        search_btn = QPushButton("🔍")
        search_btn.setObjectName("search-btn")
        search_btn.clicked.connect(self.search_content)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(search_btn)

        layout.addLayout(search_layout)
        layout.addWidget(self.content_text)

        return tab

    def create_analysis_tab(self):
        """Create analysis tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)

        # Statistics
        stats_group = QGroupBox("Content Statistics")
        stats_group.setObjectName("stats-group")
        stats_layout = QGridLayout(stats_group)

        self.stats_labels = {}
        stats_fields = [
            ("Word Count:", "word_count"),
            ("Character Count:", "character_count"),
            ("Line Count:", "line_count"),
            ("Language:", "language")
        ]

        for i, (label, key) in enumerate(stats_fields):
            label_widget = QLabel(label)
            label_widget.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
            value_widget = QLabel("-")
            value_widget.setObjectName("stats-value")

            stats_layout.addWidget(label_widget, i, 0)
            stats_layout.addWidget(value_widget, i, 1)
            self.stats_labels[key] = value_widget

        # Keywords
        keywords_group = QGroupBox("Top Keywords")
        keywords_group.setObjectName("keywords-group")
        keywords_layout = QVBoxLayout(keywords_group)

        self.keywords_list = QListWidget()
        self.keywords_list.setObjectName("keywords-list")
        self.keywords_list.setMaximumHeight(150)

        keywords_layout.addWidget(self.keywords_list)

        # Summary
        summary_group = QGroupBox("AI Summary")
        summary_group.setObjectName("summary-group")
        summary_layout = QVBoxLayout(summary_group)

        self.summary_text = QTextEdit()
        self.summary_text.setObjectName("summary-text")
        self.summary_text.setReadOnly(True)
        self.summary_text.setMaximumHeight(100)

        summary_layout.addWidget(self.summary_text)

        layout.addWidget(stats_group)
        layout.addWidget(keywords_group)
        layout.addWidget(summary_group)
        layout.addStretch()

        return tab

    def create_pages_tab(self):
        """Create pages tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)

        # Pages table
        self.pages_table = QTableWidget()
        self.pages_table.setObjectName("pages-table")
        self.pages_table.setColumnCount(6)
        self.pages_table.setHorizontalHeaderLabels([
            "Page", "Width", "Height", "Text Length", "Images", "Links"
        ])

        layout.addWidget(self.pages_table)

        return tab

    def load_document(self, document_data: Dict[str, Any]):
        """Load document data into viewer"""
        self.current_document = document_data

        # Update title and status
        file_name = document_data.get('file_name', 'Unknown')
        self.doc_title.setText(file_name)
        self.doc_status.setText("Loaded")

        # Update overview
        self.update_overview(document_data)

        # Update content
        self.update_content(document_data)

        # Update analysis
        self.update_analysis(document_data)

        # Update pages
        self.update_pages(document_data)

    def update_overview(self, data: Dict[str, Any]):
        """Update overview tab"""
        metadata = data.get('metadata', {})

        # Format file size
        file_size = data.get('file_size', 0)
        if file_size > 1024 * 1024:
            size_str = f"{file_size / (1024 * 1024):.1f} MB"
        elif file_size > 1024:
            size_str = f"{file_size / 1024:.1f} KB"
        else:
            size_str = f"{file_size} bytes"

        # Update info labels
        updates = {
            'file_name': data.get('file_name', '-'),
            'file_size': size_str,
            'page_count': str(metadata.get('page_count', '-')),
            'title': metadata.get('title', '-') or '-',
            'author': metadata.get('author', '-') or '-',
            'creation_date': metadata.get('creation_date', '-') or '-',
            'modification_date': metadata.get('modification_date', '-') or '-'
        }

        for key, value in updates.items():
            if key in self.info_labels:
                self.info_labels[key].setText(str(value))

    def update_content(self, data: Dict[str, Any]):
        """Update content tab"""
        content = data.get('content', {})
        text = content.get('text', '')

        # Limit text display for performance
        if len(text) > 50000:
            text = text[:50000] + "\n\n... (Content truncated for display)"

        self.content_text.setPlainText(text)

    def update_analysis(self, data: Dict[str, Any]):
        """Update analysis tab"""
        analysis = data.get('analysis', {})

        # Update statistics
        stats_updates = {
            'word_count': f"{analysis.get('word_count', 0):,}",
            'character_count': f"{analysis.get('character_count', 0):,}",
            'line_count': f"{analysis.get('line_count', 0):,}",
            'language': analysis.get('language', 'Unknown')
        }

        for key, value in stats_updates.items():
            if key in self.stats_labels:
                self.stats_labels[key].setText(str(value))

        # Update keywords
        self.keywords_list.clear()
        keywords = analysis.get('keywords', [])
        for keyword in keywords[:20]:  # Show top 20
            self.keywords_list.addItem(keyword)

        # Update summary
        summary = analysis.get('summary', 'No summary available')
        self.summary_text.setPlainText(summary)

    def update_pages(self, data: Dict[str, Any]):
        """Update pages tab"""
        pages = data.get('pages', [])

        self.pages_table.setRowCount(len(pages))

        for i, page in enumerate(pages):
            items = [
                str(page.get('page_number', i + 1)),
                f"{page.get('width', 0):.0f}",
                f"{page.get('height', 0):.0f}",
                str(page.get('text_length', 0)),
                str(page.get('image_count', 0)),
                str(page.get('link_count', 0))
            ]

            for j, item_text in enumerate(items):
                item = QTableWidgetItem(item_text)
                self.pages_table.setItem(i, j, item)

        self.pages_table.resizeColumnsToContents()

    def search_content(self):
        """Search in document content"""
        search_term = self.search_input.text().strip()
        if not search_term:
            return

        # Simple text search
        cursor = self.content_text.textCursor()
        cursor.setPosition(0)
        self.content_text.setTextCursor(cursor)

        if self.content_text.find(search_term):
            # Highlight found text
            cursor = self.content_text.textCursor()
            cursor.select(cursor.SelectionType.WordUnderCursor)
        else:
            QMessageBox.information(self, "Search", f"'{search_term}' not found in document.")

    def show_processing_progress(self, progress: int):
        """Show processing progress"""
        self.processing_progress.setVisible(True)
        self.processing_progress.setValue(progress)

        if progress < 100:
            self.processing_status.setText(f"Processing... {progress}%")
        else:
            self.processing_status.setText("Processing complete")
            self.processing_progress.setVisible(False)


class PDFProcessorWidget(QWidget):
    """Main PDF processor widget with comprehensive functionality"""

    def __init__(self, api_client=None):
        super().__init__()
        self.api_client = api_client
        self.pdf_processor = PDFProcessor()
        self.processed_documents = {}
        self.active_pdfs = []  # PDFs currently being used by AI

        self.setup_ui()
        self.setup_connections()

    def setup_ui(self):
        """Setup main PDF processor UI"""
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Create main splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.setObjectName("pdf-main-splitter")

        # Left panel - Upload and document list
        left_panel = self.create_left_panel()

        # Center panel - Document viewer
        self.document_viewer = PDFDocumentViewer()

        # Right panel - AI analysis and controls
        right_panel = self.create_right_panel()

        # Add panels to splitter
        splitter.addWidget(left_panel)
        splitter.addWidget(self.document_viewer)
        splitter.addWidget(right_panel)

        # Set splitter proportions
        splitter.setSizes([300, 600, 350])

        main_layout.addWidget(splitter)

        # Apply global styling
        self.setStyleSheet("""
            QWidget {
                background: #000000;
                color: #ffffff;
                font-family: 'Segoe UI', sans-serif;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #3a3a3c;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
                background: #2c2c2e;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 8px 0 8px;
                color: #ffffff;
            }
            QListWidget {
                background: #2c2c2e;
                border: 1px solid #3a3a3c;
                border-radius: 8px;
                padding: 4px;
            }
            QListWidget::item {
                padding: 8px;
                border-radius: 6px;
                margin: 2px;
            }
            QListWidget::item:selected {
                background: #007aff;
            }
            QListWidget::item:hover {
                background: #3a3a3c;
            }
            QTextEdit {
                background: #2c2c2e;
                border: 1px solid #3a3a3c;
                border-radius: 8px;
                padding: 8px;
                color: #ffffff;
            }
            QLineEdit {
                background: #2c2c2e;
                border: 1px solid #3a3a3c;
                border-radius: 6px;
                padding: 6px 12px;
                color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #007aff;
            }
            QPushButton {
                background: #007aff;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #0056cc;
            }
            QPushButton:pressed {
                background: #004499;
            }
            QProgressBar {
                border: 1px solid #3a3a3c;
                border-radius: 4px;
                background: #2c2c2e;
                text-align: center;
            }
            QProgressBar::chunk {
                background: #007aff;
                border-radius: 3px;
            }
            QTableWidget {
                background: #2c2c2e;
                border: 1px solid #3a3a3c;
                border-radius: 8px;
                gridline-color: #3a3a3c;
            }
            QTableWidget::item {
                padding: 4px 8px;
                border: none;
            }
            QTableWidget::item:selected {
                background: #007aff;
            }
            QHeaderView::section {
                background: #1c1c1e;
                border: 1px solid #3a3a3c;
                padding: 6px;
                font-weight: bold;
            }
        """)

    def create_left_panel(self):
        """Create left panel with upload and document list"""
        panel = QFrame()
        panel.setObjectName("left-panel")
        panel.setFixedWidth(300)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)

        # Header
        header = QLabel("📄 PDF Documents")
        header.setFont(QFont("Segoe UI", 16, QFont.Weight.Bold))
        header.setObjectName("panel-header")

        # Upload area
        upload_group = QGroupBox("Upload Documents")
        upload_layout = QVBoxLayout(upload_group)

        self.drop_zone = PDFDropZone()
        upload_layout.addWidget(self.drop_zone)

        # Processing options
        options_layout = QGridLayout()

        self.enable_ocr = QCheckBox("Enable OCR")
        self.enable_ocr.setEnabled(OCR_AVAILABLE)
        self.enable_ocr.setToolTip("Extract text from images using OCR")

        self.extract_images = QCheckBox("Extract Images")
        self.extract_images.setChecked(True)

        self.deep_analysis = QCheckBox("Deep Analysis")
        self.deep_analysis.setChecked(True)
        self.deep_analysis.setToolTip("Perform comprehensive content analysis")

        options_layout.addWidget(self.enable_ocr, 0, 0)
        options_layout.addWidget(self.extract_images, 0, 1)
        options_layout.addWidget(self.deep_analysis, 1, 0, 1, 2)

        upload_layout.addLayout(options_layout)

        # Document list
        docs_group = QGroupBox("Processed Documents")
        docs_layout = QVBoxLayout(docs_group)

        # Search documents
        search_layout = QHBoxLayout()
        self.doc_search = QLineEdit()
        self.doc_search.setPlaceholderText("Search documents...")
        search_btn = QPushButton("🔍")
        search_btn.setFixedWidth(40)
        search_btn.clicked.connect(self.search_documents)

        search_layout.addWidget(self.doc_search)
        search_layout.addWidget(search_btn)

        # Documents list
        self.documents_list = QListWidget()
        self.documents_list.setObjectName("documents-list")
        self.documents_list.itemClicked.connect(self.select_document)

        docs_layout.addLayout(search_layout)
        docs_layout.addWidget(self.documents_list)

        # Active PDFs indicator
        active_group = QGroupBox("Active in AI")
        active_layout = QVBoxLayout(active_group)

        self.active_pdfs_list = QListWidget()
        self.active_pdfs_list.setObjectName("active-pdfs-list")
        self.active_pdfs_list.setMaximumHeight(100)

        active_layout.addWidget(self.active_pdfs_list)

        layout.addWidget(header)
        layout.addWidget(upload_group)
        layout.addWidget(docs_group)
        layout.addWidget(active_group)

        return panel

    def create_right_panel(self):
        """Create right panel with AI analysis and controls"""
        panel = QFrame()
        panel.setObjectName("right-panel")
        panel.setFixedWidth(350)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)

        # AI Analysis section
        ai_group = QGroupBox("AI Analysis")
        ai_layout = QVBoxLayout(ai_group)

        # Quick analysis buttons
        analysis_buttons = QGridLayout()

        summarize_btn = QPushButton("📝 Summarize")
        summarize_btn.clicked.connect(self.ai_summarize)

        extract_key_btn = QPushButton("🔑 Key Points")
        extract_key_btn.clicked.connect(self.ai_extract_key_points)

        translate_btn = QPushButton("🌐 Translate")
        translate_btn.clicked.connect(self.ai_translate)

        compare_btn = QPushButton("⚖️ Compare")
        compare_btn.clicked.connect(self.ai_compare_documents)

        analysis_buttons.addWidget(summarize_btn, 0, 0)
        analysis_buttons.addWidget(extract_key_btn, 0, 1)
        analysis_buttons.addWidget(translate_btn, 1, 0)
        analysis_buttons.addWidget(compare_btn, 1, 1)

        # AI response area
        self.ai_response = QTextEdit()
        self.ai_response.setObjectName("ai-response")
        self.ai_response.setPlaceholderText("AI analysis results will appear here...")
        self.ai_response.setMaximumHeight(200)

        ai_layout.addLayout(analysis_buttons)
        ai_layout.addWidget(self.ai_response)

        # Document actions
        actions_group = QGroupBox("Document Actions")
        actions_layout = QVBoxLayout(actions_group)

        export_btn = QPushButton("📤 Export Analysis")
        export_btn.clicked.connect(self.export_analysis)

        share_btn = QPushButton("🔗 Share with AI")
        share_btn.clicked.connect(self.share_with_ai)

        delete_btn = QPushButton("🗑️ Remove Document")
        delete_btn.clicked.connect(self.remove_document)
        delete_btn.setStyleSheet("QPushButton { background: #ff3b30; }")

        actions_layout.addWidget(export_btn)
        actions_layout.addWidget(share_btn)
        actions_layout.addWidget(delete_btn)

        # Processing queue
        queue_group = QGroupBox("Processing Queue")
        queue_layout = QVBoxLayout(queue_group)

        self.queue_list = QListWidget()
        self.queue_list.setObjectName("queue-list")
        self.queue_list.setMaximumHeight(100)

        queue_layout.addWidget(self.queue_list)

        layout.addWidget(ai_group)
        layout.addWidget(actions_group)
        layout.addWidget(queue_group)
        layout.addStretch()

        return panel

    def setup_connections(self):
        """Setup signal connections"""
        # Drop zone connections
        self.drop_zone.files_dropped.connect(self.process_files)

        # PDF processor connections
        self.pdf_processor.processing_started.connect(self.on_processing_started)
        self.pdf_processor.processing_progress.connect(self.on_processing_progress)
        self.pdf_processor.processing_completed.connect(self.on_processing_completed)
        self.pdf_processor.processing_error.connect(self.on_processing_error)

        # Search connection
        self.doc_search.textChanged.connect(self.filter_documents)

    def process_files(self, file_paths: List[str]):
        """Process dropped/selected PDF files"""
        for file_path in file_paths:
            if os.path.exists(file_path) and file_path.lower().endswith('.pdf'):
                # Check file size (limit to 100MB)
                file_size = os.path.getsize(file_path)
                if file_size > 100 * 1024 * 1024:
                    QMessageBox.warning(
                        self,
                        "File Too Large",
                        f"File {os.path.basename(file_path)} is too large (max 100MB)"
                    )
                    continue

                # Get processing options
                options = {
                    'enable_ocr': self.enable_ocr.isChecked(),
                    'extract_images': self.extract_images.isChecked(),
                    'deep_analysis': self.deep_analysis.isChecked()
                }

                # Add to queue and start processing
                self.add_to_queue(file_path)
                self.pdf_processor.process_pdf(file_path, options)

    def add_to_queue(self, file_path: str):
        """Add file to processing queue display"""
        file_name = os.path.basename(file_path)
        item = QListWidgetItem(f"⏳ {file_name}")
        item.setData(Qt.ItemDataRole.UserRole, file_path)
        self.queue_list.addItem(item)

    def remove_from_queue(self, file_path: str):
        """Remove file from processing queue display"""
        for i in range(self.queue_list.count()):
            item = self.queue_list.item(i)
            if item.data(Qt.ItemDataRole.UserRole) == file_path:
                self.queue_list.takeItem(i)
                break

    def on_processing_started(self, file_path: str):
        """Handle processing started"""
        file_name = os.path.basename(file_path)

        # Update queue item
        for i in range(self.queue_list.count()):
            item = self.queue_list.item(i)
            if item.data(Qt.ItemDataRole.UserRole) == file_path:
                item.setText(f"🔄 {file_name}")
                break

    def on_processing_progress(self, file_path: str, progress: int):
        """Handle processing progress"""
        file_name = os.path.basename(file_path)

        # Update queue item
        for i in range(self.queue_list.count()):
            item = self.queue_list.item(i)
            if item.data(Qt.ItemDataRole.UserRole) == file_path:
                item.setText(f"🔄 {file_name} ({progress}%)")
                break

        # Update document viewer if this is the current document
        if (hasattr(self.document_viewer, 'current_document') and
            self.document_viewer.current_document and
            self.document_viewer.current_document.get('file_path') == file_path):
            self.document_viewer.show_processing_progress(progress)

    def on_processing_completed(self, file_path: str, results: Dict[str, Any]):
        """Handle processing completed"""
        file_name = os.path.basename(file_path)

        # Store results
        self.processed_documents[file_path] = results

        # Remove from queue
        self.remove_from_queue(file_path)

        # Add to documents list
        item = QListWidgetItem(f"📄 {file_name}")
        item.setData(Qt.ItemDataRole.UserRole, file_path)
        item.setToolTip(f"Processed: {results.get('processed_at', 'Unknown')}")
        self.documents_list.addItem(item)

        # Auto-select if first document
        if self.documents_list.count() == 1:
            self.documents_list.setCurrentItem(item)
            self.select_document(item)

        # Show notification
        QMessageBox.information(
            self,
            "Processing Complete",
            f"Successfully processed {file_name}"
        )

    def on_processing_error(self, file_path: str, error: str):
        """Handle processing error"""
        file_name = os.path.basename(file_path)

        # Remove from queue
        self.remove_from_queue(file_path)

        # Show error
        QMessageBox.critical(
            self,
            "Processing Error",
            f"Failed to process {file_name}:\n{error}"
        )

    def select_document(self, item: QListWidgetItem):
        """Select and display document"""
        file_path = item.data(Qt.ItemDataRole.UserRole)

        if file_path in self.processed_documents:
            document_data = self.processed_documents[file_path]
            self.document_viewer.load_document(document_data)

    def filter_documents(self):
        """Filter documents list based on search"""
        search_text = self.doc_search.text().lower()

        for i in range(self.documents_list.count()):
            item = self.documents_list.item(i)
            item_text = item.text().lower()
            item.setHidden(search_text not in item_text)

    def search_documents(self):
        """Perform document search"""
        self.filter_documents()

    def ai_summarize(self):
        """AI summarize current document"""
        if not self.document_viewer.current_document:
            QMessageBox.information(self, "No Document", "Please select a document first.")
            return

        content = self.document_viewer.current_document.get('content', {}).get('text', '')
        if not content:
            QMessageBox.information(self, "No Content", "Document has no extractable text content.")
            return

        # Simulate AI processing
        self.ai_response.setPlainText("🤖 Generating summary...\n\nPlease wait while I analyze the document content.")

        # In a real implementation, this would call the AI API
        QTimer.singleShot(2000, lambda: self.ai_response.setPlainText(
            "📝 Document Summary:\n\n" +
            "This document contains important information about the topic. " +
            "Key findings include various aspects that are discussed in detail. " +
            "The document provides comprehensive coverage of the subject matter.\n\n" +
            "Note: This is a placeholder summary. Integrate with your AI service for actual analysis."
        ))

    def ai_extract_key_points(self):
        """AI extract key points from current document"""
        if not self.document_viewer.current_document:
            QMessageBox.information(self, "No Document", "Please select a document first.")
            return

        self.ai_response.setPlainText("🔑 Extracting key points...\n\nAnalyzing document structure and content.")

        QTimer.singleShot(2000, lambda: self.ai_response.setPlainText(
            "🔑 Key Points:\n\n" +
            "• Main topic: [Document subject]\n" +
            "• Important findings: [Key discoveries]\n" +
            "• Recommendations: [Suggested actions]\n" +
            "• Conclusions: [Final thoughts]\n\n" +
            "Note: Integrate with your AI service for actual key point extraction."
        ))

    def ai_translate(self):
        """AI translate current document"""
        if not self.document_viewer.current_document:
            QMessageBox.information(self, "No Document", "Please select a document first.")
            return

        self.ai_response.setPlainText("🌐 Translation in progress...\n\nDetecting language and translating content.")

    def ai_compare_documents(self):
        """AI compare multiple documents"""
        if len(self.processed_documents) < 2:
            QMessageBox.information(self, "Need More Documents", "Please upload at least 2 documents to compare.")
            return

        self.ai_response.setPlainText("⚖️ Comparing documents...\n\nAnalyzing similarities and differences.")

    def export_analysis(self):
        """Export document analysis"""
        if not self.document_viewer.current_document:
            QMessageBox.information(self, "No Document", "Please select a document first.")
            return

        filename, _ = QFileDialog.getSaveFileName(
            self,
            "Export Analysis",
            f"analysis_{self.document_viewer.current_document.get('file_name', 'document')}.json",
            "JSON Files (*.json)"
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.document_viewer.current_document, f, indent=2, default=str)
                QMessageBox.information(self, "Export Complete", f"Analysis exported to {filename}")
            except Exception as e:
                QMessageBox.critical(self, "Export Error", f"Failed to export analysis: {e}")

    def share_with_ai(self):
        """Share document with AI chat"""
        if not self.document_viewer.current_document:
            QMessageBox.information(self, "No Document", "Please select a document first.")
            return

        file_path = self.document_viewer.current_document.get('file_path')
        file_name = self.document_viewer.current_document.get('file_name')

        if file_path not in self.active_pdfs:
            self.active_pdfs.append(file_path)

            # Add to active PDFs list
            item = QListWidgetItem(f"🔗 {file_name}")
            item.setData(Qt.ItemDataRole.UserRole, file_path)
            self.active_pdfs_list.addItem(item)

            QMessageBox.information(self, "Shared", f"{file_name} is now available to the AI assistant.")
        else:
            QMessageBox.information(self, "Already Shared", f"{file_name} is already shared with the AI.")

    def remove_document(self):
        """Remove selected document"""
        current_item = self.documents_list.currentItem()
        if not current_item:
            QMessageBox.information(self, "No Selection", "Please select a document to remove.")
            return

        file_path = current_item.data(Qt.ItemDataRole.UserRole)
        file_name = os.path.basename(file_path)

        reply = QMessageBox.question(
            self,
            "Confirm Removal",
            f"Are you sure you want to remove {file_name} from the processed documents?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Remove from processed documents
            if file_path in self.processed_documents:
                del self.processed_documents[file_path]

            # Remove from active PDFs
            if file_path in self.active_pdfs:
                self.active_pdfs.remove(file_path)

                # Remove from active PDFs list
                for i in range(self.active_pdfs_list.count()):
                    item = self.active_pdfs_list.item(i)
                    if item.data(Qt.ItemDataRole.UserRole) == file_path:
                        self.active_pdfs_list.takeItem(i)
                        break

            # Remove from documents list
            row = self.documents_list.row(current_item)
            self.documents_list.takeItem(row)

            # Clear viewer if this was the current document
            if (hasattr(self.document_viewer, 'current_document') and
                self.document_viewer.current_document and
                self.document_viewer.current_document.get('file_path') == file_path):
                self.document_viewer.current_document = None
                self.document_viewer.doc_title.setText("No document selected")

    def get_active_pdfs(self):
        """Get list of PDFs currently active in AI"""
        return [self.processed_documents[path] for path in self.active_pdfs
                if path in self.processed_documents]

    def update_data(self):
        """Update widget data (for consistency with other widgets)"""
        pass
