"""
Settings Widget for Offline Sentinel PyQt6 Application
Application configuration and preferences interface
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, 
    QFrame, QPushButton, QScrollArea, QComboBox, QLineEdit,
    QCheckBox, QSpinBox, QTabWidget, QGroupBox, QSlider,
    QFileDialog, QMessageBox, QTextEdit
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
from typing import Dict, Any


class GeneralSettingsWidget(QWidget):
    """General application settings"""
    
    settings_changed = pyqtSignal(str, object)
    
    def __init__(self, config_manager):
        super().__init__()
        self.config_manager = config_manager
        self.setup_ui()
        self.load_settings()
        
    def setup_ui(self):
        """Setup the general settings UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Application settings group
        app_group = QGroupBox("Application Settings")
        app_layout = QGridLayout(app_group)
        
        # Theme selection
        theme_label = QLabel("Theme:")
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["Dark", "Light"])
        self.theme_combo.currentTextChanged.connect(
            lambda value: self.settings_changed.emit('ui.theme', value.lower())
        )
        
        # Update interval
        interval_label = QLabel("Update Interval (seconds):")
        self.interval_spin = QSpinBox()
        self.interval_spin.setRange(1, 60)
        self.interval_spin.setValue(5)
        self.interval_spin.valueChanged.connect(
            lambda value: self.settings_changed.emit('ui.update_interval', value)
        )
        
        # Minimize to tray
        self.tray_checkbox = QCheckBox("Minimize to system tray")
        self.tray_checkbox.toggled.connect(
            lambda checked: self.settings_changed.emit('ui.minimize_to_tray', checked)
        )
        
        # Start minimized
        self.start_min_checkbox = QCheckBox("Start minimized")
        self.start_min_checkbox.toggled.connect(
            lambda checked: self.settings_changed.emit('ui.start_minimized', checked)
        )
        
        # Show notifications
        self.notifications_checkbox = QCheckBox("Show desktop notifications")
        self.notifications_checkbox.toggled.connect(
            lambda checked: self.settings_changed.emit('ui.show_notifications', checked)
        )
        
        app_layout.addWidget(theme_label, 0, 0)
        app_layout.addWidget(self.theme_combo, 0, 1)
        app_layout.addWidget(interval_label, 1, 0)
        app_layout.addWidget(self.interval_spin, 1, 1)
        app_layout.addWidget(self.tray_checkbox, 2, 0, 1, 2)
        app_layout.addWidget(self.start_min_checkbox, 3, 0, 1, 2)
        app_layout.addWidget(self.notifications_checkbox, 4, 0, 1, 2)
        
        # API settings group
        api_group = QGroupBox("API Settings")
        api_layout = QGridLayout(api_group)
        
        # Base URL
        url_label = QLabel("Backend URL:")
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("http://localhost:8001")
        self.url_input.textChanged.connect(
            lambda text: self.settings_changed.emit('api.base_url', text)
        )
        
        # Timeout
        timeout_label = QLabel("Request Timeout (seconds):")
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(1, 60)
        self.timeout_spin.setValue(10)
        self.timeout_spin.valueChanged.connect(
            lambda value: self.settings_changed.emit('api.timeout', value)
        )
        
        # Retry attempts
        retry_label = QLabel("Retry Attempts:")
        self.retry_spin = QSpinBox()
        self.retry_spin.setRange(0, 10)
        self.retry_spin.setValue(3)
        self.retry_spin.valueChanged.connect(
            lambda value: self.settings_changed.emit('api.retry_attempts', value)
        )
        
        api_layout.addWidget(url_label, 0, 0)
        api_layout.addWidget(self.url_input, 0, 1)
        api_layout.addWidget(timeout_label, 1, 0)
        api_layout.addWidget(self.timeout_spin, 1, 1)
        api_layout.addWidget(retry_label, 2, 0)
        api_layout.addWidget(self.retry_spin, 2, 1)
        
        layout.addWidget(app_group)
        layout.addWidget(api_group)
        layout.addStretch()
        
    def load_settings(self):
        """Load settings from config manager"""
        # Load UI settings
        theme = self.config_manager.get('ui.theme', 'dark')
        self.theme_combo.setCurrentText(theme.title())
        
        interval = self.config_manager.get('ui.update_interval', 5)
        self.interval_spin.setValue(interval)
        
        minimize_to_tray = self.config_manager.get('ui.minimize_to_tray', True)
        self.tray_checkbox.setChecked(minimize_to_tray)
        
        start_minimized = self.config_manager.get('ui.start_minimized', False)
        self.start_min_checkbox.setChecked(start_minimized)
        
        show_notifications = self.config_manager.get('ui.show_notifications', True)
        self.notifications_checkbox.setChecked(show_notifications)
        
        # Load API settings
        base_url = self.config_manager.get('api.base_url', 'http://localhost:8001')
        self.url_input.setText(base_url)
        
        timeout = self.config_manager.get('api.timeout', 10)
        self.timeout_spin.setValue(timeout)
        
        retry_attempts = self.config_manager.get('api.retry_attempts', 3)
        self.retry_spin.setValue(retry_attempts)


class AgentSettingsWidget(QWidget):
    """Agent configuration settings"""
    
    settings_changed = pyqtSignal(str, object)
    
    def __init__(self, config_manager):
        super().__init__()
        self.config_manager = config_manager
        self.setup_ui()
        self.load_settings()
        
    def setup_ui(self):
        """Setup the agent settings UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Agent behavior group
        behavior_group = QGroupBox("Agent Behavior")
        behavior_layout = QGridLayout(behavior_group)
        
        # Auto start
        self.auto_start_checkbox = QCheckBox("Auto-start agents on application launch")
        self.auto_start_checkbox.toggled.connect(
            lambda checked: self.settings_changed.emit('agents.auto_start', checked)
        )
        
        # Default mode
        mode_label = QLabel("Default Agent Mode:")
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["Autonomous", "Collaborative", "Supervised", "Manual"])
        self.mode_combo.currentTextChanged.connect(
            lambda value: self.settings_changed.emit('agents.default_mode', value.lower())
        )
        
        # Performance level
        perf_label = QLabel("Performance Level:")
        self.perf_combo = QComboBox()
        self.perf_combo.addItems(["Eco", "Balanced", "High Performance", "Maximum"])
        self.perf_combo.currentTextChanged.connect(
            lambda value: self.settings_changed.emit('agents.performance_level', value.lower().replace(' ', '_'))
        )
        
        behavior_layout.addWidget(self.auto_start_checkbox, 0, 0, 1, 2)
        behavior_layout.addWidget(mode_label, 1, 0)
        behavior_layout.addWidget(self.mode_combo, 1, 1)
        behavior_layout.addWidget(perf_label, 2, 0)
        behavior_layout.addWidget(self.perf_combo, 2, 1)
        
        # Logging group
        logging_group = QGroupBox("Agent Logging")
        logging_layout = QGridLayout(logging_group)
        
        # Enable logging
        self.logging_checkbox = QCheckBox("Enable agent logging")
        self.logging_checkbox.toggled.connect(
            lambda checked: self.settings_changed.emit('agents.enable_logging', checked)
        )
        
        # Log level
        log_level_label = QLabel("Log Level:")
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        self.log_level_combo.currentTextChanged.connect(
            lambda value: self.settings_changed.emit('agents.log_level', value)
        )
        
        logging_layout.addWidget(self.logging_checkbox, 0, 0, 1, 2)
        logging_layout.addWidget(log_level_label, 1, 0)
        logging_layout.addWidget(self.log_level_combo, 1, 1)
        
        layout.addWidget(behavior_group)
        layout.addWidget(logging_group)
        layout.addStretch()
        
    def load_settings(self):
        """Load agent settings from config manager"""
        auto_start = self.config_manager.get('agents.auto_start', True)
        self.auto_start_checkbox.setChecked(auto_start)
        
        mode = self.config_manager.get('agents.default_mode', 'autonomous')
        self.mode_combo.setCurrentText(mode.title())
        
        perf_level = self.config_manager.get('agents.performance_level', 'balanced')
        perf_display = perf_level.replace('_', ' ').title()
        self.perf_combo.setCurrentText(perf_display)
        
        enable_logging = self.config_manager.get('agents.enable_logging', True)
        self.logging_checkbox.setChecked(enable_logging)
        
        log_level = self.config_manager.get('agents.log_level', 'INFO')
        self.log_level_combo.setCurrentText(log_level)


class MonitoringSettingsWidget(QWidget):
    """Monitoring and alerting settings"""
    
    settings_changed = pyqtSignal(str, object)
    
    def __init__(self, config_manager):
        super().__init__()
        self.config_manager = config_manager
        self.setup_ui()
        self.load_settings()
        
    def setup_ui(self):
        """Setup the monitoring settings UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Monitoring group
        monitoring_group = QGroupBox("Monitoring Settings")
        monitoring_layout = QGridLayout(monitoring_group)
        
        # Enable live updates
        self.live_updates_checkbox = QCheckBox("Enable live updates")
        self.live_updates_checkbox.toggled.connect(
            lambda checked: self.settings_changed.emit('monitoring.enable_live_updates', checked)
        )
        
        # Update frequency
        freq_label = QLabel("Update Frequency (seconds):")
        self.freq_spin = QSpinBox()
        self.freq_spin.setRange(1, 60)
        self.freq_spin.setValue(5)
        self.freq_spin.valueChanged.connect(
            lambda value: self.settings_changed.emit('monitoring.update_frequency', value)
        )
        
        # Show performance metrics
        self.metrics_checkbox = QCheckBox("Show performance metrics")
        self.metrics_checkbox.toggled.connect(
            lambda checked: self.settings_changed.emit('monitoring.show_performance_metrics', checked)
        )
        
        monitoring_layout.addWidget(self.live_updates_checkbox, 0, 0, 1, 2)
        monitoring_layout.addWidget(freq_label, 1, 0)
        monitoring_layout.addWidget(self.freq_spin, 1, 1)
        monitoring_layout.addWidget(self.metrics_checkbox, 2, 0, 1, 2)
        
        # Alert thresholds group
        thresholds_group = QGroupBox("Alert Thresholds")
        thresholds_layout = QGridLayout(thresholds_group)
        
        # CPU threshold
        cpu_label = QLabel("CPU Usage (%):")
        self.cpu_slider = QSlider(Qt.Orientation.Horizontal)
        self.cpu_slider.setRange(50, 100)
        self.cpu_slider.setValue(80)
        self.cpu_value_label = QLabel("80%")
        self.cpu_slider.valueChanged.connect(self.update_cpu_threshold)
        
        # Memory threshold
        memory_label = QLabel("Memory Usage (%):")
        self.memory_slider = QSlider(Qt.Orientation.Horizontal)
        self.memory_slider.setRange(50, 100)
        self.memory_slider.setValue(85)
        self.memory_value_label = QLabel("85%")
        self.memory_slider.valueChanged.connect(self.update_memory_threshold)
        
        # Disk threshold
        disk_label = QLabel("Disk Usage (%):")
        self.disk_slider = QSlider(Qt.Orientation.Horizontal)
        self.disk_slider.setRange(50, 100)
        self.disk_slider.setValue(90)
        self.disk_value_label = QLabel("90%")
        self.disk_slider.valueChanged.connect(self.update_disk_threshold)
        
        thresholds_layout.addWidget(cpu_label, 0, 0)
        thresholds_layout.addWidget(self.cpu_slider, 0, 1)
        thresholds_layout.addWidget(self.cpu_value_label, 0, 2)
        thresholds_layout.addWidget(memory_label, 1, 0)
        thresholds_layout.addWidget(self.memory_slider, 1, 1)
        thresholds_layout.addWidget(self.memory_value_label, 1, 2)
        thresholds_layout.addWidget(disk_label, 2, 0)
        thresholds_layout.addWidget(self.disk_slider, 2, 1)
        thresholds_layout.addWidget(self.disk_value_label, 2, 2)
        
        layout.addWidget(monitoring_group)
        layout.addWidget(thresholds_group)
        layout.addStretch()
        
    def update_cpu_threshold(self, value):
        """Update CPU threshold"""
        self.cpu_value_label.setText(f"{value}%")
        self.settings_changed.emit('monitoring.alert_thresholds.cpu_usage', value)
        
    def update_memory_threshold(self, value):
        """Update memory threshold"""
        self.memory_value_label.setText(f"{value}%")
        self.settings_changed.emit('monitoring.alert_thresholds.memory_usage', value)
        
    def update_disk_threshold(self, value):
        """Update disk threshold"""
        self.disk_value_label.setText(f"{value}%")
        self.settings_changed.emit('monitoring.alert_thresholds.disk_usage', value)
        
    def load_settings(self):
        """Load monitoring settings from config manager"""
        live_updates = self.config_manager.get('monitoring.enable_live_updates', True)
        self.live_updates_checkbox.setChecked(live_updates)
        
        frequency = self.config_manager.get('monitoring.update_frequency', 5)
        self.freq_spin.setValue(frequency)
        
        show_metrics = self.config_manager.get('monitoring.show_performance_metrics', True)
        self.metrics_checkbox.setChecked(show_metrics)
        
        # Load thresholds
        cpu_threshold = self.config_manager.get('monitoring.alert_thresholds.cpu_usage', 80)
        self.cpu_slider.setValue(cpu_threshold)
        self.cpu_value_label.setText(f"{cpu_threshold}%")
        
        memory_threshold = self.config_manager.get('monitoring.alert_thresholds.memory_usage', 85)
        self.memory_slider.setValue(memory_threshold)
        self.memory_value_label.setText(f"{memory_threshold}%")
        
        disk_threshold = self.config_manager.get('monitoring.alert_thresholds.disk_usage', 90)
        self.disk_slider.setValue(disk_threshold)
        self.disk_value_label.setText(f"{disk_threshold}%")


class SettingsWidget(QWidget):
    """Main settings widget"""
    
    def __init__(self, config_manager):
        super().__init__()
        self.config_manager = config_manager
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the settings widget UI"""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)
        
        # Header
        header_layout = QHBoxLayout()
        
        title = QLabel("⚙️ Settings & Configuration")
        title.setFont(QFont("Arial", 20, QFont.Weight.Bold))
        
        # Action buttons
        reset_btn = QPushButton("🔄 Reset to Defaults")
        reset_btn.setObjectName("warning")
        reset_btn.clicked.connect(self.reset_settings)
        
        export_btn = QPushButton("📤 Export Config")
        export_btn.clicked.connect(self.export_config)
        
        import_btn = QPushButton("📥 Import Config")
        import_btn.clicked.connect(self.import_config)
        
        header_layout.addWidget(title)
        header_layout.addStretch()
        header_layout.addWidget(reset_btn)
        header_layout.addWidget(export_btn)
        header_layout.addWidget(import_btn)
        
        # Settings tabs
        self.settings_tabs = QTabWidget()
        
        # General settings
        self.general_widget = GeneralSettingsWidget(self.config_manager)
        self.general_widget.settings_changed.connect(self.handle_setting_change)
        self.settings_tabs.addTab(self.general_widget, "General")
        
        # Agent settings
        self.agent_widget = AgentSettingsWidget(self.config_manager)
        self.agent_widget.settings_changed.connect(self.handle_setting_change)
        self.settings_tabs.addTab(self.agent_widget, "Agents")
        
        # Monitoring settings
        self.monitoring_widget = MonitoringSettingsWidget(self.config_manager)
        self.monitoring_widget.settings_changed.connect(self.handle_setting_change)
        self.settings_tabs.addTab(self.monitoring_widget, "Monitoring")
        
        # Add to main layout
        main_layout.addLayout(header_layout)
        main_layout.addWidget(self.settings_tabs)
        
    def handle_setting_change(self, key: str, value):
        """Handle setting changes"""
        self.config_manager.set(key, value)
        print(f"Setting changed: {key} = {value}")
        
    def reset_settings(self):
        """Reset all settings to defaults"""
        reply = QMessageBox.question(
            self, "Reset Settings",
            "Are you sure you want to reset all settings to their default values?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.config_manager.reset_to_defaults()
            
            # Reload all settings widgets
            self.general_widget.load_settings()
            self.agent_widget.load_settings()
            self.monitoring_widget.load_settings()
            
            QMessageBox.information(self, "Settings Reset", "All settings have been reset to defaults.")
            
    def export_config(self):
        """Export configuration to file"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Configuration", "sentinel_config.json",
            "JSON Files (*.json);;All Files (*.*)"
        )
        
        if file_path:
            try:
                self.config_manager.export_config(file_path)
                QMessageBox.information(self, "Export Successful", f"Configuration exported to {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Export Failed", f"Failed to export configuration: {e}")
                
    def import_config(self):
        """Import configuration from file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Import Configuration", "",
            "JSON Files (*.json);;All Files (*.*)"
        )
        
        if file_path:
            try:
                self.config_manager.import_config(file_path)
                
                # Reload all settings widgets
                self.general_widget.load_settings()
                self.agent_widget.load_settings()
                self.monitoring_widget.load_settings()
                
                QMessageBox.information(self, "Import Successful", f"Configuration imported from {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Import Failed", f"Failed to import configuration: {e}")
                
    def update_data(self):
        """Update settings data (placeholder for consistency)"""
        pass
