"""
Research Widget for Offline Sentinel PyQt6 Application
Research task management and results interface
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, 
    QFrame, QPushButton, QScrollArea, QComboBox, QTextEdit,
    QLineEdit, QTabWidget, QListWidget, QListWidgetItem,
    QProgressBar, QFileDialog, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont
from typing import Dict, Any, List


class ResearchTaskWidget(QFrame):
    """Widget for creating new research tasks"""
    
    task_started = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.setObjectName("card")
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the research task UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Header
        header = QLabel("🔬 Start New Research Task")
        header.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(header)
        
        # Task type selection
        type_layout = QHBoxLayout()
        type_label = QLabel("Task Type:")
        type_label.setObjectName("muted")
        type_label.setFixedWidth(100)
        
        self.task_type_combo = QComboBox()
        self.task_type_combo.addItems([
            "Security Analysis",
            "Threat Intelligence",
            "Vulnerability Assessment",
            "Code Review",
            "Network Analysis",
            "Malware Analysis",
            "Performance Analysis"
        ])
        
        type_layout.addWidget(type_label)
        type_layout.addWidget(self.task_type_combo)
        type_layout.addStretch()
        
        # Target input
        target_layout = QHBoxLayout()
        target_label = QLabel("Target:")
        target_label.setObjectName("muted")
        target_label.setFixedWidth(100)
        
        self.target_input = QLineEdit()
        self.target_input.setPlaceholderText("Enter target (file, URL, system, etc.)")
        
        browse_btn = QPushButton("📁 Browse")
        browse_btn.clicked.connect(self.browse_target)
        
        target_layout.addWidget(target_label)
        target_layout.addWidget(self.target_input)
        target_layout.addWidget(browse_btn)
        
        # Depth selection
        depth_layout = QHBoxLayout()
        depth_label = QLabel("Depth:")
        depth_label.setObjectName("muted")
        depth_label.setFixedWidth(100)
        
        self.depth_combo = QComboBox()
        self.depth_combo.addItems(["Quick", "Standard", "Deep", "Comprehensive"])
        self.depth_combo.setCurrentText("Standard")
        
        depth_layout.addWidget(depth_label)
        depth_layout.addWidget(self.depth_combo)
        depth_layout.addStretch()
        
        # Priority selection
        priority_layout = QHBoxLayout()
        priority_label = QLabel("Priority:")
        priority_label.setObjectName("muted")
        priority_label.setFixedWidth(100)
        
        self.priority_combo = QComboBox()
        self.priority_combo.addItems(["Low", "Medium", "High", "Critical"])
        self.priority_combo.setCurrentText("Medium")
        
        priority_layout.addWidget(priority_label)
        priority_layout.addWidget(self.priority_combo)
        priority_layout.addStretch()
        
        # Start button
        start_btn = QPushButton("🚀 Start Research Task")
        start_btn.setObjectName("success")
        start_btn.clicked.connect(self.start_task)
        
        # Add all layouts
        layout.addLayout(type_layout)
        layout.addLayout(target_layout)
        layout.addLayout(depth_layout)
        layout.addLayout(priority_layout)
        layout.addWidget(start_btn)
        
    def browse_target(self):
        """Browse for target file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select Target File", "", "All Files (*.*)"
        )
        if file_path:
            self.target_input.setText(file_path)
            
    def start_task(self):
        """Start the research task"""
        task_data = {
            'task_type': self.task_type_combo.currentText().lower().replace(' ', '_'),
            'target': self.target_input.text(),
            'depth': self.depth_combo.currentText().lower(),
            'priority': self.priority_combo.currentText().lower()
        }
        
        if not task_data['target']:
            QMessageBox.warning(self, "Warning", "Please specify a target for the research task.")
            return
            
        self.task_started.emit(task_data)


class ResearchResultsWidget(QFrame):
    """Widget for displaying research results"""
    
    def __init__(self):
        super().__init__()
        self.setObjectName("card")
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the research results UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Header
        header_layout = QHBoxLayout()
        
        header = QLabel("📊 Research Results")
        header.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        
        export_btn = QPushButton("📤 Export Results")
        export_btn.clicked.connect(self.export_results)
        
        header_layout.addWidget(header)
        header_layout.addStretch()
        header_layout.addWidget(export_btn)
        
        # Results tabs
        self.results_tabs = QTabWidget()
        
        # Summary tab
        summary_widget = QWidget()
        summary_layout = QVBoxLayout(summary_widget)
        
        self.summary_text = QTextEdit()
        self.summary_text.setReadOnly(True)
        self.summary_text.setPlainText(
            "Research Task Summary\n"
            "====================\n\n"
            "Task: Security Analysis\n"
            "Target: all_files\n"
            "Status: Completed\n"
            "Duration: 2m 34s\n\n"
            "Findings:\n"
            "- 0 Critical vulnerabilities\n"
            "- 2 Medium risk issues\n"
            "- 5 Low priority recommendations\n\n"
            "Overall Security Score: 87/100"
        )
        
        summary_layout.addWidget(self.summary_text)
        self.results_tabs.addTab(summary_widget, "Summary")
        
        # Detailed results tab
        details_widget = QWidget()
        details_layout = QVBoxLayout(details_widget)
        
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setPlainText(
            "Detailed Analysis Results\n"
            "========================\n\n"
            "1. File Structure Analysis\n"
            "   - Total files scanned: 247\n"
            "   - Code files: 189\n"
            "   - Configuration files: 58\n\n"
            "2. Security Findings\n"
            "   - Hardcoded credentials: 0\n"
            "   - SQL injection risks: 0\n"
            "   - XSS vulnerabilities: 1 (low risk)\n"
            "   - Insecure dependencies: 1 (medium risk)\n\n"
            "3. Code Quality\n"
            "   - Code coverage: 78%\n"
            "   - Complexity score: 6.2/10\n"
            "   - Documentation: 65%\n\n"
            "4. Recommendations\n"
            "   - Update dependency 'requests' to latest version\n"
            "   - Add input validation for user forms\n"
            "   - Implement rate limiting\n"
            "   - Add security headers\n"
            "   - Enable HTTPS enforcement"
        )
        
        details_layout.addWidget(self.details_text)
        self.results_tabs.addTab(details_widget, "Details")
        
        # Recommendations tab
        recommendations_widget = QWidget()
        recommendations_layout = QVBoxLayout(recommendations_widget)
        
        self.recommendations_list = QListWidget()
        recommendations = [
            "🔴 HIGH: Update vulnerable dependency 'requests'",
            "🟡 MEDIUM: Implement input validation for forms",
            "🟡 MEDIUM: Add rate limiting to API endpoints",
            "🟢 LOW: Add security headers to responses",
            "🟢 LOW: Enable HTTPS enforcement",
            "🟢 LOW: Improve error handling",
            "🟢 LOW: Add logging for security events"
        ]
        
        for rec in recommendations:
            item = QListWidgetItem(rec)
            self.recommendations_list.addItem(item)
            
        recommendations_layout.addWidget(self.recommendations_list)
        self.results_tabs.addTab(recommendations_widget, "Recommendations")
        
        # Add to main layout
        layout.addLayout(header_layout)
        layout.addWidget(self.results_tabs)
        
    def export_results(self):
        """Export research results"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Research Results", "research_results.txt", 
            "Text Files (*.txt);;JSON Files (*.json);;All Files (*.*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w') as f:
                    f.write(self.summary_text.toPlainText())
                    f.write("\n\n" + "="*50 + "\n\n")
                    f.write(self.details_text.toPlainText())
                    
                QMessageBox.information(self, "Success", f"Results exported to {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to export results: {e}")
                
    def update_results(self, results_data: Dict[str, Any]):
        """Update the results display"""
        # This would be implemented to update with real data
        pass


class ActiveTasksWidget(QFrame):
    """Widget for displaying active research tasks"""
    
    def __init__(self):
        super().__init__()
        self.setObjectName("card")
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the active tasks UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Header
        header = QLabel("⏳ Active Research Tasks")
        header.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(header)
        
        # Tasks list
        self.tasks_layout = QVBoxLayout()
        layout.addLayout(self.tasks_layout)
        
        # Add sample active task
        self.add_sample_task()
        
    def add_sample_task(self):
        """Add a sample active task"""
        task_widget = QWidget()
        task_layout = QVBoxLayout(task_widget)
        task_layout.setContentsMargins(10, 10, 10, 10)
        task_layout.setSpacing(8)
        
        # Task header
        header_layout = QHBoxLayout()
        
        task_name = QLabel("🔍 Security Analysis - all_files")
        task_name.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        
        status_label = QLabel("Running")
        status_label.setObjectName("success")
        
        header_layout.addWidget(task_name)
        header_layout.addStretch()
        header_layout.addWidget(status_label)
        
        # Progress
        progress_layout = QHBoxLayout()
        
        progress_bar = QProgressBar()
        progress_bar.setMaximum(100)
        progress_bar.setValue(73)
        progress_bar.setFixedHeight(8)
        progress_bar.setTextVisible(False)
        
        progress_text = QLabel("73%")
        progress_text.setObjectName("muted")
        progress_text.setFixedWidth(40)
        
        progress_layout.addWidget(progress_bar)
        progress_layout.addWidget(progress_text)
        
        # Task details
        details_label = QLabel("Analyzing file structure and security patterns...")
        details_label.setObjectName("secondary")
        details_label.setFont(QFont("Arial", 10))
        
        # Time info
        time_label = QLabel("Started: 2 minutes ago • ETA: 1 minute")
        time_label.setObjectName("muted")
        time_label.setFont(QFont("Arial", 9))
        
        task_layout.addLayout(header_layout)
        task_layout.addLayout(progress_layout)
        task_layout.addWidget(details_label)
        task_layout.addWidget(time_label)
        
        # Add separator
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setObjectName("muted")
        
        self.tasks_layout.addWidget(task_widget)
        self.tasks_layout.addWidget(separator)


class ResearchWidget(QWidget):
    """Main research widget"""
    
    def __init__(self, api_client):
        super().__init__()
        self.api_client = api_client
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the research widget UI"""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(30)
        
        # Top section - New task and active tasks
        top_layout = QHBoxLayout()
        top_layout.setSpacing(30)
        
        # New task widget
        self.task_widget = ResearchTaskWidget()
        self.task_widget.task_started.connect(self.start_research_task)
        
        # Active tasks widget
        self.active_tasks_widget = ActiveTasksWidget()
        
        top_layout.addWidget(self.task_widget, 1)
        top_layout.addWidget(self.active_tasks_widget, 1)
        
        # Bottom section - Results
        self.results_widget = ResearchResultsWidget()
        
        # Add to main layout
        main_layout.addLayout(top_layout)
        main_layout.addWidget(self.results_widget, 1)
        
    def start_research_task(self, task_data: Dict[str, Any]):
        """Start a new research task"""
        try:
            result = self.api_client.start_research_task(
                task_data['task_type'],
                task_data['target'],
                task_data['depth'],
                task_data['priority']
            )
            
            if result:
                QMessageBox.information(
                    self, "Task Started", 
                    f"Research task '{task_data['task_type']}' has been started successfully."
                )
                # Update active tasks display
                self.update_active_tasks()
            else:
                QMessageBox.warning(
                    self, "Error", 
                    "Failed to start research task. Please check the API connection."
                )
                
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error starting research task: {e}")
            
    def update_active_tasks(self):
        """Update the active tasks display"""
        # This would fetch real active tasks from the API
        pass
        
    def update_data(self):
        """Update research data from API"""
        try:
            # Get research results
            results = self.api_client.get_research_results()
            if results:
                self.results_widget.update_results(results)
                
            # Update active tasks
            self.update_active_tasks()
            
        except Exception as e:
            print(f"Error updating research data: {e}")
