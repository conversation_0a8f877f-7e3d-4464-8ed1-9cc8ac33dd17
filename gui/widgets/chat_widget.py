"""
Enhanced Modern Chat Widget for Offline Sentinel PyQt6 Application
Beautiful, responsive chat interface with live TTS, voice input, and advanced features
"""

import json
import threading
import tempfile
import os
from datetime import datetime
from typing import Dict, Any, List, Optional

# Optional imports with fallbacks
try:
    import speech_recognition as sr
    STT_AVAILABLE = True
except ImportError:
    STT_AVAILABLE = False
    sr = None

try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
except ImportError:
    PYAUDIO_AVAILABLE = False
    pyaudio = None

try:
    import wave
    WAVE_AVAILABLE = True
except ImportError:
    WAVE_AVAILABLE = False
    wave = None

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, QLineEdit,
    QPushButton, QScrollArea, QFrame, QLabel, QComboBox,
    QCheckBox, QSlider, QSplitter, QListWidget, QListWidgetItem,
    QMessageBox, QProgressBar, QGroupBox, QGridLayout, QSpacerItem,
    QSizePolicy, QTextBrowser, QFileDialog, QTabWidget, QSplitterHandle
)
from PyQt6.QtCore import (
    Qt, QTimer, pyqtSignal, QThread, QObject, QPropertyAnimation,
    QEasingCurve, QRect, QSize, QMutex, QWaitCondition
)
from PyQt6.QtGui import (
    QFont, QTextCursor, QPixmap, QPainter, QColor, QPalette,
    QLinearGradient, QBrush, QPen, QFontMetrics, QAction, QIcon
)

try:
    import pyttsx3
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False
    pyttsx3 = None
    print("⚠️ pyttsx3 not available. TTS functionality will be disabled.")

if not STT_AVAILABLE:
    print("⚠️ speech_recognition not available. Voice input will be disabled.")


class VoiceRecorder(QObject):
    """Voice recording and speech-to-text engine"""

    recording_started = pyqtSignal()
    recording_stopped = pyqtSignal()
    text_recognized = pyqtSignal(str)
    error_occurred = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.recognizer = sr.Recognizer() if STT_AVAILABLE else None
        self.microphone = sr.Microphone() if STT_AVAILABLE else None
        self.is_recording = False
        self.recording_thread = None

        if STT_AVAILABLE:
            # Adjust for ambient noise
            try:
                with self.microphone as source:
                    self.recognizer.adjust_for_ambient_noise(source, duration=1)
            except Exception as e:
                print(f"Warning: Could not adjust for ambient noise: {e}")

    def start_recording(self):
        """Start voice recording"""
        if not STT_AVAILABLE or self.is_recording:
            return

        self.is_recording = True
        self.recording_started.emit()

        def record_audio():
            try:
                with self.microphone as source:
                    # Listen for audio with timeout
                    audio = self.recognizer.listen(source, timeout=10, phrase_time_limit=30)

                if self.is_recording:  # Check if still recording
                    # Recognize speech
                    text = self.recognizer.recognize_google(audio)
                    self.text_recognized.emit(text)

            except sr.WaitTimeoutError:
                self.error_occurred.emit("Recording timeout - no speech detected")
            except sr.UnknownValueError:
                self.error_occurred.emit("Could not understand audio")
            except sr.RequestError as e:
                self.error_occurred.emit(f"Speech recognition error: {e}")
            except Exception as e:
                self.error_occurred.emit(f"Recording error: {e}")
            finally:
                self.is_recording = False
                self.recording_stopped.emit()

        self.recording_thread = threading.Thread(target=record_audio, daemon=True)
        self.recording_thread.start()

    def stop_recording(self):
        """Stop voice recording"""
        self.is_recording = False


class TTSEngine(QObject):
    """Text-to-Speech engine wrapper"""
    
    finished = pyqtSignal()
    error = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.engine = None
        self.enabled = TTS_AVAILABLE
        self.rate = 150
        self.volume = 0.8
        self.voice_index = 0
        self.voices = []
        
        if TTS_AVAILABLE:
            self.init_engine()
            
    def init_engine(self):
        """Initialize the TTS engine"""
        try:
            self.engine = pyttsx3.init()
            self.voices = self.engine.getProperty('voices')
            
            # Set default properties
            self.engine.setProperty('rate', self.rate)
            self.engine.setProperty('volume', self.volume)
            
            if self.voices and len(self.voices) > 0:
                self.engine.setProperty('voice', self.voices[0].id)
                
        except Exception as e:
            self.enabled = False
            self.error.emit(f"TTS initialization failed: {e}")
            
    def speak(self, text: str):
        """Speak the given text"""
        if not self.enabled or not self.engine:
            return
            
        try:
            # Run TTS in separate thread to avoid blocking UI
            def speak_thread():
                self.engine.say(text)
                self.engine.runAndWait()
                self.finished.emit()
                
            thread = threading.Thread(target=speak_thread, daemon=True)
            thread.start()
            
        except Exception as e:
            self.error.emit(f"TTS error: {e}")
            
    def set_rate(self, rate: int):
        """Set speech rate"""
        self.rate = rate
        if self.engine:
            self.engine.setProperty('rate', rate)
            
    def set_volume(self, volume: float):
        """Set speech volume"""
        self.volume = volume
        if self.engine:
            self.engine.setProperty('volume', volume)
            
    def set_voice(self, voice_index: int):
        """Set voice by index"""
        if self.engine and self.voices and 0 <= voice_index < len(self.voices):
            self.voice_index = voice_index
            self.engine.setProperty('voice', self.voices[voice_index].id)
            
    def get_voices(self) -> List[str]:
        """Get available voice names"""
        if not self.voices:
            return ["Default"]
        return [voice.name for voice in self.voices]
        
    def stop(self):
        """Stop current speech"""
        if self.engine:
            try:
                self.engine.stop()
            except:
                pass


class EnhancedChatBubble(QFrame):
    """Enhanced chat bubble with advanced styling and features"""

    message_action = pyqtSignal(str, str)  # action, message_id

    def __init__(self, message: str, sender: str, timestamp: datetime, is_user: bool = False,
                 message_type: str = "text", attachments: List[str] = None, message_id: str = None):
        super().__init__()
        self.message = message
        self.sender = sender
        self.timestamp = timestamp
        self.is_user = is_user
        self.message_type = message_type  # text, code, file, system, error
        self.attachments = attachments or []
        self.message_id = message_id or str(datetime.now().timestamp())

        self.setObjectName("enhanced-chat-bubble")
        self.setup_ui()
        self.apply_styling()

    def setup_ui(self):
        """Setup enhanced bubble UI with advanced features"""
        # Main container with proper alignment
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(20, 8, 20, 8)

        if self.is_user:
            main_layout.addStretch()  # Push to right

        # Bubble container
        bubble = QFrame()
        bubble.setObjectName(f"chat-bubble-{self.message_type}-{'user' if self.is_user else 'agent'}")
        bubble_layout = QVBoxLayout(bubble)
        bubble_layout.setContentsMargins(16, 12, 16, 12)
        bubble_layout.setSpacing(8)

        # Header with sender, time, and actions
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)

        if not self.is_user:  # Show sender for agent messages
            sender_label = QLabel(self.sender)
            sender_label.setFont(QFont("Segoe UI", 9, QFont.Weight.Bold))
            sender_label.setObjectName("bubble-sender")
            header_layout.addWidget(sender_label)

        header_layout.addStretch()

        # Message actions
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(4)

        # Copy button
        copy_btn = QPushButton("📋")
        copy_btn.setObjectName("bubble-action-btn")
        copy_btn.setFixedSize(20, 20)
        copy_btn.setToolTip("Copy message")
        copy_btn.clicked.connect(lambda: self.message_action.emit("copy", self.message_id))

        # Speak button (for agent messages)
        if not self.is_user:
            speak_btn = QPushButton("🔊")
            speak_btn.setObjectName("bubble-action-btn")
            speak_btn.setFixedSize(20, 20)
            speak_btn.setToolTip("Speak message")
            speak_btn.clicked.connect(lambda: self.message_action.emit("speak", self.message_id))
            actions_layout.addWidget(speak_btn)

        actions_layout.addWidget(copy_btn)

        # Time
        time_label = QLabel(self.timestamp.strftime("%H:%M"))
        time_label.setFont(QFont("Segoe UI", 8))
        time_label.setObjectName("bubble-time")

        header_layout.addLayout(actions_layout)
        header_layout.addWidget(time_label)
        bubble_layout.addLayout(header_layout)

        # Message content based on type
        if self.message_type == "code":
            self.setup_code_content(bubble_layout)
        elif self.message_type == "file":
            self.setup_file_content(bubble_layout)
        elif self.message_type == "system":
            self.setup_system_content(bubble_layout)
        else:
            self.setup_text_content(bubble_layout)

        # Attachments
        if self.attachments:
            self.setup_attachments(bubble_layout)

        main_layout.addWidget(bubble)

        if not self.is_user:
            main_layout.addStretch()  # Push to left

    def setup_text_content(self, layout):
        """Setup regular text message content"""
        message_label = QLabel(self.message)
        message_label.setWordWrap(True)
        message_label.setFont(QFont("Segoe UI", 10))
        message_label.setObjectName("bubble-message")
        message_label.setMaximumWidth(400)
        message_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        layout.addWidget(message_label)

    def setup_code_content(self, layout):
        """Setup code message content with syntax highlighting"""
        code_frame = QFrame()
        code_frame.setObjectName("code-frame")
        code_layout = QVBoxLayout(code_frame)
        code_layout.setContentsMargins(8, 8, 8, 8)

        # Code header
        code_header = QLabel("💻 Code")
        code_header.setFont(QFont("Segoe UI", 8, QFont.Weight.Bold))
        code_header.setObjectName("code-header")

        # Code content
        code_text = QTextEdit()
        code_text.setPlainText(self.message)
        code_text.setFont(QFont("Consolas", 9))
        code_text.setObjectName("code-content")
        code_text.setMaximumHeight(200)
        code_text.setReadOnly(True)

        code_layout.addWidget(code_header)
        code_layout.addWidget(code_text)
        layout.addWidget(code_frame)

    def setup_file_content(self, layout):
        """Setup file message content"""
        file_frame = QFrame()
        file_frame.setObjectName("file-frame")
        file_layout = QHBoxLayout(file_frame)
        file_layout.setContentsMargins(8, 8, 8, 8)

        file_icon = QLabel("📄")
        file_icon.setFont(QFont("Segoe UI", 16))

        file_info = QVBoxLayout()
        file_name = QLabel(self.message)
        file_name.setFont(QFont("Segoe UI", 10, QFont.Weight.Bold))
        file_name.setObjectName("file-name")

        file_desc = QLabel("Click to open")
        file_desc.setFont(QFont("Segoe UI", 8))
        file_desc.setObjectName("file-desc")

        file_info.addWidget(file_name)
        file_info.addWidget(file_desc)

        file_layout.addWidget(file_icon)
        file_layout.addLayout(file_info)
        file_layout.addStretch()

        layout.addWidget(file_frame)

    def setup_system_content(self, layout):
        """Setup system message content"""
        system_label = QLabel(f"🔧 {self.message}")
        system_label.setWordWrap(True)
        system_label.setFont(QFont("Segoe UI", 9, QFont.Weight.Bold))
        system_label.setObjectName("system-message")
        system_label.setMaximumWidth(400)
        layout.addWidget(system_label)

    def setup_attachments(self, layout):
        """Setup attachments display"""
        attachments_frame = QFrame()
        attachments_frame.setObjectName("attachments-frame")
        attachments_layout = QVBoxLayout(attachments_frame)
        attachments_layout.setContentsMargins(8, 8, 8, 8)

        attachments_header = QLabel(f"📎 {len(self.attachments)} attachment(s)")
        attachments_header.setFont(QFont("Segoe UI", 8, QFont.Weight.Bold))
        attachments_header.setObjectName("attachments-header")

        attachments_layout.addWidget(attachments_header)

        for attachment in self.attachments[:3]:  # Show max 3 attachments
            att_label = QLabel(f"• {os.path.basename(attachment)}")
            att_label.setFont(QFont("Segoe UI", 8))
            att_label.setObjectName("attachment-item")
            attachments_layout.addWidget(att_label)

        if len(self.attachments) > 3:
            more_label = QLabel(f"... and {len(self.attachments) - 3} more")
            more_label.setFont(QFont("Segoe UI", 8))
            more_label.setObjectName("attachment-more")
            attachments_layout.addWidget(more_label)

        layout.addWidget(attachments_frame)

    def apply_styling(self):
        """Apply modern styling to the bubble"""
        if self.is_user:
            self.setStyleSheet("""
                QFrame#chat-bubble-user {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #007AFF, stop:1 #5856D6);
                    border-radius: 18px;
                    border: none;
                }
                QLabel#bubble-message {
                    color: white;
                    background: transparent;
                }
                QLabel#bubble-time-user {
                    color: rgba(255, 255, 255, 0.8);
                    background: transparent;
                }
            """)
        else:
            self.setStyleSheet("""
                QFrame#chat-bubble-agent {
                    background: #2C2C2E;
                    border-radius: 18px;
                    border: 1px solid #3A3A3C;
                }
                QLabel#bubble-sender {
                    color: #007AFF;
                    background: transparent;
                }
                QLabel#bubble-message {
                    color: #FFFFFF;
                    background: transparent;
                }
                QLabel#bubble-time {
                    color: #8E8E93;
                    background: transparent;
                }
            """)


class ModernAgentSelector(QFrame):
    """Modern agent selection widget"""

    agent_changed = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.setObjectName("modern-card")
        self.setup_ui()

    def setup_ui(self):
        """Setup modern agent selector"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(12)

        # Header with icon
        header_layout = QHBoxLayout()
        header_icon = QLabel("🤖")
        header_icon.setFont(QFont("Segoe UI", 16))
        header_text = QLabel("AI Agent")
        header_text.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
        header_text.setObjectName("modern-header")

        header_layout.addWidget(header_icon)
        header_layout.addWidget(header_text)
        header_layout.addStretch()

        # Modern agent selection
        self.agent_combo = QComboBox()
        self.agent_combo.setObjectName("modern-combo")
        self.agent_combo.addItems([
            "🔬 Research Agent",
            "⚙️ System Agent",
            "🔧 Upgrade Agent",
            "📡 Communication Agent",
            "🧠 General AI Assistant"
        ])
        self.agent_combo.currentTextChanged.connect(self.agent_changed.emit)

        # Status indicator
        status_layout = QHBoxLayout()
        self.status_dot = QLabel("●")
        self.status_dot.setFont(QFont("Segoe UI", 12))
        self.status_dot.setObjectName("status-online")
        self.status_label = QLabel("Online")
        self.status_label.setFont(QFont("Segoe UI", 10))
        self.status_label.setObjectName("status-text")

        status_layout.addWidget(self.status_dot)
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()

        # Description
        self.description_label = QLabel("Specialized in research and analysis tasks")
        self.description_label.setObjectName("modern-description")
        self.description_label.setFont(QFont("Segoe UI", 9))
        self.description_label.setWordWrap(True)

        layout.addLayout(header_layout)
        layout.addWidget(self.agent_combo)
        layout.addLayout(status_layout)
        layout.addWidget(self.description_label)

        # Update description when agent changes
        self.agent_combo.currentTextChanged.connect(self.update_description)

        # Apply modern styling
        self.setStyleSheet("""
            QFrame#modern-card {
                background: #1C1C1E;
                border-radius: 12px;
                border: 1px solid #3A3A3C;
            }
            QLabel#modern-header {
                color: #FFFFFF;
            }
            QComboBox#modern-combo {
                background: #2C2C2E;
                border: 1px solid #3A3A3C;
                border-radius: 8px;
                padding: 8px 12px;
                color: #FFFFFF;
                font-size: 11px;
            }
            QComboBox#modern-combo::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox#modern-combo::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #8E8E93;
            }
            QLabel#status-online {
                color: #30D158;
            }
            QLabel#status-text {
                color: #30D158;
            }
            QLabel#modern-description {
                color: #8E8E93;
            }
        """)

    def update_description(self, agent_name: str):
        """Update agent description"""
        descriptions = {
            "🔬 Research Agent": "Specialized in research and analysis tasks",
            "⚙️ System Agent": "Monitors system performance and health",
            "🔧 Upgrade Agent": "Handles system upgrades and improvements",
            "📡 Communication Agent": "Manages inter-agent communication",
            "🧠 General AI Assistant": "General purpose AI assistant"
        }

        self.description_label.setText(descriptions.get(agent_name, "AI Agent"))


class VoiceInputWidget(QFrame):
    """Voice input control widget"""

    voice_text_ready = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.voice_recorder = VoiceRecorder()
        self.setObjectName("voice-input-widget")
        self.setup_ui()
        self.setup_connections()

    def setup_ui(self):
        """Setup voice input UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)

        # Header
        header = QLabel("🎤 Voice Input")
        header.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        header.setObjectName("voice-header")

        # Status
        self.status_label = QLabel("Ready to listen")
        self.status_label.setFont(QFont("Segoe UI", 9))
        self.status_label.setObjectName("voice-status")

        # Record button
        self.record_btn = QPushButton("🎤 Start Recording")
        self.record_btn.setObjectName("voice-record-btn")
        self.record_btn.clicked.connect(self.toggle_recording)

        # Voice activity indicator
        self.activity_indicator = QProgressBar()
        self.activity_indicator.setObjectName("voice-activity")
        self.activity_indicator.setVisible(False)
        self.activity_indicator.setRange(0, 0)  # Indeterminate

        # Settings
        settings_layout = QHBoxLayout()

        # Enable/disable voice input
        self.voice_enabled = QCheckBox("Enable Voice Input")
        self.voice_enabled.setChecked(STT_AVAILABLE)
        self.voice_enabled.setEnabled(STT_AVAILABLE)
        if not STT_AVAILABLE:
            self.voice_enabled.setText("Voice Input Not Available")

        settings_layout.addWidget(self.voice_enabled)

        layout.addWidget(header)
        layout.addWidget(self.status_label)
        layout.addWidget(self.record_btn)
        layout.addWidget(self.activity_indicator)
        layout.addLayout(settings_layout)

        # Apply styling
        self.setStyleSheet("""
            QFrame#voice-input-widget {
                background: #1C1C1E;
                border-radius: 12px;
                border: 1px solid #3A3A3C;
            }
            QLabel#voice-header {
                color: #FFFFFF;
            }
            QLabel#voice-status {
                color: #8E8E93;
            }
            QPushButton#voice-record-btn {
                background: #2C2C2E;
                border: 1px solid #3A3A3C;
                border-radius: 8px;
                padding: 8px 16px;
                color: #FFFFFF;
                font-weight: bold;
            }
            QPushButton#voice-record-btn:hover {
                background: #3A3A3C;
            }
            QPushButton#voice-record-btn:pressed {
                background: #FF3B30;
            }
            QProgressBar#voice-activity {
                border: 1px solid #3A3A3C;
                border-radius: 4px;
                background: #2C2C2E;
            }
            QProgressBar#voice-activity::chunk {
                background: #007AFF;
                border-radius: 3px;
            }
            QCheckBox {
                color: #FFFFFF;
            }
        """)

    def setup_connections(self):
        """Setup signal connections"""
        self.voice_recorder.recording_started.connect(self.on_recording_started)
        self.voice_recorder.recording_stopped.connect(self.on_recording_stopped)
        self.voice_recorder.text_recognized.connect(self.on_text_recognized)
        self.voice_recorder.error_occurred.connect(self.on_error)

    def toggle_recording(self):
        """Toggle voice recording"""
        if not self.voice_enabled.isChecked() or not STT_AVAILABLE:
            return

        if not self.voice_recorder.is_recording:
            self.voice_recorder.start_recording()
        else:
            self.voice_recorder.stop_recording()

    def on_recording_started(self):
        """Handle recording started"""
        self.record_btn.setText("🛑 Stop Recording")
        self.record_btn.setObjectName("voice-record-btn-active")
        self.status_label.setText("Listening... Speak now")
        self.activity_indicator.setVisible(True)

    def on_recording_stopped(self):
        """Handle recording stopped"""
        self.record_btn.setText("🎤 Start Recording")
        self.record_btn.setObjectName("voice-record-btn")
        self.status_label.setText("Processing...")
        self.activity_indicator.setVisible(False)

    def on_text_recognized(self, text: str):
        """Handle recognized text"""
        self.status_label.setText(f"Recognized: {text[:50]}...")
        self.voice_text_ready.emit(text)
        QTimer.singleShot(3000, lambda: self.status_label.setText("Ready to listen"))

    def on_error(self, error: str):
        """Handle voice recognition error"""
        self.status_label.setText(f"Error: {error}")
        QTimer.singleShot(5000, lambda: self.status_label.setText("Ready to listen"))


class TTSControls(QFrame):
    """Enhanced TTS control panel"""

    settings_changed = pyqtSignal(dict)

    def __init__(self, tts_engine: TTSEngine):
        super().__init__()
        self.tts_engine = tts_engine
        self.setObjectName("tts-controls")
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the TTS controls UI - compact design"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)  # Smaller margins
        layout.setSpacing(8)  # Smaller spacing

        # Header - more compact
        header = QLabel("🔊 TTS")
        header.setFont(QFont("Arial", 12, QFont.Weight.Bold))  # Smaller font
        layout.addWidget(header)
        
        # Enable/disable TTS
        self.tts_enabled = QCheckBox("Enable TTS")
        self.tts_enabled.setChecked(self.tts_engine.enabled)
        self.tts_enabled.toggled.connect(self.toggle_tts)
        layout.addWidget(self.tts_enabled)
        
        if not TTS_AVAILABLE:
            self.tts_enabled.setEnabled(False)
            self.tts_enabled.setText("TTS Not Available (install pyttsx3)")
            
        # Voice selection
        voice_layout = QHBoxLayout()
        voice_label = QLabel("Voice:")
        voice_label.setObjectName("muted")
        
        self.voice_combo = QComboBox()
        self.voice_combo.addItems(self.tts_engine.get_voices())
        self.voice_combo.currentIndexChanged.connect(self.change_voice)
        
        voice_layout.addWidget(voice_label)
        voice_layout.addWidget(self.voice_combo)
        
        # Speech rate
        rate_layout = QVBoxLayout()
        rate_label = QLabel("Speech Rate:")
        rate_label.setObjectName("muted")
        
        self.rate_slider = QSlider(Qt.Orientation.Horizontal)
        self.rate_slider.setRange(50, 300)
        self.rate_slider.setValue(self.tts_engine.rate)
        self.rate_slider.valueChanged.connect(self.change_rate)
        
        self.rate_value = QLabel(f"{self.tts_engine.rate} WPM")
        self.rate_value.setObjectName("muted")
        self.rate_value.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        rate_layout.addWidget(rate_label)
        rate_layout.addWidget(self.rate_slider)
        rate_layout.addWidget(self.rate_value)
        
        # Volume
        volume_layout = QVBoxLayout()
        volume_label = QLabel("Volume:")
        volume_label.setObjectName("muted")
        
        self.volume_slider = QSlider(Qt.Orientation.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(int(self.tts_engine.volume * 100))
        self.volume_slider.valueChanged.connect(self.change_volume)
        
        self.volume_value = QLabel(f"{int(self.tts_engine.volume * 100)}%")
        self.volume_value.setObjectName("muted")
        self.volume_value.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        volume_layout.addWidget(volume_label)
        volume_layout.addWidget(self.volume_slider)
        volume_layout.addWidget(self.volume_value)
        
        # Test button
        test_btn = QPushButton("🔊 Test Voice")
        test_btn.clicked.connect(self.test_voice)
        
        layout.addLayout(voice_layout)
        layout.addLayout(rate_layout)
        layout.addLayout(volume_layout)
        layout.addWidget(test_btn)
        
    def toggle_tts(self, enabled: bool):
        """Toggle TTS on/off"""
        self.tts_engine.enabled = enabled and TTS_AVAILABLE
        self.settings_changed.emit({'enabled': self.tts_engine.enabled})
        
    def change_voice(self, index: int):
        """Change TTS voice"""
        self.tts_engine.set_voice(index)
        self.settings_changed.emit({'voice': index})
        
    def change_rate(self, rate: int):
        """Change speech rate"""
        self.tts_engine.set_rate(rate)
        self.rate_value.setText(f"{rate} WPM")
        self.settings_changed.emit({'rate': rate})
        
    def change_volume(self, volume: int):
        """Change speech volume"""
        volume_float = volume / 100.0
        self.tts_engine.set_volume(volume_float)
        self.volume_value.setText(f"{volume}%")
        self.settings_changed.emit({'volume': volume_float})
        
    def test_voice(self):
        """Test the current voice settings"""
        test_text = "Hello! This is a test of the text-to-speech system. How do I sound?"
        self.tts_engine.speak(test_text)


class ConversationManager(QObject):
    """Manages multiple conversations and chat history"""

    conversation_changed = pyqtSignal(str)  # conversation_id

    def __init__(self):
        super().__init__()
        self.conversations = {}
        self.current_conversation_id = "default"
        self.create_conversation("default", "General Chat")

    def create_conversation(self, conversation_id: str, title: str):
        """Create a new conversation"""
        self.conversations[conversation_id] = {
            'id': conversation_id,
            'title': title,
            'messages': [],
            'created_at': datetime.now(),
            'agent': "🔬 Research Agent"
        }

    def get_conversation(self, conversation_id: str):
        """Get conversation by ID"""
        return self.conversations.get(conversation_id)

    def add_message(self, conversation_id: str, message: dict):
        """Add message to conversation"""
        if conversation_id in self.conversations:
            self.conversations[conversation_id]['messages'].append(message)

    def get_messages(self, conversation_id: str):
        """Get messages from conversation"""
        conv = self.conversations.get(conversation_id)
        return conv['messages'] if conv else []

    def set_current_conversation(self, conversation_id: str):
        """Set current active conversation"""
        if conversation_id in self.conversations:
            self.current_conversation_id = conversation_id
            self.conversation_changed.emit(conversation_id)

    def get_conversation_list(self):
        """Get list of all conversations"""
        return list(self.conversations.values())


class EnhancedChatWidget(QWidget):
    """Enhanced chat widget with advanced features"""

    def __init__(self, api_client):
        super().__init__()
        self.api_client = api_client
        self.tts_engine = TTSEngine()
        self.voice_input = VoiceInputWidget()
        self.conversation_manager = ConversationManager()
        self.current_agent = "🔬 Research Agent"
        self.auto_tts = True
        self.message_widgets = {}  # Store message widgets by ID
        self.chat_history = []  # Store chat history

        self.setup_ui()
        self.setup_connections()

    def setup_ui(self):
        """Setup enhanced chat interface with advanced features"""
        # Main splitter layout
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_splitter.setObjectName("main-chat-splitter")

        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(main_splitter)

        # Left panel - Conversations list
        conversations_panel = self.create_conversations_panel()

        # Center panel - Chat area
        chat_container = QFrame()
        chat_container.setObjectName("chat-container")
        chat_layout = QVBoxLayout(chat_container)
        chat_layout.setContentsMargins(0, 0, 0, 0)
        chat_layout.setSpacing(0)

        # Right panel - Controls and settings
        controls_panel = self.create_controls_panel()

        # Add panels to splitter
        main_splitter.addWidget(conversations_panel)
        main_splitter.addWidget(chat_container)
        main_splitter.addWidget(controls_panel)

        # Set splitter proportions
        main_splitter.setSizes([250, 600, 350])
        main_splitter.setCollapsible(0, True)
        main_splitter.setCollapsible(2, True)
        
        # Modern header
        header = QFrame()
        header.setObjectName("chat-header")
        header.setFixedHeight(70)
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(24, 16, 24, 16)

        # Title section
        title_layout = QVBoxLayout()
        title_layout.setSpacing(2)

        title = QLabel("AI Assistant")
        title.setFont(QFont("Segoe UI", 18, QFont.Weight.Bold))
        title.setObjectName("chat-title")

        subtitle = QLabel("Chat with your AI agents")
        subtitle.setFont(QFont("Segoe UI", 11))
        subtitle.setObjectName("chat-subtitle")

        title_layout.addWidget(title)
        title_layout.addWidget(subtitle)

        # Header controls
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(12)

        # Auto-TTS toggle
        self.auto_tts_checkbox = QCheckBox("🔊 Auto TTS")
        self.auto_tts_checkbox.setChecked(self.auto_tts)
        self.auto_tts_checkbox.setObjectName("modern-checkbox")
        self.auto_tts_checkbox.toggled.connect(self.toggle_auto_tts)

        # Clear chat button
        clear_btn = QPushButton("🗑️")
        clear_btn.setObjectName("header-button")
        clear_btn.setToolTip("Clear Chat")
        clear_btn.setFixedSize(36, 36)
        clear_btn.clicked.connect(self.clear_chat)

        controls_layout.addWidget(self.auto_tts_checkbox)
        controls_layout.addWidget(clear_btn)

        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        header_layout.addLayout(controls_layout)
        
        # Messages area with modern styling
        messages_container = QFrame()
        messages_container.setObjectName("messages-container")
        messages_layout = QVBoxLayout(messages_container)
        messages_layout.setContentsMargins(0, 0, 0, 0)

        self.chat_scroll = QScrollArea()
        self.chat_scroll.setObjectName("chat-scroll")
        self.chat_scroll.setWidgetResizable(True)
        self.chat_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.chat_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.chat_scroll.setFrameShape(QFrame.Shape.NoFrame)

        self.chat_container = QWidget()
        self.chat_container.setObjectName("chat-messages")
        self.chat_layout = QVBoxLayout(self.chat_container)
        self.chat_layout.setContentsMargins(0, 20, 0, 20)
        self.chat_layout.setSpacing(12)
        self.chat_layout.addStretch()  # Push messages to bottom

        self.chat_scroll.setWidget(self.chat_container)
        messages_layout.addWidget(self.chat_scroll)
        
        # Modern input area
        input_container = QFrame()
        input_container.setObjectName("input-container")
        input_container.setFixedHeight(80)
        input_layout = QHBoxLayout(input_container)
        input_layout.setContentsMargins(24, 16, 24, 16)
        input_layout.setSpacing(12)

        # Message input with modern styling
        self.message_input = QLineEdit()
        self.message_input.setObjectName("modern-input")
        self.message_input.setPlaceholderText("Type your message...")
        self.message_input.returnPressed.connect(self.send_message)
        self.message_input.setFixedHeight(48)

        # Input buttons
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(8)

        speak_btn = QPushButton("🔊")
        speak_btn.setObjectName("input-button")
        speak_btn.setToolTip("Speak last message")
        speak_btn.setFixedSize(48, 48)
        speak_btn.clicked.connect(self.speak_last_message)

        send_btn = QPushButton("➤")
        send_btn.setObjectName("send-button")
        send_btn.setToolTip("Send Message")
        send_btn.setFixedSize(48, 48)
        send_btn.clicked.connect(self.send_message)

        buttons_layout.addWidget(speak_btn)
        buttons_layout.addWidget(send_btn)

        input_layout.addWidget(self.message_input)
        input_layout.addLayout(buttons_layout)
        
        # Assemble chat area
        chat_layout.addWidget(header)
        chat_layout.addWidget(messages_container, 1)
        chat_layout.addWidget(input_container)

        # Sidebar with controls
        sidebar = QFrame()
        sidebar.setObjectName("chat-sidebar")
        sidebar.setFixedWidth(320)
        sidebar_layout = QVBoxLayout(sidebar)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)

        # Agent selector
        self.agent_selector = ModernAgentSelector()
        self.agent_selector.agent_changed.connect(self.change_agent)

        # TTS controls
        self.tts_controls = TTSControls(self.tts_engine)

        # Quick actions
        quick_actions = QFrame()
        quick_actions.setObjectName("quick-actions")
        quick_layout = QVBoxLayout(quick_actions)
        quick_layout.setContentsMargins(20, 20, 20, 20)
        quick_layout.setSpacing(12)

        quick_header = QLabel("Quick Actions")
        quick_header.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
        quick_header.setObjectName("modern-header")

        # Quick action buttons
        actions_grid = QGridLayout()
        actions_grid.setSpacing(8)

        status_btn = QPushButton("📊 System Status")
        status_btn.setObjectName("quick-action-btn")
        status_btn.clicked.connect(lambda: self.send_quick_message("What's the current system status?"))

        health_btn = QPushButton("💚 Health Check")
        health_btn.setObjectName("quick-action-btn")
        health_btn.clicked.connect(lambda: self.send_quick_message("Run a system health check"))

        agents_btn = QPushButton("🤖 Agent Status")
        agents_btn.setObjectName("quick-action-btn")
        agents_btn.clicked.connect(lambda: self.send_quick_message("Show me the status of all agents"))

        actions_grid.addWidget(status_btn, 0, 0)
        actions_grid.addWidget(health_btn, 1, 0)
        actions_grid.addWidget(agents_btn, 2, 0)

        quick_layout.addWidget(quick_header)
        quick_layout.addLayout(actions_grid)

        sidebar_layout.addWidget(self.agent_selector)
        sidebar_layout.addWidget(self.tts_controls)
        sidebar_layout.addWidget(quick_actions)
        sidebar_layout.addStretch()

        # Add to main layout
        main_layout.addWidget(chat_container, 1)
        main_layout.addWidget(sidebar)

        # Apply modern styling
        self.apply_modern_styling()
        
        # Add welcome message
        self.add_message("Welcome! I'm your AI assistant. How can I help you today?", "🤖 AI Assistant", False)

    def create_conversations_panel(self):
        """Create conversations management panel"""
        panel = QFrame()
        panel.setObjectName("conversations-panel")
        panel.setFixedWidth(250)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)

        # Header
        header_layout = QHBoxLayout()
        header = QLabel("💬 Conversations")
        header.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
        header.setObjectName("panel-header")

        new_chat_btn = QPushButton("➕")
        new_chat_btn.setObjectName("new-chat-btn")
        new_chat_btn.setFixedSize(28, 28)
        new_chat_btn.setToolTip("New Conversation")
        new_chat_btn.clicked.connect(self.create_new_conversation)

        header_layout.addWidget(header)
        header_layout.addStretch()
        header_layout.addWidget(new_chat_btn)

        # Conversations list
        self.conversations_list = QListWidget()
        self.conversations_list.setObjectName("conversations-list")
        self.conversations_list.itemClicked.connect(self.switch_conversation)

        # Populate conversations
        self.refresh_conversations_list()

        layout.addLayout(header_layout)
        layout.addWidget(self.conversations_list)

        return panel

    def create_controls_panel(self):
        """Create controls and settings panel"""
        panel = QFrame()
        panel.setObjectName("controls-panel")
        panel.setFixedWidth(350)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Tabs for different control sections
        tabs = QTabWidget()
        tabs.setObjectName("controls-tabs")

        # Agent tab
        agent_tab = QWidget()
        agent_layout = QVBoxLayout(agent_tab)
        agent_layout.setContentsMargins(16, 16, 16, 16)
        agent_layout.setSpacing(16)

        self.agent_selector = ModernAgentSelector()
        self.agent_selector.agent_changed.connect(self.change_agent)
        agent_layout.addWidget(self.agent_selector)
        agent_layout.addStretch()

        # Voice tab
        voice_tab = QWidget()
        voice_layout = QVBoxLayout(voice_tab)
        voice_layout.setContentsMargins(16, 16, 16, 16)
        voice_layout.setSpacing(16)

        voice_layout.addWidget(self.voice_input)
        self.tts_controls = TTSControls(self.tts_engine)
        voice_layout.addWidget(self.tts_controls)
        voice_layout.addStretch()

        # Settings tab
        settings_tab = self.create_settings_tab()

        # Add tabs
        tabs.addTab(agent_tab, "🤖 Agent")
        tabs.addTab(voice_tab, "🎤 Voice")
        tabs.addTab(settings_tab, "⚙️ Settings")

        layout.addWidget(tabs)

        return panel

    def create_settings_tab(self):
        """Create settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)

        # Chat settings
        chat_settings = QGroupBox("Chat Settings")
        chat_settings.setObjectName("settings-group")
        chat_layout = QVBoxLayout(chat_settings)

        # Auto-TTS
        self.auto_tts_checkbox = QCheckBox("🔊 Auto TTS for responses")
        self.auto_tts_checkbox.setChecked(self.auto_tts)
        self.auto_tts_checkbox.toggled.connect(self.toggle_auto_tts)

        # Auto-scroll
        self.auto_scroll_checkbox = QCheckBox("📜 Auto-scroll to new messages")
        self.auto_scroll_checkbox.setChecked(True)

        # Message timestamps
        self.show_timestamps_checkbox = QCheckBox("🕐 Show message timestamps")
        self.show_timestamps_checkbox.setChecked(True)

        chat_layout.addWidget(self.auto_tts_checkbox)
        chat_layout.addWidget(self.auto_scroll_checkbox)
        chat_layout.addWidget(self.show_timestamps_checkbox)

        # Export settings
        export_settings = QGroupBox("Export & Backup")
        export_settings.setObjectName("settings-group")
        export_layout = QVBoxLayout(export_settings)

        export_chat_btn = QPushButton("📤 Export Current Chat")
        export_chat_btn.setObjectName("settings-btn")
        export_chat_btn.clicked.connect(self.export_current_chat)

        backup_all_btn = QPushButton("💾 Backup All Conversations")
        backup_all_btn.setObjectName("settings-btn")
        backup_all_btn.clicked.connect(self.backup_all_conversations)

        export_layout.addWidget(export_chat_btn)
        export_layout.addWidget(backup_all_btn)

        # Quick actions
        quick_actions = QGroupBox("Quick Actions")
        quick_actions.setObjectName("settings-group")
        quick_layout = QVBoxLayout(quick_actions)

        clear_chat_btn = QPushButton("🗑️ Clear Current Chat")
        clear_chat_btn.setObjectName("settings-btn")
        clear_chat_btn.clicked.connect(self.clear_chat)

        system_status_btn = QPushButton("📊 System Status")
        system_status_btn.setObjectName("settings-btn")
        system_status_btn.clicked.connect(lambda: self.send_quick_message("What's the current system status?"))

        quick_layout.addWidget(clear_chat_btn)
        quick_layout.addWidget(system_status_btn)

        layout.addWidget(chat_settings)
        layout.addWidget(export_settings)
        layout.addWidget(quick_actions)
        layout.addStretch()

        return tab

    def apply_modern_styling(self):
        """Apply modern styling to the chat widget"""
        self.setStyleSheet("""
            QFrame#chat-container {
                background: #000000;
                border: none;
            }
            QFrame#chat-header {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1C1C1E, stop:1 #2C2C2E);
                border-bottom: 1px solid #3A3A3C;
            }
            QLabel#chat-title {
                color: #FFFFFF;
            }
            QLabel#chat-subtitle {
                color: #8E8E93;
            }
            QCheckBox#modern-checkbox {
                color: #FFFFFF;
                spacing: 8px;
            }
            QCheckBox#modern-checkbox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                border: 2px solid #3A3A3C;
                background: #1C1C1E;
            }
            QCheckBox#modern-checkbox::indicator:checked {
                background: #007AFF;
                border-color: #007AFF;
            }
            QPushButton#header-button {
                background: #2C2C2E;
                border: 1px solid #3A3A3C;
                border-radius: 18px;
                color: #8E8E93;
                font-size: 16px;
            }
            QPushButton#header-button:hover {
                background: #3A3A3C;
                color: #FFFFFF;
            }
            QFrame#messages-container {
                background: #000000;
                border: none;
            }
            QScrollArea#chat-scroll {
                background: #000000;
                border: none;
            }
            QWidget#chat-messages {
                background: #000000;
            }
            QFrame#input-container {
                background: #1C1C1E;
                border-top: 1px solid #3A3A3C;
            }
            QLineEdit#modern-input {
                background: #2C2C2E;
                border: 2px solid #3A3A3C;
                border-radius: 24px;
                padding: 12px 20px;
                color: #FFFFFF;
                font-size: 14px;
            }
            QLineEdit#modern-input:focus {
                border-color: #007AFF;
            }
            QPushButton#input-button {
                background: #2C2C2E;
                border: 1px solid #3A3A3C;
                border-radius: 24px;
                color: #8E8E93;
                font-size: 16px;
            }
            QPushButton#input-button:hover {
                background: #3A3A3C;
                color: #FFFFFF;
            }
            QPushButton#send-button {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #007AFF, stop:1 #5856D6);
                border: none;
                border-radius: 24px;
                color: white;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton#send-button:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0056CC, stop:1 #4A44B8);
            }
            QFrame#chat-sidebar {
                background: #1C1C1E;
                border-left: 1px solid #3A3A3C;
            }
            QFrame#quick-actions {
                background: #1C1C1E;
                border-top: 1px solid #3A3A3C;
            }
            QPushButton#quick-action-btn {
                background: #2C2C2E;
                border: 1px solid #3A3A3C;
                border-radius: 8px;
                padding: 12px;
                color: #FFFFFF;
                text-align: left;
                font-size: 12px;
            }
            QPushButton#quick-action-btn:hover {
                background: #3A3A3C;
                border-color: #007AFF;
            }
        """)

    def setup_connections(self):
        """Setup signal connections"""
        self.tts_engine.error.connect(self.handle_tts_error)
        
    def add_message(self, message: str, sender: str, is_user: bool):
        """Add a message to the chat"""
        timestamp = datetime.now()

        # Create modern message bubble
        message_widget = EnhancedChatBubble(message, sender, timestamp, is_user)

        # Insert before the stretch
        self.chat_layout.insertWidget(self.chat_layout.count() - 1, message_widget)

        # Scroll to bottom
        QTimer.singleShot(100, self.scroll_to_bottom)

        # Store in history
        self.chat_history.append({
            'message': message,
            'sender': sender,
            'timestamp': timestamp,
            'is_user': is_user
        })

        # Auto TTS for agent messages
        if not is_user and self.auto_tts and self.tts_engine.enabled:
            self.tts_engine.speak(message)
            
    def scroll_to_bottom(self):
        """Scroll chat to bottom"""
        scrollbar = self.chat_scroll.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
    def send_message(self):
        """Send a message to the AI agent"""
        message = self.message_input.text().strip()
        if not message:
            return
            
        # Add user message
        self.add_message(message, "👤 You", True)
        self.message_input.clear()
        
        # Send to agent (simulate response for now)
        self.send_to_agent(message)
        
    def send_quick_message(self, message: str):
        """Send a quick message"""
        self.message_input.setText(message)
        self.send_message()
        
    def send_to_agent(self, message: str):
        """Send message to the selected agent"""
        try:
            # Use the API client to interact with agent
            response = self.api_client.interact_with_agent(message)
            
            if response and 'response' in response:
                agent_response = response['response']
            else:
                # Fallback response based on current agent
                agent_response = self.generate_fallback_response(message)
                
            # Add agent response
            self.add_message(agent_response, self.current_agent, False)
            
        except Exception as e:
            error_msg = f"Sorry, I'm having trouble connecting to the backend. Error: {e}"
            self.add_message(error_msg, self.current_agent, False)
            
    def generate_fallback_response(self, message: str) -> str:
        """Generate a fallback response when API is unavailable"""
        message_lower = message.lower()
        
        if "status" in message_lower:
            return "System status: All agents are operational. CPU: 45%, Memory: 68%, Network: Online."
        elif "health" in message_lower:
            return "Health check complete: System health is at 94%. All components are functioning normally."
        elif "agent" in message_lower:
            return "Agent status: 4 agents active - Research Agent (85%), System Agent (99%), Upgrade Agent (73%), Communication Agent (95%)."
        elif "help" in message_lower:
            return "I can help you with system monitoring, agent management, security analysis, and general questions about the Offline Sentinel system."
        else:
            return f"I understand you're asking about: '{message}'. I'm currently processing your request and will provide detailed information shortly."
            
    def change_agent(self, agent_name: str):
        """Change the current agent"""
        self.current_agent = agent_name
        self.add_message(f"Switched to {agent_name}. How can I assist you?", agent_name, False)
        
    def toggle_auto_tts(self, enabled: bool):
        """Toggle automatic TTS"""
        self.auto_tts = enabled
        
    def speak_last_message(self):
        """Speak the last agent message"""
        if self.chat_history:
            # Find last agent message
            for msg in reversed(self.chat_history):
                if not msg['is_user']:
                    self.tts_engine.speak(msg['message'])
                    break
                    
    def clear_chat(self):
        """Clear the chat history"""
        # Remove all message widgets except the stretch
        for i in reversed(range(self.chat_layout.count() - 1)):
            child = self.chat_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
                
        self.chat_history.clear()
        
        # Add welcome message
        self.add_message("Chat cleared. How can I help you?", self.current_agent, False)
        
    def handle_tts_error(self, error: str):
        """Handle TTS errors"""
        print(f"TTS Error: {error}")
        
    def refresh_conversations_list(self):
        """Refresh the conversations list"""
        self.conversations_list.clear()
        for conv in self.conversation_manager.get_conversation_list():
            item = QListWidgetItem(conv['title'])
            item.setData(Qt.ItemDataRole.UserRole, conv['id'])
            if conv['id'] == self.conversation_manager.current_conversation_id:
                item.setSelected(True)
            self.conversations_list.addItem(item)

    def create_new_conversation(self):
        """Create a new conversation"""
        import uuid
        conv_id = str(uuid.uuid4())
        title = f"Chat {len(self.conversation_manager.conversations) + 1}"
        self.conversation_manager.create_conversation(conv_id, title)
        self.refresh_conversations_list()
        self.conversation_manager.set_current_conversation(conv_id)
        self.clear_chat_display()

    def switch_conversation(self, item):
        """Switch to selected conversation"""
        conv_id = item.data(Qt.ItemDataRole.UserRole)
        self.conversation_manager.set_current_conversation(conv_id)
        self.load_conversation_messages(conv_id)

    def load_conversation_messages(self, conv_id):
        """Load messages for a conversation"""
        self.clear_chat_display()
        messages = self.conversation_manager.get_messages(conv_id)
        for msg in messages:
            self.add_message_widget(msg)

    def clear_chat_display(self):
        """Clear the chat display"""
        for i in reversed(range(self.chat_layout.count() - 1)):
            child = self.chat_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        self.message_widgets.clear()

    def export_current_chat(self):
        """Export current conversation"""
        conv_id = self.conversation_manager.current_conversation_id
        messages = self.conversation_manager.get_messages(conv_id)

        if not messages:
            QMessageBox.information(self, "Export", "No messages to export.")
            return

        filename, _ = QFileDialog.getSaveFileName(
            self, "Export Chat", f"chat_{conv_id}.json", "JSON Files (*.json)"
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(messages, f, indent=2, default=str)
                QMessageBox.information(self, "Export", f"Chat exported to {filename}")
            except Exception as e:
                QMessageBox.critical(self, "Export Error", f"Failed to export chat: {e}")

    def backup_all_conversations(self):
        """Backup all conversations"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "Backup Conversations", "conversations_backup.json", "JSON Files (*.json)"
        )

        if filename:
            try:
                backup_data = {
                    'conversations': self.conversation_manager.conversations,
                    'backup_date': datetime.now().isoformat()
                }
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, indent=2, default=str)
                QMessageBox.information(self, "Backup", f"All conversations backed up to {filename}")
            except Exception as e:
                QMessageBox.critical(self, "Backup Error", f"Failed to backup conversations: {e}")

    def update_data(self):
        """Update chat data (placeholder for consistency)"""
        pass


# Alias for backward compatibility
ChatWidget = EnhancedChatWidget
