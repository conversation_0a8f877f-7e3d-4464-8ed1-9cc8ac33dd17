"""
Modern Chat Widget for Offline Sentinel PyQt6 Application
Beautiful, responsive chat interface with live TTS functionality
"""

import json
import threading
from datetime import datetime
from typing import Dict, Any, List, Optional

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, QLineEdit,
    QPushButton, QScrollArea, QFrame, QLabel, QComboBox,
    QCheckBox, QSlider, QSplitter, QListWidget, QListWidgetItem,
    QMessageBox, QProgressBar, QGroupBox, QGridLayout, QSpacerItem,
    QSizePolicy, QTextBrowser
)
from PyQt6.QtCore import (
    Qt, QTimer, pyqtSignal, QThread, QObject, QPropertyAnimation,
    QEasingCurve, QRect, QSize
)
from PyQt6.QtGui import (
    QFont, QTextCursor, QPixmap, Q<PERSON><PERSON>ter, QColor, Q<PERSON>alette,
    QLinearGradient, QBrush, QPen, QFontMetrics
)

try:
    import pyttsx3
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False
    print("⚠️ pyttsx3 not available. TTS functionality will be disabled.")


class TTSEngine(QObject):
    """Text-to-Speech engine wrapper"""
    
    finished = pyqtSignal()
    error = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.engine = None
        self.enabled = TTS_AVAILABLE
        self.rate = 150
        self.volume = 0.8
        self.voice_index = 0
        self.voices = []
        
        if TTS_AVAILABLE:
            self.init_engine()
            
    def init_engine(self):
        """Initialize the TTS engine"""
        try:
            self.engine = pyttsx3.init()
            self.voices = self.engine.getProperty('voices')
            
            # Set default properties
            self.engine.setProperty('rate', self.rate)
            self.engine.setProperty('volume', self.volume)
            
            if self.voices and len(self.voices) > 0:
                self.engine.setProperty('voice', self.voices[0].id)
                
        except Exception as e:
            self.enabled = False
            self.error.emit(f"TTS initialization failed: {e}")
            
    def speak(self, text: str):
        """Speak the given text"""
        if not self.enabled or not self.engine:
            return
            
        try:
            # Run TTS in separate thread to avoid blocking UI
            def speak_thread():
                self.engine.say(text)
                self.engine.runAndWait()
                self.finished.emit()
                
            thread = threading.Thread(target=speak_thread, daemon=True)
            thread.start()
            
        except Exception as e:
            self.error.emit(f"TTS error: {e}")
            
    def set_rate(self, rate: int):
        """Set speech rate"""
        self.rate = rate
        if self.engine:
            self.engine.setProperty('rate', rate)
            
    def set_volume(self, volume: float):
        """Set speech volume"""
        self.volume = volume
        if self.engine:
            self.engine.setProperty('volume', volume)
            
    def set_voice(self, voice_index: int):
        """Set voice by index"""
        if self.engine and self.voices and 0 <= voice_index < len(self.voices):
            self.voice_index = voice_index
            self.engine.setProperty('voice', self.voices[voice_index].id)
            
    def get_voices(self) -> List[str]:
        """Get available voice names"""
        if not self.voices:
            return ["Default"]
        return [voice.name for voice in self.voices]
        
    def stop(self):
        """Stop current speech"""
        if self.engine:
            try:
                self.engine.stop()
            except:
                pass


class ModernChatBubble(QFrame):
    """Modern chat bubble with beautiful styling"""

    def __init__(self, message: str, sender: str, timestamp: datetime, is_user: bool = False):
        super().__init__()
        self.message = message
        self.sender = sender
        self.timestamp = timestamp
        self.is_user = is_user

        self.setObjectName("modern-chat-bubble")
        self.setup_ui()
        self.apply_styling()

    def setup_ui(self):
        """Setup modern bubble UI"""
        # Main container with proper alignment
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(20, 8, 20, 8)

        if self.is_user:
            main_layout.addStretch()  # Push to right

        # Bubble container
        bubble = QFrame()
        bubble.setObjectName("chat-bubble-user" if self.is_user else "chat-bubble-agent")
        bubble_layout = QVBoxLayout(bubble)
        bubble_layout.setContentsMargins(16, 12, 16, 12)
        bubble_layout.setSpacing(6)

        # Sender and time in header
        if not self.is_user:  # Only show sender for agent messages
            header_layout = QHBoxLayout()
            header_layout.setContentsMargins(0, 0, 0, 0)

            sender_label = QLabel(self.sender)
            sender_label.setFont(QFont("Segoe UI", 9, QFont.Weight.Bold))
            sender_label.setObjectName("bubble-sender")

            time_label = QLabel(self.timestamp.strftime("%H:%M"))
            time_label.setFont(QFont("Segoe UI", 8))
            time_label.setObjectName("bubble-time")

            header_layout.addWidget(sender_label)
            header_layout.addStretch()
            header_layout.addWidget(time_label)

            bubble_layout.addLayout(header_layout)

        # Message content
        message_label = QLabel(self.message)
        message_label.setWordWrap(True)
        message_label.setFont(QFont("Segoe UI", 10))
        message_label.setObjectName("bubble-message")

        # Set max width for better readability
        message_label.setMaximumWidth(400)

        bubble_layout.addWidget(message_label)

        # Time for user messages (bottom right)
        if self.is_user:
            time_layout = QHBoxLayout()
            time_layout.addStretch()
            time_label = QLabel(self.timestamp.strftime("%H:%M"))
            time_label.setFont(QFont("Segoe UI", 8))
            time_label.setObjectName("bubble-time-user")
            time_layout.addWidget(time_label)
            bubble_layout.addLayout(time_layout)

        main_layout.addWidget(bubble)

        if not self.is_user:
            main_layout.addStretch()  # Push to left

    def apply_styling(self):
        """Apply modern styling to the bubble"""
        if self.is_user:
            self.setStyleSheet("""
                QFrame#chat-bubble-user {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #007AFF, stop:1 #5856D6);
                    border-radius: 18px;
                    border: none;
                }
                QLabel#bubble-message {
                    color: white;
                    background: transparent;
                }
                QLabel#bubble-time-user {
                    color: rgba(255, 255, 255, 0.8);
                    background: transparent;
                }
            """)
        else:
            self.setStyleSheet("""
                QFrame#chat-bubble-agent {
                    background: #2C2C2E;
                    border-radius: 18px;
                    border: 1px solid #3A3A3C;
                }
                QLabel#bubble-sender {
                    color: #007AFF;
                    background: transparent;
                }
                QLabel#bubble-message {
                    color: #FFFFFF;
                    background: transparent;
                }
                QLabel#bubble-time {
                    color: #8E8E93;
                    background: transparent;
                }
            """)


class ModernAgentSelector(QFrame):
    """Modern agent selection widget"""

    agent_changed = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.setObjectName("modern-card")
        self.setup_ui()

    def setup_ui(self):
        """Setup modern agent selector"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(12)

        # Header with icon
        header_layout = QHBoxLayout()
        header_icon = QLabel("🤖")
        header_icon.setFont(QFont("Segoe UI", 16))
        header_text = QLabel("AI Agent")
        header_text.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
        header_text.setObjectName("modern-header")

        header_layout.addWidget(header_icon)
        header_layout.addWidget(header_text)
        header_layout.addStretch()

        # Modern agent selection
        self.agent_combo = QComboBox()
        self.agent_combo.setObjectName("modern-combo")
        self.agent_combo.addItems([
            "🔬 Research Agent",
            "⚙️ System Agent",
            "🔧 Upgrade Agent",
            "📡 Communication Agent",
            "🧠 General AI Assistant"
        ])
        self.agent_combo.currentTextChanged.connect(self.agent_changed.emit)

        # Status indicator
        status_layout = QHBoxLayout()
        self.status_dot = QLabel("●")
        self.status_dot.setFont(QFont("Segoe UI", 12))
        self.status_dot.setObjectName("status-online")
        self.status_label = QLabel("Online")
        self.status_label.setFont(QFont("Segoe UI", 10))
        self.status_label.setObjectName("status-text")

        status_layout.addWidget(self.status_dot)
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()

        # Description
        self.description_label = QLabel("Specialized in research and analysis tasks")
        self.description_label.setObjectName("modern-description")
        self.description_label.setFont(QFont("Segoe UI", 9))
        self.description_label.setWordWrap(True)

        layout.addLayout(header_layout)
        layout.addWidget(self.agent_combo)
        layout.addLayout(status_layout)
        layout.addWidget(self.description_label)

        # Update description when agent changes
        self.agent_combo.currentTextChanged.connect(self.update_description)

        # Apply modern styling
        self.setStyleSheet("""
            QFrame#modern-card {
                background: #1C1C1E;
                border-radius: 12px;
                border: 1px solid #3A3A3C;
            }
            QLabel#modern-header {
                color: #FFFFFF;
            }
            QComboBox#modern-combo {
                background: #2C2C2E;
                border: 1px solid #3A3A3C;
                border-radius: 8px;
                padding: 8px 12px;
                color: #FFFFFF;
                font-size: 11px;
            }
            QComboBox#modern-combo::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox#modern-combo::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #8E8E93;
            }
            QLabel#status-online {
                color: #30D158;
            }
            QLabel#status-text {
                color: #30D158;
            }
            QLabel#modern-description {
                color: #8E8E93;
            }
        """)

    def update_description(self, agent_name: str):
        """Update agent description"""
        descriptions = {
            "🔬 Research Agent": "Specialized in research and analysis tasks",
            "⚙️ System Agent": "Monitors system performance and health",
            "🔧 Upgrade Agent": "Handles system upgrades and improvements",
            "📡 Communication Agent": "Manages inter-agent communication",
            "🧠 General AI Assistant": "General purpose AI assistant"
        }

        self.description_label.setText(descriptions.get(agent_name, "AI Agent"))


class TTSControls(QFrame):
    """TTS control panel"""
    
    settings_changed = pyqtSignal(dict)
    
    def __init__(self, tts_engine: TTSEngine):
        super().__init__()
        self.tts_engine = tts_engine
        self.setObjectName("card")
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the TTS controls UI - compact design"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)  # Smaller margins
        layout.setSpacing(8)  # Smaller spacing

        # Header - more compact
        header = QLabel("🔊 TTS")
        header.setFont(QFont("Arial", 12, QFont.Weight.Bold))  # Smaller font
        layout.addWidget(header)
        
        # Enable/disable TTS
        self.tts_enabled = QCheckBox("Enable TTS")
        self.tts_enabled.setChecked(self.tts_engine.enabled)
        self.tts_enabled.toggled.connect(self.toggle_tts)
        layout.addWidget(self.tts_enabled)
        
        if not TTS_AVAILABLE:
            self.tts_enabled.setEnabled(False)
            self.tts_enabled.setText("TTS Not Available (install pyttsx3)")
            
        # Voice selection
        voice_layout = QHBoxLayout()
        voice_label = QLabel("Voice:")
        voice_label.setObjectName("muted")
        
        self.voice_combo = QComboBox()
        self.voice_combo.addItems(self.tts_engine.get_voices())
        self.voice_combo.currentIndexChanged.connect(self.change_voice)
        
        voice_layout.addWidget(voice_label)
        voice_layout.addWidget(self.voice_combo)
        
        # Speech rate
        rate_layout = QVBoxLayout()
        rate_label = QLabel("Speech Rate:")
        rate_label.setObjectName("muted")
        
        self.rate_slider = QSlider(Qt.Orientation.Horizontal)
        self.rate_slider.setRange(50, 300)
        self.rate_slider.setValue(self.tts_engine.rate)
        self.rate_slider.valueChanged.connect(self.change_rate)
        
        self.rate_value = QLabel(f"{self.tts_engine.rate} WPM")
        self.rate_value.setObjectName("muted")
        self.rate_value.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        rate_layout.addWidget(rate_label)
        rate_layout.addWidget(self.rate_slider)
        rate_layout.addWidget(self.rate_value)
        
        # Volume
        volume_layout = QVBoxLayout()
        volume_label = QLabel("Volume:")
        volume_label.setObjectName("muted")
        
        self.volume_slider = QSlider(Qt.Orientation.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(int(self.tts_engine.volume * 100))
        self.volume_slider.valueChanged.connect(self.change_volume)
        
        self.volume_value = QLabel(f"{int(self.tts_engine.volume * 100)}%")
        self.volume_value.setObjectName("muted")
        self.volume_value.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        volume_layout.addWidget(volume_label)
        volume_layout.addWidget(self.volume_slider)
        volume_layout.addWidget(self.volume_value)
        
        # Test button
        test_btn = QPushButton("🔊 Test Voice")
        test_btn.clicked.connect(self.test_voice)
        
        layout.addLayout(voice_layout)
        layout.addLayout(rate_layout)
        layout.addLayout(volume_layout)
        layout.addWidget(test_btn)
        
    def toggle_tts(self, enabled: bool):
        """Toggle TTS on/off"""
        self.tts_engine.enabled = enabled and TTS_AVAILABLE
        self.settings_changed.emit({'enabled': self.tts_engine.enabled})
        
    def change_voice(self, index: int):
        """Change TTS voice"""
        self.tts_engine.set_voice(index)
        self.settings_changed.emit({'voice': index})
        
    def change_rate(self, rate: int):
        """Change speech rate"""
        self.tts_engine.set_rate(rate)
        self.rate_value.setText(f"{rate} WPM")
        self.settings_changed.emit({'rate': rate})
        
    def change_volume(self, volume: int):
        """Change speech volume"""
        volume_float = volume / 100.0
        self.tts_engine.set_volume(volume_float)
        self.volume_value.setText(f"{volume}%")
        self.settings_changed.emit({'volume': volume_float})
        
    def test_voice(self):
        """Test the current voice settings"""
        test_text = "Hello! This is a test of the text-to-speech system. How do I sound?"
        self.tts_engine.speak(test_text)


class ChatWidget(QWidget):
    """Modern chat widget with beautiful interface and live TTS"""

    def __init__(self, api_client):
        super().__init__()
        self.api_client = api_client
        self.tts_engine = TTSEngine()
        self.current_agent = "🔬 Research Agent"
        self.auto_tts = True
        self.chat_history = []

        self.setup_ui()
        self.setup_connections()

    def setup_ui(self):
        """Setup modern chat interface"""
        # Main layout with modern styling
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Chat area (left side)
        chat_container = QFrame()
        chat_container.setObjectName("chat-container")
        chat_layout = QVBoxLayout(chat_container)
        chat_layout.setContentsMargins(0, 0, 0, 0)
        chat_layout.setSpacing(0)
        
        # Modern header
        header = QFrame()
        header.setObjectName("chat-header")
        header.setFixedHeight(70)
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(24, 16, 24, 16)

        # Title section
        title_layout = QVBoxLayout()
        title_layout.setSpacing(2)

        title = QLabel("AI Assistant")
        title.setFont(QFont("Segoe UI", 18, QFont.Weight.Bold))
        title.setObjectName("chat-title")

        subtitle = QLabel("Chat with your AI agents")
        subtitle.setFont(QFont("Segoe UI", 11))
        subtitle.setObjectName("chat-subtitle")

        title_layout.addWidget(title)
        title_layout.addWidget(subtitle)

        # Header controls
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(12)

        # Auto-TTS toggle
        self.auto_tts_checkbox = QCheckBox("🔊 Auto TTS")
        self.auto_tts_checkbox.setChecked(self.auto_tts)
        self.auto_tts_checkbox.setObjectName("modern-checkbox")
        self.auto_tts_checkbox.toggled.connect(self.toggle_auto_tts)

        # Clear chat button
        clear_btn = QPushButton("🗑️")
        clear_btn.setObjectName("header-button")
        clear_btn.setToolTip("Clear Chat")
        clear_btn.setFixedSize(36, 36)
        clear_btn.clicked.connect(self.clear_chat)

        controls_layout.addWidget(self.auto_tts_checkbox)
        controls_layout.addWidget(clear_btn)

        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        header_layout.addLayout(controls_layout)
        
        # Messages area with modern styling
        messages_container = QFrame()
        messages_container.setObjectName("messages-container")
        messages_layout = QVBoxLayout(messages_container)
        messages_layout.setContentsMargins(0, 0, 0, 0)

        self.chat_scroll = QScrollArea()
        self.chat_scroll.setObjectName("chat-scroll")
        self.chat_scroll.setWidgetResizable(True)
        self.chat_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.chat_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.chat_scroll.setFrameShape(QFrame.Shape.NoFrame)

        self.chat_container = QWidget()
        self.chat_container.setObjectName("chat-messages")
        self.chat_layout = QVBoxLayout(self.chat_container)
        self.chat_layout.setContentsMargins(0, 20, 0, 20)
        self.chat_layout.setSpacing(12)
        self.chat_layout.addStretch()  # Push messages to bottom

        self.chat_scroll.setWidget(self.chat_container)
        messages_layout.addWidget(self.chat_scroll)
        
        # Modern input area
        input_container = QFrame()
        input_container.setObjectName("input-container")
        input_container.setFixedHeight(80)
        input_layout = QHBoxLayout(input_container)
        input_layout.setContentsMargins(24, 16, 24, 16)
        input_layout.setSpacing(12)

        # Message input with modern styling
        self.message_input = QLineEdit()
        self.message_input.setObjectName("modern-input")
        self.message_input.setPlaceholderText("Type your message...")
        self.message_input.returnPressed.connect(self.send_message)
        self.message_input.setFixedHeight(48)

        # Input buttons
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(8)

        speak_btn = QPushButton("🔊")
        speak_btn.setObjectName("input-button")
        speak_btn.setToolTip("Speak last message")
        speak_btn.setFixedSize(48, 48)
        speak_btn.clicked.connect(self.speak_last_message)

        send_btn = QPushButton("➤")
        send_btn.setObjectName("send-button")
        send_btn.setToolTip("Send Message")
        send_btn.setFixedSize(48, 48)
        send_btn.clicked.connect(self.send_message)

        buttons_layout.addWidget(speak_btn)
        buttons_layout.addWidget(send_btn)

        input_layout.addWidget(self.message_input)
        input_layout.addLayout(buttons_layout)
        
        # Assemble chat area
        chat_layout.addWidget(header)
        chat_layout.addWidget(messages_container, 1)
        chat_layout.addWidget(input_container)

        # Sidebar with controls
        sidebar = QFrame()
        sidebar.setObjectName("chat-sidebar")
        sidebar.setFixedWidth(320)
        sidebar_layout = QVBoxLayout(sidebar)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)

        # Agent selector
        self.agent_selector = ModernAgentSelector()
        self.agent_selector.agent_changed.connect(self.change_agent)

        # TTS controls
        self.tts_controls = TTSControls(self.tts_engine)

        # Quick actions
        quick_actions = QFrame()
        quick_actions.setObjectName("quick-actions")
        quick_layout = QVBoxLayout(quick_actions)
        quick_layout.setContentsMargins(20, 20, 20, 20)
        quick_layout.setSpacing(12)

        quick_header = QLabel("Quick Actions")
        quick_header.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
        quick_header.setObjectName("modern-header")

        # Quick action buttons
        actions_grid = QGridLayout()
        actions_grid.setSpacing(8)

        status_btn = QPushButton("📊 System Status")
        status_btn.setObjectName("quick-action-btn")
        status_btn.clicked.connect(lambda: self.send_quick_message("What's the current system status?"))

        health_btn = QPushButton("💚 Health Check")
        health_btn.setObjectName("quick-action-btn")
        health_btn.clicked.connect(lambda: self.send_quick_message("Run a system health check"))

        agents_btn = QPushButton("🤖 Agent Status")
        agents_btn.setObjectName("quick-action-btn")
        agents_btn.clicked.connect(lambda: self.send_quick_message("Show me the status of all agents"))

        actions_grid.addWidget(status_btn, 0, 0)
        actions_grid.addWidget(health_btn, 1, 0)
        actions_grid.addWidget(agents_btn, 2, 0)

        quick_layout.addWidget(quick_header)
        quick_layout.addLayout(actions_grid)

        sidebar_layout.addWidget(self.agent_selector)
        sidebar_layout.addWidget(self.tts_controls)
        sidebar_layout.addWidget(quick_actions)
        sidebar_layout.addStretch()

        # Add to main layout
        main_layout.addWidget(chat_container, 1)
        main_layout.addWidget(sidebar)

        # Apply modern styling
        self.apply_modern_styling()
        
        # Add welcome message
        self.add_message("Welcome! I'm your AI assistant. How can I help you today?", "🤖 AI Assistant", False)

    def apply_modern_styling(self):
        """Apply modern styling to the chat widget"""
        self.setStyleSheet("""
            QFrame#chat-container {
                background: #000000;
                border: none;
            }
            QFrame#chat-header {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1C1C1E, stop:1 #2C2C2E);
                border-bottom: 1px solid #3A3A3C;
            }
            QLabel#chat-title {
                color: #FFFFFF;
            }
            QLabel#chat-subtitle {
                color: #8E8E93;
            }
            QCheckBox#modern-checkbox {
                color: #FFFFFF;
                spacing: 8px;
            }
            QCheckBox#modern-checkbox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                border: 2px solid #3A3A3C;
                background: #1C1C1E;
            }
            QCheckBox#modern-checkbox::indicator:checked {
                background: #007AFF;
                border-color: #007AFF;
            }
            QPushButton#header-button {
                background: #2C2C2E;
                border: 1px solid #3A3A3C;
                border-radius: 18px;
                color: #8E8E93;
                font-size: 16px;
            }
            QPushButton#header-button:hover {
                background: #3A3A3C;
                color: #FFFFFF;
            }
            QFrame#messages-container {
                background: #000000;
                border: none;
            }
            QScrollArea#chat-scroll {
                background: #000000;
                border: none;
            }
            QWidget#chat-messages {
                background: #000000;
            }
            QFrame#input-container {
                background: #1C1C1E;
                border-top: 1px solid #3A3A3C;
            }
            QLineEdit#modern-input {
                background: #2C2C2E;
                border: 2px solid #3A3A3C;
                border-radius: 24px;
                padding: 12px 20px;
                color: #FFFFFF;
                font-size: 14px;
            }
            QLineEdit#modern-input:focus {
                border-color: #007AFF;
            }
            QPushButton#input-button {
                background: #2C2C2E;
                border: 1px solid #3A3A3C;
                border-radius: 24px;
                color: #8E8E93;
                font-size: 16px;
            }
            QPushButton#input-button:hover {
                background: #3A3A3C;
                color: #FFFFFF;
            }
            QPushButton#send-button {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #007AFF, stop:1 #5856D6);
                border: none;
                border-radius: 24px;
                color: white;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton#send-button:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0056CC, stop:1 #4A44B8);
            }
            QFrame#chat-sidebar {
                background: #1C1C1E;
                border-left: 1px solid #3A3A3C;
            }
            QFrame#quick-actions {
                background: #1C1C1E;
                border-top: 1px solid #3A3A3C;
            }
            QPushButton#quick-action-btn {
                background: #2C2C2E;
                border: 1px solid #3A3A3C;
                border-radius: 8px;
                padding: 12px;
                color: #FFFFFF;
                text-align: left;
                font-size: 12px;
            }
            QPushButton#quick-action-btn:hover {
                background: #3A3A3C;
                border-color: #007AFF;
            }
        """)

    def setup_connections(self):
        """Setup signal connections"""
        self.tts_engine.error.connect(self.handle_tts_error)
        
    def add_message(self, message: str, sender: str, is_user: bool):
        """Add a message to the chat"""
        timestamp = datetime.now()

        # Create modern message bubble
        message_widget = ModernChatBubble(message, sender, timestamp, is_user)

        # Insert before the stretch
        self.chat_layout.insertWidget(self.chat_layout.count() - 1, message_widget)

        # Scroll to bottom
        QTimer.singleShot(100, self.scroll_to_bottom)

        # Store in history
        self.chat_history.append({
            'message': message,
            'sender': sender,
            'timestamp': timestamp,
            'is_user': is_user
        })

        # Auto TTS for agent messages
        if not is_user and self.auto_tts and self.tts_engine.enabled:
            self.tts_engine.speak(message)
            
    def scroll_to_bottom(self):
        """Scroll chat to bottom"""
        scrollbar = self.chat_scroll.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
    def send_message(self):
        """Send a message to the AI agent"""
        message = self.message_input.text().strip()
        if not message:
            return
            
        # Add user message
        self.add_message(message, "👤 You", True)
        self.message_input.clear()
        
        # Send to agent (simulate response for now)
        self.send_to_agent(message)
        
    def send_quick_message(self, message: str):
        """Send a quick message"""
        self.message_input.setText(message)
        self.send_message()
        
    def send_to_agent(self, message: str):
        """Send message to the selected agent"""
        try:
            # Use the API client to interact with agent
            response = self.api_client.interact_with_agent(message)
            
            if response and 'response' in response:
                agent_response = response['response']
            else:
                # Fallback response based on current agent
                agent_response = self.generate_fallback_response(message)
                
            # Add agent response
            self.add_message(agent_response, self.current_agent, False)
            
        except Exception as e:
            error_msg = f"Sorry, I'm having trouble connecting to the backend. Error: {e}"
            self.add_message(error_msg, self.current_agent, False)
            
    def generate_fallback_response(self, message: str) -> str:
        """Generate a fallback response when API is unavailable"""
        message_lower = message.lower()
        
        if "status" in message_lower:
            return "System status: All agents are operational. CPU: 45%, Memory: 68%, Network: Online."
        elif "health" in message_lower:
            return "Health check complete: System health is at 94%. All components are functioning normally."
        elif "agent" in message_lower:
            return "Agent status: 4 agents active - Research Agent (85%), System Agent (99%), Upgrade Agent (73%), Communication Agent (95%)."
        elif "help" in message_lower:
            return "I can help you with system monitoring, agent management, security analysis, and general questions about the Offline Sentinel system."
        else:
            return f"I understand you're asking about: '{message}'. I'm currently processing your request and will provide detailed information shortly."
            
    def change_agent(self, agent_name: str):
        """Change the current agent"""
        self.current_agent = agent_name
        self.add_message(f"Switched to {agent_name}. How can I assist you?", agent_name, False)
        
    def toggle_auto_tts(self, enabled: bool):
        """Toggle automatic TTS"""
        self.auto_tts = enabled
        
    def speak_last_message(self):
        """Speak the last agent message"""
        if self.chat_history:
            # Find last agent message
            for msg in reversed(self.chat_history):
                if not msg['is_user']:
                    self.tts_engine.speak(msg['message'])
                    break
                    
    def clear_chat(self):
        """Clear the chat history"""
        # Remove all message widgets except the stretch
        for i in reversed(range(self.chat_layout.count() - 1)):
            child = self.chat_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
                
        self.chat_history.clear()
        
        # Add welcome message
        self.add_message("Chat cleared. How can I help you?", self.current_agent, False)
        
    def handle_tts_error(self, error: str):
        """Handle TTS errors"""
        print(f"TTS Error: {error}")
        
    def update_data(self):
        """Update chat data (placeholder for consistency)"""
        pass
