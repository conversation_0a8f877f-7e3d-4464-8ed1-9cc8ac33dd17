"""
Network Widget for Offline Sentinel PyQt6 Application
Network monitoring and security interface
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, 
    QFrame, QProgressBar, QPushButton, QScrollArea, QTextEdit,
    QTabWidget, QListWidget, QListWidgetItem, QTableWidget,
    QTableWidgetItem, QHeaderView
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont
from typing import Dict, Any, List


class NetworkStatusCard(QFrame):
    """Network status overview card"""
    
    def __init__(self):
        super().__init__()
        self.setObjectName("stat-card")
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the network status UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)
        
        # Header
        header = QLabel("📡 Network Status")
        header.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        layout.addWidget(header)
        
        # Status indicators
        status_layout = QGridLayout()
        
        # Connection status
        conn_label = QLabel("Connection:")
        conn_label.setObjectName("muted")
        self.conn_status = QLabel("🟢 Online")
        self.conn_status.setObjectName("success")
        
        # Throughput
        throughput_label = QLabel("Throughput:")
        throughput_label.setObjectName("muted")
        self.throughput_value = QLabel("23.5 Mbps")
        
        # Latency
        latency_label = QLabel("Latency:")
        latency_label.setObjectName("muted")
        self.latency_value = QLabel("12ms")
        
        # Packet loss
        loss_label = QLabel("Packet Loss:")
        loss_label.setObjectName("muted")
        self.loss_value = QLabel("0.1%")
        
        status_layout.addWidget(conn_label, 0, 0)
        status_layout.addWidget(self.conn_status, 0, 1)
        status_layout.addWidget(throughput_label, 1, 0)
        status_layout.addWidget(self.throughput_value, 1, 1)
        status_layout.addWidget(latency_label, 2, 0)
        status_layout.addWidget(self.latency_value, 2, 1)
        status_layout.addWidget(loss_label, 3, 0)
        status_layout.addWidget(self.loss_value, 3, 1)
        
        layout.addLayout(status_layout)
        
    def update_status(self, throughput: str, latency: str, loss: str, online: bool = True):
        """Update network status"""
        self.conn_status.setText("🟢 Online" if online else "🔴 Offline")
        self.throughput_value.setText(throughput)
        self.latency_value.setText(latency)
        self.loss_value.setText(loss)


class ConnectionsTableWidget(QFrame):
    """Active connections table widget"""
    
    def __init__(self):
        super().__init__()
        self.setObjectName("card")
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the connections table UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Header
        header_layout = QHBoxLayout()
        
        header = QLabel("🔗 Active Connections")
        header.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        
        refresh_btn = QPushButton("🔄 Refresh")
        refresh_btn.clicked.connect(self.refresh_connections)
        
        header_layout.addWidget(header)
        header_layout.addStretch()
        header_layout.addWidget(refresh_btn)
        
        # Connections table
        self.connections_table = QTableWidget()
        self.connections_table.setColumnCount(5)
        self.connections_table.setHorizontalHeaderLabels([
            "Protocol", "Local Address", "Remote Address", "Status", "Process"
        ])
        
        # Set column widths
        header = self.connections_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        
        # Add sample data
        self.populate_sample_data()
        
        layout.addLayout(header_layout)
        layout.addWidget(self.connections_table)
        
    def populate_sample_data(self):
        """Populate with sample connection data"""
        connections = [
            ("TCP", "127.0.0.1:8001", "127.0.0.1:54321", "ESTABLISHED", "Backend"),
            ("TCP", "0.0.0.0:8001", "*:*", "LISTENING", "Backend"),
            ("TCP", "127.0.0.1:5432", "127.0.0.1:54322", "ESTABLISHED", "Database"),
            ("TCP", "127.0.0.1:6379", "127.0.0.1:54323", "ESTABLISHED", "Redis"),
            ("UDP", "0.0.0.0:53", "*:*", "LISTENING", "DNS"),
            ("TCP", "***********00:443", "8.8.8.8:443", "ESTABLISHED", "Agent"),
        ]
        
        self.connections_table.setRowCount(len(connections))
        
        for row, (protocol, local, remote, status, process) in enumerate(connections):
            self.connections_table.setItem(row, 0, QTableWidgetItem(protocol))
            self.connections_table.setItem(row, 1, QTableWidgetItem(local))
            self.connections_table.setItem(row, 2, QTableWidgetItem(remote))
            self.connections_table.setItem(row, 3, QTableWidgetItem(status))
            self.connections_table.setItem(row, 4, QTableWidgetItem(process))
            
    def refresh_connections(self):
        """Refresh the connections table"""
        print("Refreshing network connections...")


class SecurityEventsWidget(QFrame):
    """Network security events widget"""
    
    def __init__(self):
        super().__init__()
        self.setObjectName("card")
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the security events UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Header
        header_layout = QHBoxLayout()
        
        header = QLabel("🛡️ Security Events")
        header.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        
        clear_btn = QPushButton("🗑️ Clear")
        clear_btn.clicked.connect(self.clear_events)
        
        header_layout.addWidget(header)
        header_layout.addStretch()
        header_layout.addWidget(clear_btn)
        
        # Events list
        self.events_list = QListWidget()
        
        # Add sample security events
        events = [
            "🟢 [14:23:45] Connection established from 192.168.1.50",
            "🟡 [14:22:12] Unusual traffic pattern detected from ********5",
            "🟢 [14:21:33] SSL certificate validated for api.example.com",
            "🔴 [14:20:45] Failed login attempt from 203.0.113.42",
            "🟢 [14:19:22] Firewall rule applied successfully",
            "🟡 [14:18:15] High bandwidth usage detected",
            "🟢 [14:17:08] VPN connection established",
            "🔴 [14:16:33] Port scan detected from 198.51.100.25"
        ]
        
        for event in events:
            item = QListWidgetItem(event)
            self.events_list.addItem(item)
            
        layout.addLayout(header_layout)
        layout.addWidget(self.events_list)
        
    def clear_events(self):
        """Clear security events"""
        self.events_list.clear()
        
    def add_event(self, level: str, message: str):
        """Add a new security event"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        icon = "🟢" if level == "info" else "🟡" if level == "warning" else "🔴"
        event_text = f"{icon} [{timestamp}] {message}"
        
        item = QListWidgetItem(event_text)
        self.events_list.insertItem(0, item)  # Add to top
        
        # Limit to 50 events
        if self.events_list.count() > 50:
            self.events_list.takeItem(50)


class NetworkToolsWidget(QFrame):
    """Network tools and utilities widget"""
    
    def __init__(self):
        super().__init__()
        self.setObjectName("card")
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the network tools UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Header
        header = QLabel("🔧 Network Tools")
        header.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(header)
        
        # Tools buttons
        tools_layout = QGridLayout()
        
        ping_btn = QPushButton("📡 Ping Test")
        ping_btn.clicked.connect(self.ping_test)
        
        traceroute_btn = QPushButton("🗺️ Traceroute")
        traceroute_btn.clicked.connect(self.traceroute)
        
        portscan_btn = QPushButton("🔍 Port Scan")
        portscan_btn.clicked.connect(self.port_scan)
        
        bandwidth_btn = QPushButton("📊 Bandwidth Test")
        bandwidth_btn.clicked.connect(self.bandwidth_test)
        
        dns_btn = QPushButton("🌐 DNS Lookup")
        dns_btn.clicked.connect(self.dns_lookup)
        
        firewall_btn = QPushButton("🛡️ Firewall Status")
        firewall_btn.clicked.connect(self.firewall_status)
        
        tools_layout.addWidget(ping_btn, 0, 0)
        tools_layout.addWidget(traceroute_btn, 0, 1)
        tools_layout.addWidget(portscan_btn, 1, 0)
        tools_layout.addWidget(bandwidth_btn, 1, 1)
        tools_layout.addWidget(dns_btn, 2, 0)
        tools_layout.addWidget(firewall_btn, 2, 1)
        
        # Results area
        results_label = QLabel("Results:")
        results_label.setObjectName("muted")
        results_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        
        self.results_text = QTextEdit()
        self.results_text.setMaximumHeight(150)
        self.results_text.setReadOnly(True)
        self.results_text.setPlainText("Network tools ready. Click a tool to run diagnostics.")
        
        layout.addLayout(tools_layout)
        layout.addWidget(results_label)
        layout.addWidget(self.results_text)
        
    def ping_test(self):
        """Run ping test"""
        self.results_text.setPlainText(
            "Ping Test Results:\n"
            "==================\n"
            "PING google.com (**************): 56 data bytes\n"
            "64 bytes from **************: icmp_seq=0 ttl=118 time=12.3 ms\n"
            "64 bytes from **************: icmp_seq=1 ttl=118 time=11.8 ms\n"
            "64 bytes from **************: icmp_seq=2 ttl=118 time=12.1 ms\n\n"
            "--- google.com ping statistics ---\n"
            "3 packets transmitted, 3 received, 0% packet loss\n"
            "round-trip min/avg/max/stddev = 11.8/12.1/12.3/0.2 ms"
        )
        
    def traceroute(self):
        """Run traceroute"""
        self.results_text.setPlainText(
            "Traceroute Results:\n"
            "==================\n"
            "traceroute to google.com (**************), 30 hops max\n"
            " 1  *********** (***********)  1.234 ms  1.123 ms  1.089 ms\n"
            " 2  ******** (********)  8.456 ms  8.234 ms  8.123 ms\n"
            " 3  *********** (***********)  12.345 ms  12.234 ms  12.123 ms\n"
            " 4  * * *\n"
            " 5  ************** (**************)  15.678 ms  15.567 ms  15.456 ms"
        )
        
    def port_scan(self):
        """Run port scan"""
        self.results_text.setPlainText(
            "Port Scan Results:\n"
            "==================\n"
            "Scanning localhost (127.0.0.1)...\n\n"
            "Open ports:\n"
            "22/tcp   ssh\n"
            "80/tcp   http\n"
            "443/tcp  https\n"
            "5432/tcp postgresql\n"
            "6379/tcp redis\n"
            "8001/tcp http-alt\n\n"
            "Scan completed in 2.34 seconds"
        )
        
    def bandwidth_test(self):
        """Run bandwidth test"""
        self.results_text.setPlainText(
            "Bandwidth Test Results:\n"
            "======================\n"
            "Testing download speed...\n"
            "Download: 45.67 Mbps\n\n"
            "Testing upload speed...\n"
            "Upload: 12.34 Mbps\n\n"
            "Ping: 12 ms\n"
            "Jitter: 2 ms\n\n"
            "Test completed successfully"
        )
        
    def dns_lookup(self):
        """Run DNS lookup"""
        self.results_text.setPlainText(
            "DNS Lookup Results:\n"
            "==================\n"
            "Domain: google.com\n\n"
            "A Records:\n"
            "**************\n"
            "142.250.191.46\n\n"
            "AAAA Records:\n"
            "2607:f8b0:4004:c1b::65\n"
            "2607:f8b0:4004:c1b::71\n\n"
            "MX Records:\n"
            "10 smtp.google.com"
        )
        
    def firewall_status(self):
        """Check firewall status"""
        self.results_text.setPlainText(
            "Firewall Status:\n"
            "===============\n"
            "Status: Active\n"
            "Default Policy: DENY\n\n"
            "Active Rules:\n"
            "ALLOW 22/tcp (SSH)\n"
            "ALLOW 80/tcp (HTTP)\n"
            "ALLOW 443/tcp (HTTPS)\n"
            "ALLOW 8001/tcp (Backend)\n"
            "DENY ALL others\n\n"
            "Blocked connections: 23 today"
        )


class NetworkWidget(QWidget):
    """Main network monitoring widget"""
    
    def __init__(self, api_client):
        super().__init__()
        self.api_client = api_client
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the network widget UI"""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(30)
        
        # Top row - Status and tools
        top_layout = QHBoxLayout()
        top_layout.setSpacing(30)
        
        # Network status
        self.status_widget = NetworkStatusCard()
        
        # Network tools
        self.tools_widget = NetworkToolsWidget()
        
        top_layout.addWidget(self.status_widget)
        top_layout.addWidget(self.tools_widget)
        
        # Bottom row - Connections and security events
        bottom_layout = QHBoxLayout()
        bottom_layout.setSpacing(30)
        
        # Connections table
        self.connections_widget = ConnectionsTableWidget()
        
        # Security events
        self.security_widget = SecurityEventsWidget()
        
        bottom_layout.addWidget(self.connections_widget, 2)
        bottom_layout.addWidget(self.security_widget, 1)
        
        # Add to main layout
        main_layout.addLayout(top_layout)
        main_layout.addLayout(bottom_layout, 1)
        
    def update_data(self):
        """Update network data from API"""
        try:
            # Get network status
            network_status = self.api_client.get_network_status()
            
            if network_status:
                # Update status widget
                throughput = network_status.get('throughput', '23.5 Mbps')
                latency = network_status.get('latency', '12ms')
                packet_loss = network_status.get('packet_loss', '0.1%')
                online = network_status.get('online', True)
                
                self.status_widget.update_status(throughput, latency, packet_loss, online)
                
            # Get security events
            security_status = self.api_client.get_security_status()
            if security_status:
                # Add new security events
                events = security_status.get('recent_events', [])
                for event in events[-5:]:  # Add last 5 events
                    level = event.get('level', 'info')
                    message = event.get('message', 'Security event')
                    self.security_widget.add_event(level, message)
                    
        except Exception as e:
            print(f"Error updating network data: {e}")
