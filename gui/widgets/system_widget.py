"""
System Widget for Offline Sentinel PyQt6 Application
System monitoring and performance interface
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, 
    QFrame, QProgressBar, QPushButton, QScrollArea, QTextEdit,
    QTabWidget, QListWidget, QListWidgetItem, QSplitter
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont
from typing import Dict, Any, List


class SystemMetricCard(QFrame):
    """Individual system metric card"""
    
    def __init__(self, title: str, value: str = "0", unit: str = "", icon: str = "📊"):
        super().__init__()
        self.setObjectName("stat-card")
        self.title_text = title
        self.value_text = value
        self.unit_text = unit
        self.icon_text = icon
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the metric card UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(8)
        
        # Header
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(self.icon_text)
        icon_label.setFont(QFont("Arial", 18))
        
        title_label = QLabel(self.title_text)
        title_label.setObjectName("secondary")
        title_label.setFont(QFont("Arial", 11))
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # Value
        value_layout = QHBoxLayout()
        
        self.value_label = QLabel(self.value_text)
        self.value_label.setFont(QFont("Arial", 20, QFont.Weight.Bold))
        
        self.unit_label = QLabel(self.unit_text)
        self.unit_label.setObjectName("muted")
        self.unit_label.setFont(QFont("Arial", 12))
        
        value_layout.addWidget(self.value_label)
        value_layout.addWidget(self.unit_label)
        value_layout.addStretch()
        
        # Progress bar (optional)
        self.progress_bar = QProgressBar()
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.setFixedHeight(6)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setVisible(False)
        
        layout.addLayout(header_layout)
        layout.addLayout(value_layout)
        layout.addWidget(self.progress_bar)
        
    def update_value(self, value: str, unit: str = "", progress: int = None):
        """Update the metric value"""
        self.value_label.setText(value)
        if unit:
            self.unit_label.setText(unit)
        if progress is not None:
            self.progress_bar.setValue(progress)
            self.progress_bar.setVisible(True)


class SystemHealthWidget(QFrame):
    """System health overview widget"""
    
    def __init__(self):
        super().__init__()
        self.setObjectName("card")
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the system health UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Header
        header = QLabel("💚 System Health Overview")
        header.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(header)
        
        # Health metrics grid
        metrics_grid = QGridLayout()
        
        # CPU Usage
        self.cpu_metric = SystemMetricCard("CPU Usage", "45", "%", "🔥")
        metrics_grid.addWidget(self.cpu_metric, 0, 0)
        
        # Memory Usage
        self.memory_metric = SystemMetricCard("Memory", "5.7", "GB", "🧠")
        metrics_grid.addWidget(self.memory_metric, 0, 1)
        
        # Disk Usage
        self.disk_metric = SystemMetricCard("Disk Space", "52", "%", "💾")
        metrics_grid.addWidget(self.disk_metric, 1, 0)
        
        # Network
        self.network_metric = SystemMetricCard("Network", "23.5", "Mbps", "📡")
        metrics_grid.addWidget(self.network_metric, 1, 1)
        
        layout.addLayout(metrics_grid)
        
        # Overall health score
        health_layout = QHBoxLayout()
        
        health_label = QLabel("Overall Health Score:")
        health_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        
        self.health_score = QLabel("94%")
        self.health_score.setFont(QFont("Arial", 24, QFont.Weight.Bold))
        self.health_score.setObjectName("success")
        
        health_layout.addWidget(health_label)
        health_layout.addStretch()
        health_layout.addWidget(self.health_score)
        
        layout.addLayout(health_layout)
        
    def update_metrics(self, cpu: int, memory: float, disk: int, network: float, health: int):
        """Update all health metrics"""
        self.cpu_metric.update_value(str(cpu), "%", cpu)
        self.memory_metric.update_value(f"{memory:.1f}", "GB", int(memory * 12.5))  # Assuming 8GB max
        self.disk_metric.update_value(str(disk), "%", disk)
        self.network_metric.update_value(f"{network:.1f}", "Mbps", min(int(network * 2), 100))
        self.health_score.setText(f"{health}%")


class ProcessListWidget(QFrame):
    """Process list widget"""
    
    def __init__(self):
        super().__init__()
        self.setObjectName("card")
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the process list UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Header
        header_layout = QHBoxLayout()
        
        header = QLabel("⚙️ System Processes")
        header.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        
        refresh_btn = QPushButton("🔄 Refresh")
        refresh_btn.clicked.connect(self.refresh_processes)
        
        header_layout.addWidget(header)
        header_layout.addStretch()
        header_layout.addWidget(refresh_btn)
        
        # Process list
        self.process_list = QListWidget()
        
        # Add sample processes
        processes = [
            "🤖 Research Agent - CPU: 12% - RAM: 256MB",
            "⚙️ System Agent - CPU: 8% - RAM: 128MB", 
            "🔧 Upgrade Agent - CPU: 15% - RAM: 312MB",
            "📡 Communication Agent - CPU: 6% - RAM: 89MB",
            "🐍 Python Backend - CPU: 22% - RAM: 445MB",
            "🗄️ Database Service - CPU: 5% - RAM: 234MB",
            "📊 Monitoring Service - CPU: 3% - RAM: 67MB"
        ]
        
        for process in processes:
            item = QListWidgetItem(process)
            self.process_list.addItem(item)
            
        layout.addLayout(header_layout)
        layout.addWidget(self.process_list)
        
    def refresh_processes(self):
        """Refresh the process list"""
        # This would fetch real process data
        print("Refreshing process list...")


class SystemLogsWidget(QFrame):
    """System logs widget"""
    
    def __init__(self):
        super().__init__()
        self.setObjectName("card")
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the system logs UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Header
        header_layout = QHBoxLayout()
        
        header = QLabel("📋 System Logs")
        header.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        
        clear_btn = QPushButton("🗑️ Clear")
        clear_btn.clicked.connect(self.clear_logs)
        
        export_btn = QPushButton("📤 Export")
        export_btn.clicked.connect(self.export_logs)
        
        header_layout.addWidget(header)
        header_layout.addStretch()
        header_layout.addWidget(clear_btn)
        header_layout.addWidget(export_btn)
        
        # Logs display
        self.logs_text = QTextEdit()
        self.logs_text.setReadOnly(True)
        self.logs_text.setFont(QFont("Consolas", 10))
        
        # Sample log entries
        sample_logs = """[2024-01-15 14:23:45] INFO - System startup completed successfully
[2024-01-15 14:23:46] INFO - Research Agent initialized
[2024-01-15 14:23:47] INFO - System Agent started monitoring
[2024-01-15 14:23:48] INFO - Communication Agent established connections
[2024-01-15 14:24:12] INFO - Security scan completed - No threats detected
[2024-01-15 14:24:35] WARNING - High memory usage detected (78%)
[2024-01-15 14:24:36] INFO - Memory optimization triggered
[2024-01-15 14:24:45] INFO - Memory usage normalized (65%)
[2024-01-15 14:25:12] INFO - Agent performance optimization completed
[2024-01-15 14:25:30] INFO - System health check - All systems operational"""
        
        self.logs_text.setPlainText(sample_logs)
        
        layout.addLayout(header_layout)
        layout.addWidget(self.logs_text)
        
    def clear_logs(self):
        """Clear the logs display"""
        self.logs_text.clear()
        
    def export_logs(self):
        """Export system logs"""
        # This would implement log export functionality
        print("Exporting system logs...")
        
    def add_log_entry(self, level: str, message: str):
        """Add a new log entry"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {level.upper()} - {message}"
        self.logs_text.append(log_entry)


class SystemWidget(QWidget):
    """Main system monitoring widget"""
    
    def __init__(self, api_client):
        super().__init__()
        self.api_client = api_client
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the system widget UI"""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(30)
        
        # Top section - System health
        self.health_widget = SystemHealthWidget()
        main_layout.addWidget(self.health_widget)
        
        # Bottom section - Processes and logs
        bottom_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left side - Processes
        self.process_widget = ProcessListWidget()
        bottom_splitter.addWidget(self.process_widget)
        
        # Right side - Logs
        self.logs_widget = SystemLogsWidget()
        bottom_splitter.addWidget(self.logs_widget)
        
        # Set splitter proportions
        bottom_splitter.setSizes([400, 600])
        
        main_layout.addWidget(bottom_splitter, 1)
        
        # Control buttons
        controls_layout = QHBoxLayout()
        
        optimize_btn = QPushButton("⚡ Optimize System")
        optimize_btn.setObjectName("success")
        optimize_btn.clicked.connect(self.optimize_system)
        
        cleanup_btn = QPushButton("🧹 Cleanup")
        cleanup_btn.clicked.connect(self.cleanup_system)
        
        restart_btn = QPushButton("🔄 Restart Services")
        restart_btn.setObjectName("warning")
        restart_btn.clicked.connect(self.restart_services)
        
        controls_layout.addWidget(optimize_btn)
        controls_layout.addWidget(cleanup_btn)
        controls_layout.addWidget(restart_btn)
        controls_layout.addStretch()
        
        main_layout.addLayout(controls_layout)
        
    def update_data(self):
        """Update system data from API"""
        try:
            # Get live system stats
            live_stats = self.api_client.get_live_stats()
            
            if live_stats:
                system_health = live_stats.get('system_health', {})
                
                # Parse metrics
                cpu = int(system_health.get('cpu_usage', '45%').replace('%', ''))
                memory_pct = int(system_health.get('memory_usage', '68%').replace('%', ''))
                memory_gb = memory_pct * 0.08  # Convert to GB (assuming 8GB max)
                disk = int(system_health.get('disk_usage', '52%').replace('%', ''))
                network_str = system_health.get('network_throughput', '23.5 Mbps')
                network = float(network_str.split()[0]) if network_str else 0
                health = system_health.get('overall', 94)
                
                # Update health widget
                self.health_widget.update_metrics(cpu, memory_gb, disk, network, health)
                
            # Get recent logs
            recent_logs = self.api_client.get_recent_logs()
            if recent_logs:
                self.update_logs(recent_logs)
                
        except Exception as e:
            print(f"Error updating system data: {e}")
            
    def update_logs(self, logs_data: Dict[str, Any]):
        """Update the logs display"""
        entries = logs_data.get('entries', [])
        if entries:
            # Clear existing logs and add new ones
            self.logs_widget.logs_text.clear()
            
            for entry in entries[-20:]:  # Show last 20 entries
                level = entry.get('level', 'INFO')
                message = entry.get('message', 'System activity')
                source = entry.get('source', 'System')
                
                # Format log entry
                log_message = f"{source}: {message}"
                self.logs_widget.add_log_entry(level, log_message)
                
    def optimize_system(self):
        """Optimize system performance"""
        try:
            result = self.api_client.optimize_agents()
            if result:
                self.logs_widget.add_log_entry("INFO", "System optimization initiated")
        except Exception as e:
            self.logs_widget.add_log_entry("ERROR", f"Optimization failed: {e}")
            
    def cleanup_system(self):
        """Cleanup system resources"""
        self.logs_widget.add_log_entry("INFO", "System cleanup initiated")
        # This would implement actual cleanup logic
        
    def restart_services(self):
        """Restart system services"""
        self.logs_widget.add_log_entry("WARNING", "Service restart initiated")
        # This would implement service restart logic
