"""
Sub-Agent Management Widget for PyQt6 Application
Provides visual management of AI sub-agents with network view and monitoring
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                            QFrame, QScrollArea, QGridLayout, QListWidget, QListWidgetItem,
                            QTabWidget, QTextEdit, QProgressBar, QGroupBox, QComboBox,
                            QLineEdit, QDialog, QFormLayout, QCheckBox, QSpinBox,
                            QMessageBox, QSplitter, QGraphicsView, QGraphicsScene,
                            QGraphicsEllipseItem, QGraphicsLineItem, QGraphicsTextItem)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread, QObject, QPointF
from PyQt6.QtGui import QFont, QColor, QPen, QBrush, QPainter
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum


class AgentStatus(Enum):
    IDLE = "idle"
    ACTIVE = "active"
    BUSY = "busy"
    ERROR = "error"
    COMPLETED = "completed"


class AgentType(Enum):
    RESEARCH = "research"
    ANALYSIS = "analysis"
    COMMUNICATION = "communication"
    SECURITY = "security"
    SYSTEM = "system"
    CUSTOM = "custom"


@dataclass
class SubAgent:
    id: str
    name: str
    type: AgentType
    status: AgentStatus
    current_task: str
    progress: int
    created_at: datetime
    last_activity: datetime
    capabilities: List[str]
    connections: List[str]
    metrics: Dict[str, Any]
    position: Optional[Dict[str, float]] = None


class AgentNetworkView(QGraphicsView):
    """Network visualization for sub-agents"""
    
    agent_selected = pyqtSignal(str)  # agent_id
    
    def __init__(self):
        super().__init__()
        self.scene = QGraphicsScene()
        self.setScene(self.scene)
        self.agents = {}
        self.connections = {}
        self.agent_items = {}
        self.connection_items = {}
        
        # Setup view
        self.setRenderHint(QPainter.RenderHint.Antialiasing)
        self.setDragMode(QGraphicsView.DragMode.RubberBandDrag)
        self.setBackgroundBrush(QBrush(QColor(10, 10, 10)))
        
        # Animation timer
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update_animations)
        self.animation_timer.start(100)  # Update every 100ms
        
    def add_agent(self, agent: SubAgent):
        """Add agent to network view"""
        self.agents[agent.id] = agent
        
        # Create visual representation
        x = agent.position.get('x', 0) if agent.position else 0
        y = agent.position.get('y', 0) if agent.position else 0
        
        # Agent circle
        circle = QGraphicsEllipseItem(x - 25, y - 25, 50, 50)
        circle.setBrush(QBrush(self.get_status_color(agent.status)))
        circle.setPen(QPen(QColor(255, 255, 255, 100), 2))
        circle.setFlag(circle.GraphicsItemFlag.ItemIsMovable)
        circle.setFlag(circle.GraphicsItemFlag.ItemIsSelectable)
        
        # Agent label
        text = QGraphicsTextItem(agent.name)
        text.setPos(x - 25, y + 30)
        text.setDefaultTextColor(QColor(255, 255, 255))
        font = QFont("Segoe UI", 8)
        text.setFont(font)
        
        self.agent_items[agent.id] = {'circle': circle, 'text': text}
        self.scene.addItem(circle)
        self.scene.addItem(text)
        
    def get_status_color(self, status: AgentStatus) -> QColor:
        """Get color for agent status"""
        colors = {
            AgentStatus.IDLE: QColor(142, 142, 147),
            AgentStatus.ACTIVE: QColor(48, 209, 88),
            AgentStatus.BUSY: QColor(255, 149, 0),
            AgentStatus.ERROR: QColor(255, 59, 48),
            AgentStatus.COMPLETED: QColor(100, 210, 255)
        }
        return colors.get(status, QColor(142, 142, 147))
        
    def update_agent(self, agent: SubAgent):
        """Update agent visualization"""
        if agent.id in self.agent_items:
            items = self.agent_items[agent.id]
            items['circle'].setBrush(QBrush(self.get_status_color(agent.status)))
            items['text'].setPlainText(agent.name)
            
    def add_connection(self, from_id: str, to_id: str, connection_type: str = "data"):
        """Add connection between agents"""
        if from_id in self.agent_items and to_id in self.agent_items:
            from_item = self.agent_items[from_id]['circle']
            to_item = self.agent_items[to_id]['circle']
            
            from_pos = from_item.pos() + QPointF(25, 25)
            to_pos = to_item.pos() + QPointF(25, 25)
            
            line = QGraphicsLineItem(from_pos.x(), from_pos.y(), to_pos.x(), to_pos.y())
            
            if connection_type == "command":
                line.setPen(QPen(QColor(0, 122, 255), 2))
            elif connection_type == "data":
                line.setPen(QPen(QColor(100, 210, 255), 1, Qt.PenStyle.DashLine))
            else:
                line.setPen(QPen(QColor(255, 255, 255, 100), 1))
                
            self.connection_items[f"{from_id}-{to_id}"] = line
            self.scene.addItem(line)
            
    def update_animations(self):
        """Update visual animations"""
        # Pulse effect for active agents
        for agent_id, agent in self.agents.items():
            if agent.status == AgentStatus.ACTIVE and agent_id in self.agent_items:
                circle = self.agent_items[agent_id]['circle']
                # Simple pulse effect by varying opacity
                opacity = 0.7 + 0.3 * abs(time.time() % 2 - 1)
                circle.setOpacity(opacity)


class AgentCreationDialog(QDialog):
    """Dialog for creating new sub-agents"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Create New Sub-Agent")
        self.setModal(True)
        self.resize(400, 300)
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup dialog UI"""
        layout = QVBoxLayout(self)
        
        # Form layout
        form_layout = QFormLayout()
        
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("Enter agent name...")
        form_layout.addRow("Name:", self.name_input)
        
        self.type_combo = QComboBox()
        for agent_type in AgentType:
            self.type_combo.addItem(agent_type.value.title(), agent_type)
        form_layout.addRow("Type:", self.type_combo)
        
        self.capabilities_input = QLineEdit()
        self.capabilities_input.setPlaceholderText("capability1, capability2, ...")
        form_layout.addRow("Capabilities:", self.capabilities_input)
        
        self.auto_start = QCheckBox("Start immediately")
        self.auto_start.setChecked(True)
        form_layout.addRow("", self.auto_start)
        
        layout.addLayout(form_layout)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.create_btn = QPushButton("Create Agent")
        self.create_btn.clicked.connect(self.accept)
        
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.create_btn)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
    def get_agent_data(self) -> Dict[str, Any]:
        """Get agent creation data"""
        capabilities = [cap.strip() for cap in self.capabilities_input.text().split(',') if cap.strip()]
        
        return {
            'name': self.name_input.text() or f"Agent {int(time.time())}",
            'type': self.type_combo.currentData(),
            'capabilities': capabilities,
            'auto_start': self.auto_start.isChecked()
        }


class SubAgentWidget(QWidget):
    """Main sub-agent management widget"""
    
    agent_created = pyqtSignal(dict)
    agent_removed = pyqtSignal(str)
    task_assigned = pyqtSignal(str, str)  # agent_id, task
    
    def __init__(self):
        super().__init__()
        self.agents: Dict[str, SubAgent] = {}
        self.setup_ui()
        self.setup_sample_agents()
        
        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_agent_status)
        self.update_timer.start(5000)  # Update every 5 seconds
        
    def setup_ui(self):
        """Setup main UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Header
        header = self.create_header()
        layout.addWidget(header)
        
        # Main content
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left panel - Agent list and controls
        left_panel = self.create_left_panel()
        
        # Center panel - Network view
        self.network_view = AgentNetworkView()
        self.network_view.agent_selected.connect(self.select_agent)
        
        # Right panel - Agent details
        right_panel = self.create_right_panel()
        
        splitter.addWidget(left_panel)
        splitter.addWidget(self.network_view)
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([250, 500, 300])
        
        layout.addWidget(splitter)
        
        # Apply styling
        self.setStyleSheet("""
            QWidget {
                background: #000000;
                color: #ffffff;
                font-family: 'Segoe UI';
            }
            QFrame {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
            }
            QPushButton {
                background: #007aff;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
                color: white;
                font-weight: 600;
            }
            QPushButton:hover {
                background: #0056cc;
            }
            QListWidget {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
            }
            QListWidget::item {
                padding: 8px;
                border-radius: 6px;
                margin: 2px;
            }
            QListWidget::item:selected {
                background: #007aff;
            }
            QTabWidget::pane {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
            }
            QTabBar::tab {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                padding: 8px 16px;
                margin-right: 2px;
                border-radius: 6px;
            }
            QTabBar::tab:selected {
                background: #007aff;
            }
        """)
        
    def create_header(self) -> QWidget:
        """Create header widget"""
        header = QFrame()
        header.setFixedHeight(60)
        header.setObjectName("header")
        
        layout = QHBoxLayout(header)
        layout.setContentsMargins(16, 8, 16, 8)
        
        # Title
        title = QLabel("🤖 Sub-Agent Network")
        title.setFont(QFont("Segoe UI", 16, QFont.Weight.Bold))
        
        # Agent count
        self.agent_count_label = QLabel("0 agents active")
        self.agent_count_label.setStyleSheet("color: #8e8e93; font-size: 12px;")
        
        # Controls
        self.create_agent_btn = QPushButton("+ Create Agent")
        self.create_agent_btn.clicked.connect(self.create_agent)
        
        self.refresh_btn = QPushButton("🔄 Refresh")
        self.refresh_btn.clicked.connect(self.refresh_agents)
        
        layout.addWidget(title)
        layout.addWidget(self.agent_count_label)
        layout.addStretch()
        layout.addWidget(self.create_agent_btn)
        layout.addWidget(self.refresh_btn)
        
        return header
        
    def create_left_panel(self) -> QWidget:
        """Create left panel with agent list"""
        panel = QFrame()
        panel.setFixedWidth(250)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(16, 16, 16, 16)
        
        # Agent list
        list_label = QLabel("Active Agents")
        list_label.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        
        self.agent_list = QListWidget()
        self.agent_list.itemClicked.connect(self.on_agent_list_selection)
        
        # Quick actions
        actions_label = QLabel("Quick Actions")
        actions_label.setFont(QFont("Segoe UI", 12, QFont.Weight.Bold))
        
        self.start_all_btn = QPushButton("▶️ Start All")
        self.start_all_btn.clicked.connect(self.start_all_agents)
        
        self.stop_all_btn = QPushButton("⏹️ Stop All")
        self.stop_all_btn.clicked.connect(self.stop_all_agents)
        
        self.clear_completed_btn = QPushButton("🗑️ Clear Completed")
        self.clear_completed_btn.clicked.connect(self.clear_completed_agents)
        
        layout.addWidget(list_label)
        layout.addWidget(self.agent_list)
        layout.addWidget(actions_label)
        layout.addWidget(self.start_all_btn)
        layout.addWidget(self.stop_all_btn)
        layout.addWidget(self.clear_completed_btn)
        layout.addStretch()
        
        return panel
        
    def create_right_panel(self) -> QWidget:
        """Create right panel with agent details"""
        panel = QFrame()
        panel.setFixedWidth(300)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(16, 16, 16, 16)
        
        # Details tabs
        self.details_tabs = QTabWidget()
        
        # Overview tab
        overview_tab = QWidget()
        overview_layout = QVBoxLayout(overview_tab)
        
        self.agent_name_label = QLabel("No agent selected")
        self.agent_name_label.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
        
        self.agent_status_label = QLabel("")
        self.agent_task_label = QLabel("")
        self.agent_progress = QProgressBar()
        
        overview_layout.addWidget(self.agent_name_label)
        overview_layout.addWidget(self.agent_status_label)
        overview_layout.addWidget(self.agent_task_label)
        overview_layout.addWidget(self.agent_progress)
        overview_layout.addStretch()
        
        # Metrics tab
        metrics_tab = QWidget()
        metrics_layout = QVBoxLayout(metrics_tab)
        
        self.metrics_text = QTextEdit()
        self.metrics_text.setReadOnly(True)
        self.metrics_text.setMaximumHeight(150)
        
        metrics_layout.addWidget(self.metrics_text)
        
        # Actions tab
        actions_tab = QWidget()
        actions_layout = QVBoxLayout(actions_tab)
        
        self.assign_task_btn = QPushButton("📋 Assign Task")
        self.assign_task_btn.clicked.connect(self.assign_task)
        
        self.view_logs_btn = QPushButton("📄 View Logs")
        self.view_logs_btn.clicked.connect(self.view_logs)
        
        self.remove_agent_btn = QPushButton("🗑️ Remove Agent")
        self.remove_agent_btn.clicked.connect(self.remove_agent)
        self.remove_agent_btn.setStyleSheet("QPushButton { background: #ff3b30; }")
        
        actions_layout.addWidget(self.assign_task_btn)
        actions_layout.addWidget(self.view_logs_btn)
        actions_layout.addWidget(self.remove_agent_btn)
        actions_layout.addStretch()
        
        self.details_tabs.addTab(overview_tab, "Overview")
        self.details_tabs.addTab(metrics_tab, "Metrics")
        self.details_tabs.addTab(actions_tab, "Actions")
        
        layout.addWidget(self.details_tabs)
        
        return panel

    def setup_sample_agents(self):
        """Setup sample agents for demonstration"""
        sample_agents = [
            {
                'id': 'main-ai',
                'name': 'Main AI Assistant',
                'type': AgentType.SYSTEM,
                'status': AgentStatus.ACTIVE,
                'current_task': 'Coordinating sub-agents',
                'progress': 100,
                'capabilities': ['coordination', 'task-delegation', 'decision-making'],
                'connections': ['research-01', 'analysis-01', 'comm-01'],
                'metrics': {'tasks_completed': 156, 'success_rate': 98.5, 'avg_response_time': 1.2},
                'position': {'x': 250, 'y': 200}
            },
            {
                'id': 'research-01',
                'name': 'Research Specialist',
                'type': AgentType.RESEARCH,
                'status': AgentStatus.BUSY,
                'current_task': 'Analyzing PDF documents',
                'progress': 67,
                'capabilities': ['document-analysis', 'web-research', 'data-extraction'],
                'connections': ['main-ai', 'analysis-01'],
                'metrics': {'tasks_completed': 23, 'success_rate': 95.7, 'avg_response_time': 3.4},
                'position': {'x': 100, 'y': 100}
            },
            {
                'id': 'analysis-01',
                'name': 'Data Analyst',
                'type': AgentType.ANALYSIS,
                'status': AgentStatus.ACTIVE,
                'current_task': 'Processing research findings',
                'progress': 45,
                'capabilities': ['statistical-analysis', 'pattern-recognition', 'visualization'],
                'connections': ['main-ai', 'research-01'],
                'metrics': {'tasks_completed': 34, 'success_rate': 97.1, 'avg_response_time': 2.8},
                'position': {'x': 400, 'y': 100}
            },
            {
                'id': 'comm-01',
                'name': 'Communication Handler',
                'type': AgentType.COMMUNICATION,
                'status': AgentStatus.IDLE,
                'current_task': 'Monitoring chat channels',
                'progress': 0,
                'capabilities': ['natural-language', 'translation', 'sentiment-analysis'],
                'connections': ['main-ai'],
                'metrics': {'tasks_completed': 89, 'success_rate': 99.2, 'avg_response_time': 0.8},
                'position': {'x': 250, 'y': 300}
            }
        ]

        for agent_data in sample_agents:
            agent = SubAgent(
                id=agent_data['id'],
                name=agent_data['name'],
                type=agent_data['type'],
                status=agent_data['status'],
                current_task=agent_data['current_task'],
                progress=agent_data['progress'],
                created_at=datetime.now() - timedelta(hours=2),
                last_activity=datetime.now() - timedelta(minutes=5),
                capabilities=agent_data['capabilities'],
                connections=agent_data['connections'],
                metrics=agent_data['metrics'],
                position=agent_data['position']
            )

            self.agents[agent.id] = agent
            self.network_view.add_agent(agent)

        # Add connections
        connections = [
            ('main-ai', 'research-01', 'command'),
            ('main-ai', 'analysis-01', 'command'),
            ('main-ai', 'comm-01', 'command'),
            ('research-01', 'analysis-01', 'data')
        ]

        for from_id, to_id, conn_type in connections:
            self.network_view.add_connection(from_id, to_id, conn_type)

        self.update_agent_list()

    def update_agent_list(self):
        """Update the agent list widget"""
        self.agent_list.clear()

        for agent in self.agents.values():
            status_icon = self.get_status_icon(agent.status)
            type_icon = self.get_type_icon(agent.type)

            item_text = f"{status_icon} {type_icon} {agent.name}"
            item = QListWidgetItem(item_text)
            item.setData(Qt.ItemDataRole.UserRole, agent.id)

            # Color based on status
            if agent.status == AgentStatus.ACTIVE:
                item.setForeground(QColor(48, 209, 88))
            elif agent.status == AgentStatus.BUSY:
                item.setForeground(QColor(255, 149, 0))
            elif agent.status == AgentStatus.ERROR:
                item.setForeground(QColor(255, 59, 48))
            else:
                item.setForeground(QColor(142, 142, 147))

            self.agent_list.addItem(item)

        # Update count
        self.agent_count_label.setText(f"{len(self.agents)} agents active")

    def get_status_icon(self, status: AgentStatus) -> str:
        """Get icon for agent status"""
        icons = {
            AgentStatus.IDLE: "⚪",
            AgentStatus.ACTIVE: "🟢",
            AgentStatus.BUSY: "🟡",
            AgentStatus.ERROR: "🔴",
            AgentStatus.COMPLETED: "🔵"
        }
        return icons.get(status, "⚪")

    def get_type_icon(self, agent_type: AgentType) -> str:
        """Get icon for agent type"""
        icons = {
            AgentType.RESEARCH: "🔍",
            AgentType.ANALYSIS: "📊",
            AgentType.COMMUNICATION: "💬",
            AgentType.SECURITY: "🛡️",
            AgentType.SYSTEM: "⚙️",
            AgentType.CUSTOM: "🤖"
        }
        return icons.get(agent_type, "🤖")

    def create_agent(self):
        """Create new agent"""
        dialog = AgentCreationDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            agent_data = dialog.get_agent_data()

            # Create new agent
            agent_id = f"agent-{int(time.time())}"
            agent = SubAgent(
                id=agent_id,
                name=agent_data['name'],
                type=agent_data['type'],
                status=AgentStatus.ACTIVE if agent_data['auto_start'] else AgentStatus.IDLE,
                current_task="Initializing..." if agent_data['auto_start'] else "Waiting for task",
                progress=0,
                created_at=datetime.now(),
                last_activity=datetime.now(),
                capabilities=agent_data['capabilities'],
                connections=['main-ai'],  # Connect to main AI by default
                metrics={'tasks_completed': 0, 'success_rate': 0, 'avg_response_time': 0},
                position={'x': 150 + len(self.agents) * 50, 'y': 150 + len(self.agents) * 30}
            )

            self.agents[agent_id] = agent
            self.network_view.add_agent(agent)
            self.network_view.add_connection('main-ai', agent_id, 'command')

            self.update_agent_list()
            self.agent_created.emit(asdict(agent))

    def remove_agent(self):
        """Remove selected agent"""
        current_item = self.agent_list.currentItem()
        if not current_item:
            QMessageBox.information(self, "No Selection", "Please select an agent to remove.")
            return

        agent_id = current_item.data(Qt.ItemDataRole.UserRole)
        if agent_id == 'main-ai':
            QMessageBox.warning(self, "Cannot Remove", "Cannot remove the main AI agent.")
            return

        agent = self.agents.get(agent_id)
        if not agent:
            return

        reply = QMessageBox.question(
            self,
            "Confirm Removal",
            f"Are you sure you want to remove agent '{agent.name}'?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            del self.agents[agent_id]
            self.update_agent_list()
            self.agent_removed.emit(agent_id)

            # Clear details if this agent was selected
            if hasattr(self, 'selected_agent_id') and self.selected_agent_id == agent_id:
                self.clear_agent_details()

    def on_agent_list_selection(self, item: QListWidgetItem):
        """Handle agent list selection"""
        agent_id = item.data(Qt.ItemDataRole.UserRole)
        self.select_agent(agent_id)

    def select_agent(self, agent_id: str):
        """Select and display agent details"""
        if agent_id not in self.agents:
            return

        self.selected_agent_id = agent_id
        agent = self.agents[agent_id]

        # Update details panel
        self.agent_name_label.setText(agent.name)
        self.agent_status_label.setText(f"Status: {agent.status.value.title()}")
        self.agent_task_label.setText(f"Task: {agent.current_task}")
        self.agent_progress.setValue(agent.progress)

        # Update metrics
        metrics_text = f"""
Agent ID: {agent.id}
Type: {agent.type.value.title()}
Created: {agent.created_at.strftime('%Y-%m-%d %H:%M')}
Last Activity: {agent.last_activity.strftime('%Y-%m-%d %H:%M')}

Capabilities:
{chr(10).join(f"• {cap}" for cap in agent.capabilities)}

Performance Metrics:
• Tasks Completed: {agent.metrics.get('tasks_completed', 0)}
• Success Rate: {agent.metrics.get('success_rate', 0):.1f}%
• Avg Response Time: {agent.metrics.get('avg_response_time', 0):.1f}s

Connections:
{chr(10).join(f"• {conn}" for conn in agent.connections)}
        """.strip()

        self.metrics_text.setPlainText(metrics_text)

    def clear_agent_details(self):
        """Clear agent details panel"""
        self.agent_name_label.setText("No agent selected")
        self.agent_status_label.setText("")
        self.agent_task_label.setText("")
        self.agent_progress.setValue(0)
        self.metrics_text.clear()

    def assign_task(self):
        """Assign task to selected agent"""
        if not hasattr(self, 'selected_agent_id'):
            QMessageBox.information(self, "No Selection", "Please select an agent first.")
            return

        from PyQt6.QtWidgets import QInputDialog
        task, ok = QInputDialog.getText(self, "Assign Task", "Enter task description:")
        if ok and task:
            agent = self.agents[self.selected_agent_id]
            agent.current_task = task
            agent.status = AgentStatus.BUSY
            agent.progress = 0
            agent.last_activity = datetime.now()

            self.network_view.update_agent(agent)
            self.select_agent(self.selected_agent_id)  # Refresh details
            self.task_assigned.emit(self.selected_agent_id, task)

    def view_logs(self):
        """View agent logs"""
        if not hasattr(self, 'selected_agent_id'):
            QMessageBox.information(self, "No Selection", "Please select an agent first.")
            return

        # Placeholder for log viewing
        QMessageBox.information(self, "Agent Logs", "Log viewing functionality would be implemented here.")

    def start_all_agents(self):
        """Start all idle agents"""
        for agent in self.agents.values():
            if agent.status == AgentStatus.IDLE:
                agent.status = AgentStatus.ACTIVE
                agent.current_task = "Ready for tasks"
                agent.last_activity = datetime.now()
                self.network_view.update_agent(agent)

        self.update_agent_list()

    def stop_all_agents(self):
        """Stop all non-system agents"""
        for agent in self.agents.values():
            if agent.type != AgentType.SYSTEM and agent.status in [AgentStatus.ACTIVE, AgentStatus.BUSY]:
                agent.status = AgentStatus.IDLE
                agent.current_task = "Stopped"
                agent.progress = 0
                agent.last_activity = datetime.now()
                self.network_view.update_agent(agent)

        self.update_agent_list()

    def clear_completed_agents(self):
        """Remove completed agents"""
        completed_agents = [agent_id for agent_id, agent in self.agents.items()
                          if agent.status == AgentStatus.COMPLETED and agent.type != AgentType.SYSTEM]

        for agent_id in completed_agents:
            del self.agents[agent_id]

        self.update_agent_list()

    def refresh_agents(self):
        """Refresh agent status"""
        for agent in self.agents.values():
            agent.last_activity = datetime.now()
            self.network_view.update_agent(agent)

        self.update_agent_list()

    def update_agent_status(self):
        """Periodic update of agent status"""
        # Simulate agent progress for busy agents
        for agent in self.agents.values():
            if agent.status == AgentStatus.BUSY and agent.progress < 100:
                agent.progress += 5
                if agent.progress >= 100:
                    agent.status = AgentStatus.COMPLETED
                    agent.current_task = "Task completed"

                agent.last_activity = datetime.now()
                self.network_view.update_agent(agent)

        # Update display if an agent is selected
        if hasattr(self, 'selected_agent_id') and self.selected_agent_id in self.agents:
            self.select_agent(self.selected_agent_id)

        self.update_agent_list()

    def get_active_agents(self) -> List[SubAgent]:
        """Get list of active agents"""
        return [agent for agent in self.agents.values()
                if agent.status in [AgentStatus.ACTIVE, AgentStatus.BUSY]]

    def get_agent_by_id(self, agent_id: str) -> Optional[SubAgent]:
        """Get agent by ID"""
        return self.agents.get(agent_id)
