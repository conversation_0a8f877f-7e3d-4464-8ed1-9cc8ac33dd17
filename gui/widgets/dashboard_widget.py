"""
Dashboard Widget for Offline Sentinel PyQt6 Application
Main dashboard with system overview and statistics
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, 
    QFrame, QProgressBar, QScrollArea, QPushButton, QTextEdit
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap
from typing import Dict, Any, Optional


class StatCard(QFrame):
    """Individual statistic card widget"""
    
    def __init__(self, title: str, value: str = "0", icon: str = "📊", color: str = "#3b82f6"):
        super().__init__()
        self.setObjectName("stat-card")
        self.title_text = title
        self.value_text = value
        self.icon_text = icon
        self.color = color
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the card UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(8)
        
        # Header with icon and title
        header_layout = QHBoxLayout()
        
        self.icon_label = QLabel(self.icon_text)
        self.icon_label.setFont(QFont("Arial", 20))
        self.icon_label.setFixedSize(32, 32)
        self.icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.title_label = QLabel(self.title_text)
        self.title_label.setObjectName("secondary")
        self.title_label.setFont(QFont("Arial", 12))
        
        header_layout.addWidget(self.icon_label)
        header_layout.addWidget(self.title_label)
        header_layout.addStretch()
        
        # Value
        self.value_label = QLabel(self.value_text)
        self.value_label.setFont(QFont("Arial", 24, QFont.Weight.Bold))
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        
        # Status indicator (optional)
        self.status_label = QLabel("")
        self.status_label.setObjectName("muted")
        self.status_label.setFont(QFont("Arial", 10))
        
        layout.addLayout(header_layout)
        layout.addWidget(self.value_label)
        layout.addWidget(self.status_label)
        layout.addStretch()
        
    def update_value(self, value: str, status: str = ""):
        """Update the card value and status"""
        self.value_label.setText(value)
        self.status_label.setText(status)
        
    def update_icon(self, icon: str):
        """Update the card icon"""
        self.icon_label.setText(icon)


class AgentStatusWidget(QFrame):
    """Widget showing agent status overview"""
    
    def __init__(self):
        super().__init__()
        self.setObjectName("card")
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the agent status UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Header
        header = QLabel("🤖 Agent Network Status")
        header.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(header)
        
        # Agent list
        self.agent_layout = QVBoxLayout()
        layout.addLayout(self.agent_layout)
        
        # Initialize with placeholder agents
        self.update_agents([])
        
    def update_agents(self, agents_data: list):
        """Update the agent status display"""
        # Clear existing widgets
        for i in reversed(range(self.agent_layout.count())):
            child = self.agent_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
                
        # Add agent status items
        if not agents_data:
            # Default agents if no data
            agents_data = [
                {"name": "Research Agent", "status": "active", "progress": 85},
                {"name": "System Agent", "status": "active", "progress": 99},
                {"name": "Upgrade Agent", "status": "active", "progress": 73},
                {"name": "Communication Agent", "status": "active", "progress": 95}
            ]
            
        for agent in agents_data:
            agent_widget = self.create_agent_item(agent)
            self.agent_layout.addWidget(agent_widget)
            
    def create_agent_item(self, agent_data: Dict[str, Any]) -> QWidget:
        """Create an individual agent status item"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 5, 0, 5)
        
        # Status indicator
        status = agent_data.get('status', 'unknown')
        status_icon = "🟢" if status == 'active' else "🔴" if status == 'error' else "🟡"
        status_label = QLabel(status_icon)
        status_label.setFixedSize(20, 20)
        
        # Agent name
        name_label = QLabel(agent_data.get('name', 'Unknown Agent'))
        name_label.setFont(QFont("Arial", 12, QFont.Weight.Medium))
        
        # Progress bar
        progress = agent_data.get('progress', 0)
        progress_bar = QProgressBar()
        progress_bar.setMaximum(100)
        progress_bar.setValue(progress)
        progress_bar.setFixedHeight(8)
        progress_bar.setTextVisible(False)
        
        # Progress text
        progress_label = QLabel(f"{progress}%")
        progress_label.setObjectName("muted")
        progress_label.setFixedWidth(40)
        progress_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        
        layout.addWidget(status_label)
        layout.addWidget(name_label)
        layout.addWidget(progress_bar, 1)
        layout.addWidget(progress_label)
        
        return widget


class SystemHealthWidget(QFrame):
    """Widget showing system health metrics"""
    
    def __init__(self):
        super().__init__()
        self.setObjectName("card")
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the system health UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Header
        header = QLabel("⚡ System Health")
        header.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(header)
        
        # Metrics grid
        metrics_layout = QGridLayout()
        
        # Create metric items
        self.cpu_progress = self.create_metric_item("CPU Usage", "45%", 45)
        self.memory_progress = self.create_metric_item("Memory", "68%", 68)
        self.disk_progress = self.create_metric_item("Disk", "52%", 52)
        self.network_progress = self.create_metric_item("Network", "23%", 23)
        
        metrics_layout.addWidget(self.cpu_progress, 0, 0)
        metrics_layout.addWidget(self.memory_progress, 0, 1)
        metrics_layout.addWidget(self.disk_progress, 1, 0)
        metrics_layout.addWidget(self.network_progress, 1, 1)
        
        layout.addLayout(metrics_layout)
        
    def create_metric_item(self, name: str, value: str, progress: int) -> QWidget:
        """Create a metric item with progress bar"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)
        
        # Name and value
        header_layout = QHBoxLayout()
        name_label = QLabel(name)
        name_label.setFont(QFont("Arial", 11))
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 11, QFont.Weight.Bold))
        value_label.setObjectName("muted")
        
        header_layout.addWidget(name_label)
        header_layout.addStretch()
        header_layout.addWidget(value_label)
        
        # Progress bar
        progress_bar = QProgressBar()
        progress_bar.setMaximum(100)
        progress_bar.setValue(progress)
        progress_bar.setFixedHeight(6)
        progress_bar.setTextVisible(False)
        
        layout.addLayout(header_layout)
        layout.addWidget(progress_bar)
        
        # Store references for updates
        widget.value_label = value_label
        widget.progress_bar = progress_bar
        
        return widget
        
    def update_metrics(self, cpu: int, memory: int, disk: int, network: int):
        """Update the health metrics"""
        self.cpu_progress.value_label.setText(f"{cpu}%")
        self.cpu_progress.progress_bar.setValue(cpu)
        
        self.memory_progress.value_label.setText(f"{memory}%")
        self.memory_progress.progress_bar.setValue(memory)
        
        self.disk_progress.value_label.setText(f"{disk}%")
        self.disk_progress.progress_bar.setValue(disk)
        
        self.network_progress.value_label.setText(f"{network}%")
        self.network_progress.progress_bar.setValue(network)


class DashboardWidget(QWidget):
    """Main dashboard widget"""
    
    data_updated = pyqtSignal(dict)
    
    def __init__(self, api_client):
        super().__init__()
        self.api_client = api_client
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the dashboard UI"""
        # Main scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # Main content widget
        content_widget = QWidget()
        scroll_area.setWidget(content_widget)
        
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll_area)
        
        # Content layout
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(30)
        
        # Statistics cards row
        self.create_stats_section(layout)
        
        # Main content row
        content_row = QHBoxLayout()
        content_row.setSpacing(30)
        
        # Left column
        left_column = QVBoxLayout()
        left_column.setSpacing(20)
        
        # Agent status
        self.agent_status_widget = AgentStatusWidget()
        left_column.addWidget(self.agent_status_widget)
        
        # System health
        self.system_health_widget = SystemHealthWidget()
        left_column.addWidget(self.system_health_widget)
        
        # Right column
        right_column = QVBoxLayout()
        right_column.setSpacing(20)
        
        # Recent activity
        self.create_activity_section(right_column)
        
        # Quick actions
        self.create_actions_section(right_column)
        
        content_row.addLayout(left_column, 1)
        content_row.addLayout(right_column, 1)
        
        layout.addLayout(content_row)
        
    def create_stats_section(self, layout: QVBoxLayout):
        """Create the statistics cards section"""
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(20)
        
        # Create stat cards
        self.system_health_card = StatCard("System Health", "94%", "💚", "#10b981")
        self.active_agents_card = StatCard("Active Agents", "4/4", "🤖", "#3b82f6")
        self.memory_usage_card = StatCard("Memory Usage", "5.7GB", "🧠", "#8b5cf6")
        self.network_status_card = StatCard("Network", "Online", "📡", "#f59e0b")
        
        stats_layout.addWidget(self.system_health_card)
        stats_layout.addWidget(self.active_agents_card)
        stats_layout.addWidget(self.memory_usage_card)
        stats_layout.addWidget(self.network_status_card)
        
        layout.addLayout(stats_layout)
        
    def create_activity_section(self, layout: QVBoxLayout):
        """Create the recent activity section"""
        activity_frame = QFrame()
        activity_frame.setObjectName("card")
        
        activity_layout = QVBoxLayout(activity_frame)
        activity_layout.setContentsMargins(20, 20, 20, 20)
        activity_layout.setSpacing(15)
        
        # Header
        header = QLabel("📋 Recent Activity")
        header.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        activity_layout.addWidget(header)
        
        # Activity log
        self.activity_log = QTextEdit()
        self.activity_log.setMaximumHeight(200)
        self.activity_log.setReadOnly(True)
        self.activity_log.setPlainText(
            "🔍 Research Agent: Analyzing security patterns...\n"
            "⚙️ System Agent: Performance optimization complete\n"
            "🔧 Upgrade Agent: Evaluating model improvements\n"
            "📡 Communication Agent: A2A protocol sync successful\n"
            "🛡️ Security scan completed - No threats detected\n"
            "📊 System health check - All systems operational"
        )
        activity_layout.addWidget(self.activity_log)
        
        layout.addWidget(activity_frame)
        
    def create_actions_section(self, layout: QVBoxLayout):
        """Create the quick actions section"""
        actions_frame = QFrame()
        actions_frame.setObjectName("card")
        
        actions_layout = QVBoxLayout(actions_frame)
        actions_layout.setContentsMargins(20, 20, 20, 20)
        actions_layout.setSpacing(15)
        
        # Header
        header = QLabel("⚡ Quick Actions")
        header.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        actions_layout.addWidget(header)
        
        # Action buttons
        buttons_layout = QGridLayout()
        
        scan_btn = QPushButton("🔍 Run Security Scan")
        scan_btn.clicked.connect(self.run_security_scan)
        
        optimize_btn = QPushButton("⚡ Optimize Agents")
        optimize_btn.clicked.connect(self.optimize_agents)
        
        export_btn = QPushButton("📤 Export Logs")
        export_btn.clicked.connect(self.export_logs)
        
        refresh_btn = QPushButton("🔄 Refresh Data")
        refresh_btn.clicked.connect(self.update_data)
        
        buttons_layout.addWidget(scan_btn, 0, 0)
        buttons_layout.addWidget(optimize_btn, 0, 1)
        buttons_layout.addWidget(export_btn, 1, 0)
        buttons_layout.addWidget(refresh_btn, 1, 1)
        
        actions_layout.addLayout(buttons_layout)
        layout.addWidget(actions_frame)
        
    def update_data(self):
        """Update dashboard data from API"""
        try:
            # Get comprehensive statistics
            stats = self.api_client.get_statistics()
            
            if stats:
                # Update stat cards
                self.update_stat_cards(stats)
                
                # Update agent status
                agents_data = stats.get('agents', [])
                self.agent_status_widget.update_agents(agents_data)
                
                # Update system health
                live_stats = stats.get('live_stats', {})
                system_health = live_stats.get('system_health', {})
                
                cpu = int(system_health.get('cpu_usage', '45%').replace('%', ''))
                memory = int(system_health.get('memory_usage', '68%').replace('%', ''))
                disk = int(system_health.get('disk_usage', '52%').replace('%', ''))
                network_throughput = system_health.get('network_throughput', '0 Mbps')
                network_pct = min(int(float(network_throughput.split()[0]) * 2), 100)
                
                self.system_health_widget.update_metrics(cpu, memory, disk, network_pct)
                
                # Update activity log
                self.update_activity_log(stats)
                
                # Emit signal
                self.data_updated.emit(stats)
                
        except Exception as e:
            print(f"Error updating dashboard data: {e}")
            
    def update_stat_cards(self, stats: Dict[str, Any]):
        """Update the statistics cards"""
        live_stats = stats.get('live_stats', {})
        agents_data = stats.get('agents', [])
        
        # System health
        system_health = live_stats.get('system_health', {})
        health_pct = system_health.get('overall', 94)
        health_status = "Excellent" if health_pct > 90 else "Good" if health_pct > 75 else "Warning"
        self.system_health_card.update_value(f"{health_pct}%", health_status)
        
        # Active agents
        active_count = len([a for a in agents_data if a.get('status') == 'active'])
        total_count = len(agents_data) if agents_data else 4
        self.active_agents_card.update_value(f"{active_count}/{total_count}", "All Online")
        
        # Memory usage
        memory_usage = system_health.get('memory_usage', '68%')
        memory_gb = f"{float(memory_usage.replace('%', '')) * 0.08:.1f}GB"
        self.memory_usage_card.update_value(memory_gb, f"{memory_usage} used")
        
        # Network status
        network_throughput = system_health.get('network_throughput', '0 Mbps')
        self.network_status_card.update_value("Online", f"{network_throughput}")
        
    def update_activity_log(self, stats: Dict[str, Any]):
        """Update the activity log"""
        recent_logs = stats.get('recent_logs', {})
        log_entries = recent_logs.get('entries', [])
        
        if log_entries:
            activity_text = ""
            for entry in log_entries[:6]:  # Show last 6 entries
                source = entry.get('source', 'System')
                message = entry.get('message', 'Activity update')
                level = entry.get('level', 'INFO')
                
                icon = "🔍" if "Research" in source else "⚙️" if "System" in source else "📡" if "Communication" in source else "🔧"
                activity_text += f"{icon} {source}: {message}\n"
                
            self.activity_log.setPlainText(activity_text)
            
    def run_security_scan(self):
        """Run a security scan"""
        try:
            result = self.api_client.start_research_task("security_analysis", "all_files", "standard", "high")
            if result:
                self.activity_log.append("🔍 Security scan initiated...")
        except Exception as e:
            print(f"Error starting security scan: {e}")
            
    def optimize_agents(self):
        """Optimize all agents"""
        try:
            result = self.api_client.optimize_agents()
            if result:
                self.activity_log.append("⚡ Agent optimization started...")
        except Exception as e:
            print(f"Error optimizing agents: {e}")
            
    def export_logs(self):
        """Export system logs"""
        try:
            result = self.api_client.export_data("logs", "json")
            if result:
                self.activity_log.append("📤 Log export initiated...")
        except Exception as e:
            print(f"Error exporting logs: {e}")


# Create __init__.py for widgets package
def create_widgets_init():
    """Create __init__.py for widgets package"""
    pass
