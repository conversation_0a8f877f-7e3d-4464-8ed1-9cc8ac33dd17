"""
Agents Widget for Offline Sentinel PyQt6 Application
Agent management and monitoring interface
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, 
    QFrame, QProgressBar, QPushButton, QScrollArea, QComboBox,
    QTextEdit, QTabWidget, QSplitter, QListWidget, QListWidgetItem
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap
from typing import Dict, Any, List


class AgentCard(QFrame):
    """Individual agent card widget"""
    
    agent_action = pyqtSignal(str, str)  # agent_name, action
    
    def __init__(self, agent_data: Dict[str, Any]):
        super().__init__()
        self.agent_data = agent_data
        self.setObjectName("stat-card")
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the agent card UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)
        
        # Header with name and status
        header_layout = QHBoxLayout()
        
        # Agent icon and name
        name_layout = QVBoxLayout()
        name_layout.setSpacing(4)
        
        self.name_label = QLabel(self.agent_data.get('name', 'Unknown Agent'))
        self.name_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        
        self.activity_label = QLabel(self.agent_data.get('activity', 'Idle'))
        self.activity_label.setObjectName("secondary")
        self.activity_label.setFont(QFont("Arial", 10))
        
        name_layout.addWidget(self.name_label)
        name_layout.addWidget(self.activity_label)
        
        # Status indicator
        status = self.agent_data.get('status', 'unknown')
        status_icon = "🟢" if status == 'active' else "🔴" if status == 'error' else "🟡"
        self.status_label = QLabel(status_icon)
        self.status_label.setFont(QFont("Arial", 20))
        self.status_label.setFixedSize(32, 32)
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        header_layout.addLayout(name_layout)
        header_layout.addStretch()
        header_layout.addWidget(self.status_label)
        
        # Progress section
        progress_layout = QVBoxLayout()
        progress_layout.setSpacing(4)
        
        progress_header = QHBoxLayout()
        progress_label = QLabel("Progress")
        progress_label.setObjectName("muted")
        progress_label.setFont(QFont("Arial", 10))
        
        self.progress_value = QLabel(f"{self.agent_data.get('progress', 0)}%")
        self.progress_value.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        
        progress_header.addWidget(progress_label)
        progress_header.addStretch()
        progress_header.addWidget(self.progress_value)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(self.agent_data.get('progress', 0))
        self.progress_bar.setFixedHeight(8)
        self.progress_bar.setTextVisible(False)
        
        progress_layout.addLayout(progress_header)
        progress_layout.addWidget(self.progress_bar)
        
        # Performance metrics
        metrics_layout = QHBoxLayout()
        
        cpu_label = QLabel("CPU: 12%")
        cpu_label.setObjectName("muted")
        cpu_label.setFont(QFont("Arial", 9))
        
        memory_label = QLabel("RAM: 256MB")
        memory_label.setObjectName("muted")
        memory_label.setFont(QFont("Arial", 9))
        
        metrics_layout.addWidget(cpu_label)
        metrics_layout.addStretch()
        metrics_layout.addWidget(memory_label)
        
        # Action buttons
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(8)
        
        self.pause_btn = QPushButton("⏸️")
        self.pause_btn.setFixedSize(32, 32)
        self.pause_btn.setToolTip("Pause Agent")
        self.pause_btn.clicked.connect(lambda: self.agent_action.emit(self.agent_data.get('name', ''), 'pause'))
        
        self.restart_btn = QPushButton("🔄")
        self.restart_btn.setFixedSize(32, 32)
        self.restart_btn.setToolTip("Restart Agent")
        self.restart_btn.clicked.connect(lambda: self.agent_action.emit(self.agent_data.get('name', ''), 'restart'))
        
        self.details_btn = QPushButton("📊")
        self.details_btn.setFixedSize(32, 32)
        self.details_btn.setToolTip("View Details")
        self.details_btn.clicked.connect(lambda: self.agent_action.emit(self.agent_data.get('name', ''), 'details'))
        
        buttons_layout.addWidget(self.pause_btn)
        buttons_layout.addWidget(self.restart_btn)
        buttons_layout.addWidget(self.details_btn)
        buttons_layout.addStretch()
        
        # Add all sections to main layout
        layout.addLayout(header_layout)
        layout.addLayout(progress_layout)
        layout.addLayout(metrics_layout)
        layout.addLayout(buttons_layout)
        
    def update_agent_data(self, agent_data: Dict[str, Any]):
        """Update the agent card with new data"""
        self.agent_data = agent_data
        
        # Update name and activity
        self.name_label.setText(agent_data.get('name', 'Unknown Agent'))
        self.activity_label.setText(agent_data.get('activity', 'Idle'))
        
        # Update status
        status = agent_data.get('status', 'unknown')
        status_icon = "🟢" if status == 'active' else "🔴" if status == 'error' else "🟡"
        self.status_label.setText(status_icon)
        
        # Update progress
        progress = agent_data.get('progress', 0)
        self.progress_value.setText(f"{progress}%")
        self.progress_bar.setValue(progress)


class AgentControlPanel(QFrame):
    """Control panel for agent management"""
    
    control_action = pyqtSignal(str, dict)  # action, parameters
    
    def __init__(self):
        super().__init__()
        self.setObjectName("card")
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the control panel UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Header
        header = QLabel("🎛️ Agent Control Center")
        header.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(header)
        
        # Global controls
        global_layout = QHBoxLayout()
        
        start_all_btn = QPushButton("▶️ Start All")
        start_all_btn.setObjectName("success")
        start_all_btn.clicked.connect(lambda: self.control_action.emit('start_all', {}))
        
        pause_all_btn = QPushButton("⏸️ Pause All")
        pause_all_btn.setObjectName("warning")
        pause_all_btn.clicked.connect(lambda: self.control_action.emit('pause_all', {}))
        
        optimize_btn = QPushButton("⚡ Optimize")
        optimize_btn.clicked.connect(lambda: self.control_action.emit('optimize', {}))
        
        global_layout.addWidget(start_all_btn)
        global_layout.addWidget(pause_all_btn)
        global_layout.addWidget(optimize_btn)
        global_layout.addStretch()
        
        # Configuration section
        config_layout = QVBoxLayout()
        
        config_header = QLabel("Configuration")
        config_header.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        config_header.setObjectName("secondary")
        
        # Agent mode selection
        mode_layout = QHBoxLayout()
        mode_label = QLabel("Agent Mode:")
        mode_label.setObjectName("muted")
        
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["Autonomous", "Collaborative", "Supervised", "Manual"])
        self.mode_combo.setCurrentText("Autonomous")
        
        mode_layout.addWidget(mode_label)
        mode_layout.addWidget(self.mode_combo)
        mode_layout.addStretch()
        
        # Performance level
        perf_layout = QHBoxLayout()
        perf_label = QLabel("Performance:")
        perf_label.setObjectName("muted")
        
        self.perf_combo = QComboBox()
        self.perf_combo.addItems(["Eco", "Balanced", "High Performance", "Maximum"])
        self.perf_combo.setCurrentText("Balanced")
        
        perf_layout.addWidget(perf_label)
        perf_layout.addWidget(self.perf_combo)
        perf_layout.addStretch()
        
        config_layout.addWidget(config_header)
        config_layout.addLayout(mode_layout)
        config_layout.addLayout(perf_layout)
        
        # Statistics
        stats_layout = QVBoxLayout()
        
        stats_header = QLabel("Statistics")
        stats_header.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        stats_header.setObjectName("secondary")
        
        self.stats_text = QTextEdit()
        self.stats_text.setMaximumHeight(100)
        self.stats_text.setReadOnly(True)
        self.stats_text.setPlainText(
            "Active Agents: 4/4\n"
            "Tasks Completed: 1,247\n"
            "Avg Response Time: 342ms\n"
            "Efficiency Score: 94%"
        )
        
        stats_layout.addWidget(stats_header)
        stats_layout.addWidget(self.stats_text)
        
        # Add all sections
        layout.addLayout(global_layout)
        layout.addLayout(config_layout)
        layout.addLayout(stats_layout)
        layout.addStretch()


class AgentsWidget(QWidget):
    """Main agents management widget"""
    
    def __init__(self, api_client):
        super().__init__()
        self.api_client = api_client
        self.agent_cards = {}
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the agents widget UI"""
        # Main layout
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(30)
        
        # Left side - Agent grid
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(20)
        
        # Header
        header_layout = QHBoxLayout()
        
        title = QLabel("🤖 Agent Network")
        title.setFont(QFont("Arial", 20, QFont.Weight.Bold))
        
        refresh_btn = QPushButton("🔄 Refresh")
        refresh_btn.clicked.connect(self.update_data)
        
        header_layout.addWidget(title)
        header_layout.addStretch()
        header_layout.addWidget(refresh_btn)
        
        # Agent grid scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        self.agents_container = QWidget()
        self.agents_layout = QGridLayout(self.agents_container)
        self.agents_layout.setSpacing(20)
        
        scroll_area.setWidget(self.agents_container)
        
        left_layout.addLayout(header_layout)
        left_layout.addWidget(scroll_area)
        
        # Right side - Control panel
        self.control_panel = AgentControlPanel()
        self.control_panel.control_action.connect(self.handle_control_action)
        self.control_panel.setFixedWidth(350)
        
        # Add to main layout
        main_layout.addWidget(left_widget, 2)
        main_layout.addWidget(self.control_panel, 1)
        
        # Initialize with default agents
        self.update_data()
        
    def update_data(self):
        """Update agents data from API"""
        try:
            agents_data = self.api_client.get_agents()
            
            if not agents_data:
                # Default agents if no data
                agents_data = [
                    {
                        "name": "Research Agent",
                        "status": "active",
                        "progress": 85,
                        "activity": "Processing AI breakthrough papers"
                    },
                    {
                        "name": "System Agent",
                        "status": "active",
                        "progress": 99,
                        "activity": "Monitoring performance metrics"
                    },
                    {
                        "name": "Upgrade Agent",
                        "status": "active",
                        "progress": 73,
                        "activity": "Evaluating model improvements"
                    },
                    {
                        "name": "Communication Agent",
                        "status": "active",
                        "progress": 95,
                        "activity": "Managing A2A protocols"
                    }
                ]
                
            self.update_agent_cards(agents_data)
            self.update_control_panel_stats(agents_data)
            
        except Exception as e:
            print(f"Error updating agents data: {e}")
            
    def update_agent_cards(self, agents_data: List[Dict[str, Any]]):
        """Update the agent cards display"""
        # Clear existing cards
        for i in reversed(range(self.agents_layout.count())):
            child = self.agents_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
                
        self.agent_cards.clear()
        
        # Create new cards
        row, col = 0, 0
        for agent_data in agents_data:
            agent_name = agent_data.get('name', 'Unknown')
            
            card = AgentCard(agent_data)
            card.agent_action.connect(self.handle_agent_action)
            
            self.agents_layout.addWidget(card, row, col)
            self.agent_cards[agent_name] = card
            
            col += 1
            if col >= 2:  # 2 cards per row
                col = 0
                row += 1
                
    def update_control_panel_stats(self, agents_data: List[Dict[str, Any]]):
        """Update the control panel statistics"""
        active_count = len([a for a in agents_data if a.get('status') == 'active'])
        total_count = len(agents_data)
        
        avg_progress = sum(a.get('progress', 0) for a in agents_data) / len(agents_data) if agents_data else 0
        
        stats_text = f"""Active Agents: {active_count}/{total_count}
Tasks Completed: 1,247
Avg Response Time: 342ms
Efficiency Score: {avg_progress:.0f}%"""
        
        self.control_panel.stats_text.setPlainText(stats_text)
        
    def handle_agent_action(self, agent_name: str, action: str):
        """Handle individual agent actions"""
        try:
            if action == 'pause':
                result = self.api_client.control_agent(agent_name, 'pause')
            elif action == 'restart':
                result = self.api_client.restart_agent(agent_name)
            elif action == 'details':
                # Show agent details (could open a dialog)
                print(f"Showing details for {agent_name}")
                return
                
            if result:
                print(f"Agent action '{action}' executed for {agent_name}")
                # Refresh data after action
                QTimer.singleShot(1000, self.update_data)
                
        except Exception as e:
            print(f"Error executing agent action: {e}")
            
    def handle_control_action(self, action: str, parameters: Dict[str, Any]):
        """Handle control panel actions"""
        try:
            if action == 'start_all':
                # Start all agents
                for agent_name in self.agent_cards.keys():
                    self.api_client.start_agent(agent_name)
            elif action == 'pause_all':
                # Pause all agents
                for agent_name in self.agent_cards.keys():
                    self.api_client.control_agent(agent_name, 'pause')
            elif action == 'optimize':
                # Optimize all agents
                result = self.api_client.optimize_agents()
                
            print(f"Control action '{action}' executed")
            # Refresh data after action
            QTimer.singleShot(1000, self.update_data)
            
        except Exception as e:
            print(f"Error executing control action: {e}")
