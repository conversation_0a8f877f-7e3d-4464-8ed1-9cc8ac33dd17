{"api": {"base_url": "http://localhost:8001", "timeout": 10, "retry_attempts": 3, "retry_delay": 1}, "ui": {"theme": "dark", "update_interval": 5, "show_notifications": true, "minimize_to_tray": true, "start_minimized": false, "window_geometry": {"width": 1600, "height": 1000, "x": 100, "y": 100}}, "agents": {"auto_start": true, "default_mode": "autonomous", "performance_level": "balanced", "enable_logging": true, "log_level": "INFO"}, "monitoring": {"enable_live_updates": true, "update_frequency": 5, "show_performance_metrics": true, "alert_thresholds": {"cpu_usage": 80, "memory_usage": 85, "disk_usage": 90, "response_time": 1000}}, "security": {"enable_anomaly_detection": true, "anomaly_threshold": 0.8, "enable_threat_monitoring": true, "auto_quarantine": false}, "research": {"default_depth": "standard", "default_priority": "medium", "auto_save_results": true, "max_concurrent_tasks": 3}, "network": {"enable_monitoring": true, "scan_interval": 30, "port_scan_enabled": false, "traffic_analysis": true}, "export": {"default_format": "json", "include_metadata": true, "compress_exports": true, "auto_backup": true, "backup_interval": 24}, "notifications": {"enable_desktop_notifications": true, "enable_sound": false, "notification_types": {"agent_status": true, "security_alerts": true, "system_health": true, "research_complete": true}}}