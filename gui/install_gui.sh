#!/bin/bash
"""
Installation script for Offline Sentinel PyQt6 GUI
"""

echo "🚀 Installing Offline Sentinel PyQt6 GUI..."

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed. Please install Python 3.8 or later."
    exit 1
fi

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is required but not installed. Please install pip3."
    exit 1
fi

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install requirements
echo "📥 Installing Python dependencies..."
pip install -r requirements.txt

# Create assets directory if it doesn't exist
if [ ! -d "assets" ]; then
    echo "📁 Creating assets directory..."
    mkdir -p assets
fi

# Create config directory if it doesn't exist
if [ ! -d "config" ]; then
    echo "⚙️ Creating config directory..."
    mkdir -p config
fi

# Make run script executable
chmod +x run_gui.py

echo "✅ Installation completed successfully!"
echo ""
echo "To run the Offline Sentinel GUI:"
echo "1. Make sure the backend is running: cd ../backend && python -m backend.run"
echo "2. Run the GUI: cd gui && python run_gui.py"
echo ""
echo "Or use the convenience script:"
echo "   ./run_gui.py"
