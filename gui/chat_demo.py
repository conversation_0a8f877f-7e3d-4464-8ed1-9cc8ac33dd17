#!/usr/bin/env python3
"""
Chat Demo for Offline Sentinel PyQt6 GUI
Demonstrates the adaptive chat interface with live TTS
"""

import sys
import time
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import QTimer

def run_chat_demo():
    """Run a comprehensive demo of the chat functionality"""
    
    print("🎤 Starting Offline Sentinel Chat Interface Demo")
    print("=" * 55)
    
    # Import the main application
    from main import SentinelMain<PERSON>indow
    
    app = QApplication(sys.argv)
    
    # Create main window
    window = SentinelMainWindow()
    window.show()
    
    print("✅ Main window created and displayed")
    print("💬 Chat Features available:")
    print("   • Adaptive chat interface with AI agents")
    print("   • Live Text-to-Speech (TTS) functionality")
    print("   • Agent selection and switching")
    print("   • Voice control and customization")
    print("   • Quick action buttons")
    print("   • Real-time message history")
    
    # Auto-navigate to chat page
    QTimer.singleShot(2000, lambda: window.switch_page("chat"))
    
    # Demo timer to show chat features
    demo_timer = QTimer()
    demo_step = 0
    
    def demo_step_handler():
        nonlocal demo_step
        
        if demo_step == 0:
            print("\n💬 Navigating to Chat Interface...")
            window.switch_page("chat")
        elif demo_step == 1:
            print("🤖 Chat interface loaded with AI agent selection")
            print("🔊 TTS controls available on the right panel")
        elif demo_step == 2:
            print("📝 Try typing a message and pressing Enter")
            print("🎯 Use quick action buttons for common queries")
        elif demo_step == 3:
            print("🔄 Switch between different AI agents")
            print("⚙️ Adjust TTS settings (voice, speed, volume)")
        elif demo_step == 4:
            print("🎵 Test the voice synthesis with the 'Test Voice' button")
            print("🔇 Toggle Auto TTS on/off as needed")
        elif demo_step == 5:
            print("📊 Try quick actions: System Status, Health Check, Agent Status")
            print("🗑️ Clear chat history when needed")
        elif demo_step == 6:
            print("\n✨ Chat Demo completed!")
            print("💡 The chat interface is now ready for interactive use.")
            print("🎤 Features:")
            print("   • Real-time AI agent communication")
            print("   • Live TTS with voice customization")
            print("   • Adaptive interface with quick actions")
            print("   • Multi-agent support with easy switching")
            demo_timer.stop()
            
        demo_step += 1
    
    demo_timer.timeout.connect(demo_step_handler)
    demo_timer.start(4000)  # Switch every 4 seconds
    
    # Show welcome message
    QTimer.singleShot(1000, lambda: QMessageBox.information(
        window, "Chat Interface Demo",
        "🎤 Welcome to the Offline Sentinel Chat Interface!\n\n"
        "🌟 New Features:\n"
        "• 💬 Adaptive chat with AI agents\n"
        "• 🔊 Live Text-to-Speech (TTS)\n"
        "• 🤖 Multi-agent communication\n"
        "• 🎛️ Voice control and customization\n"
        "• ⚡ Quick action buttons\n"
        "• 📝 Real-time message history\n\n"
        "🎯 Try different agents and TTS settings!\n"
        "The demo will guide you through the features."
    ))
    
    # Start the application
    return app.exec()

if __name__ == "__main__":
    sys.exit(run_chat_demo())
