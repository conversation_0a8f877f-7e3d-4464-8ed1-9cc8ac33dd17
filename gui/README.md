# Offline Sentinel - PyQt6 Desktop Application

A professional desktop application for the Offline Sentinel AI-powered log analysis and monitoring system.

## Features

### 🏠 Dashboard
- **Live System Statistics**: Real-time CPU, memory, disk, and network monitoring
- **Agent Status Overview**: Visual status of all AI agents with progress tracking
- **System Health Metrics**: Comprehensive health scoring and trend analysis
- **Recent Activity Log**: Live feed of system events and agent activities
- **Quick Actions**: One-click security scans, optimizations, and exports

### 🤖 Agent Management
- **Agent Network View**: Visual grid of all active agents with individual controls
- **Real-time Progress Tracking**: Live progress bars and status indicators
- **Agent Control Panel**: Start, pause, restart, and optimize agents
- **Performance Metrics**: CPU and memory usage per agent
- **Configuration Management**: Agent modes and performance levels

### 🔬 Research Center
- **Task Creation**: Start new research tasks with customizable parameters
- **Active Task Monitoring**: Real-time progress of running research tasks
- **Results Viewer**: Comprehensive results with summary, details, and recommendations
- **Export Capabilities**: Export research results in multiple formats

### ⚙️ System Monitor
- **Health Overview**: Real-time system health metrics with visual indicators
- **Process Management**: View and manage system processes
- **System Logs**: Live log viewer with filtering and export capabilities
- **Performance Controls**: System optimization and cleanup tools

### 📡 Network Operations
- **Connection Monitoring**: Real-time view of active network connections
- **Security Events**: Live security event monitoring and alerting
- **Network Tools**: Built-in ping, traceroute, port scan, and bandwidth testing
- **Status Dashboard**: Network throughput, latency, and packet loss monitoring

### 🔧 Settings & Configuration
- **Application Settings**: Theme, update intervals, and UI preferences
- **Agent Configuration**: Default modes, performance levels, and logging
- **Monitoring Settings**: Alert thresholds and update frequencies
- **Import/Export**: Configuration backup and restore

## Installation

### Prerequisites
- Python 3.8 or later
- pip3
- PyQt6 (will be installed automatically)

### Quick Install
```bash
cd gui
chmod +x install_gui.sh
./install_gui.sh
```

### Manual Install
```bash
cd gui
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

## Usage

### Starting the Application

1. **Start the Backend** (in a separate terminal):
   ```bash
   cd backend
   source venv/bin/activate
   python -m backend.run
   ```

2. **Start the GUI**:
   ```bash
   cd gui
   python run_gui.py
   ```

### System Tray
The application can minimize to the system tray for background operation. Right-click the tray icon to access quick controls.

### Live Updates
All data updates automatically every 5 seconds by default. This can be configured in Settings.

## Architecture

### Main Components
- **Main Window**: Central application window with navigation sidebar
- **Widget System**: Modular page widgets for different functionality areas
- **API Client**: Handles all communication with the Flask backend
- **Theme Manager**: Provides dark theme styling and color management
- **Config Manager**: Manages application settings and preferences

### Key Files
- `main.py`: Main application entry point and window management
- `widgets/`: Individual page widgets for each functionality area
- `utils/api_client.py`: Backend API communication
- `utils/theme_manager.py`: UI theming and styling
- `utils/config_manager.py`: Configuration management

## Configuration

Settings are stored in `config/sentinel_config.json` and include:

- **UI Settings**: Theme, update intervals, notifications
- **API Settings**: Backend URL, timeouts, retry attempts
- **Agent Settings**: Default modes, performance levels, logging
- **Monitoring Settings**: Alert thresholds, update frequencies
- **Security Settings**: Anomaly detection, threat monitoring

## Troubleshooting

### Common Issues

1. **"Backend not responding"**
   - Ensure the Flask backend is running on port 8001
   - Check the API URL in Settings

2. **"PyQt6 not found"**
   - Install PyQt6: `pip install PyQt6`
   - Ensure you're using the correct Python environment

3. **"Permission denied"**
   - Make scripts executable: `chmod +x run_gui.py install_gui.sh`

4. **"Config file errors"**
   - Delete `config/sentinel_config.json` to reset to defaults
   - Use Settings → Reset to Defaults

### Debug Mode
Run with debug output:
```bash
python run_gui.py --debug
```

## Development

### Adding New Features
1. Create new widget in `widgets/` directory
2. Add to main window navigation in `main.py`
3. Implement API endpoints in `utils/api_client.py`
4. Update theme styling in `utils/theme_manager.py`

### Testing
```bash
# Run with test backend
python run_gui.py --test-mode

# Run with mock data
python run_gui.py --mock-data
```

## Requirements

- **Python**: 3.8+
- **PyQt6**: 6.6.0+
- **requests**: 2.31.0+
- **Backend**: Offline Sentinel Flask API

## License

Part of the Offline Sentinel project. See main project LICENSE for details.

## Support

For issues and feature requests, please use the main project repository.
