#!/usr/bin/env python3
"""
Demo script for Offline Sentinel PyQt6 GUI
Shows all features and capabilities
"""

import sys
import time
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import QTimer

def run_demo():
    """Run a comprehensive demo of the GUI"""
    
    print("🚀 Starting Offline Sentinel PyQt6 GUI Demo")
    print("=" * 50)
    
    # Import the main application
    from main import SentinelMainWindow
    
    app = QApplication(sys.argv)
    
    # Create main window
    window = SentinelMainWindow()
    window.show()
    
    print("✅ Main window created and displayed")
    print("📊 Features available:")
    print("   • Dashboard with live statistics")
    print("   • Agent management and control")
    print("   • Research task creation and monitoring")
    print("   • System health and performance monitoring")
    print("   • Network operations and security")
    print("   • Settings and configuration")
    
    # Demo timer to show different pages
    demo_timer = QTimer()
    demo_step = 0
    
    def demo_step_handler():
        nonlocal demo_step
        
        if demo_step == 0:
            print("\n🏠 Showing Dashboard...")
            window.switch_page("dashboard")
        elif demo_step == 1:
            print("🤖 Switching to Agents page...")
            window.switch_page("agents")
        elif demo_step == 2:
            print("🔬 Switching to Research page...")
            window.switch_page("research")
        elif demo_step == 3:
            print("⚙️ Switching to System Monitor...")
            window.switch_page("system")
        elif demo_step == 4:
            print("📡 Switching to Network Operations...")
            window.switch_page("network")
        elif demo_step == 5:
            print("🔧 Switching to Settings...")
            window.switch_page("settings")
        elif demo_step == 6:
            print("🔄 Returning to Dashboard...")
            window.switch_page("dashboard")
            demo_timer.stop()
            print("\n✨ Demo completed! GUI is now ready for use.")
            print("💡 Try clicking on different navigation items to explore features.")
            
        demo_step += 1
    
    demo_timer.timeout.connect(demo_step_handler)
    demo_timer.start(3000)  # Switch pages every 3 seconds
    
    # Show welcome message
    QTimer.singleShot(1000, lambda: QMessageBox.information(
        window, "Welcome to Offline Sentinel",
        "🎉 Welcome to the Offline Sentinel PyQt6 Desktop Application!\n\n"
        "Features:\n"
        "• Live system monitoring and statistics\n"
        "• AI agent management and control\n"
        "• Research task automation\n"
        "• Network security monitoring\n"
        "• Comprehensive system health tracking\n\n"
        "The demo will automatically show different pages.\n"
        "Feel free to explore the interface!"
    ))
    
    # Start the application
    return app.exec()

if __name__ == "__main__":
    sys.exit(run_demo())
