"""
Enhanced Theme Manager for Offline Sentinel PyQt6 Application
Provides glassmorphism styling, animations, and advanced theming
"""

from PyQt6.QtCore import QObject, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect, QTimer
from PyQt6.QtGui import QPalette, QColor, QFont, QLinearGradient, QBrush, QPainter, QPixmap
from PyQt6.QtWidgets import QApplication, QGraphicsEffect, QGraphicsBlurEffect, QWidget
import json
import os
import math


class GlassmorphismEffect(QGraphicsEffect):
    """Custom glassmorphism effect for widgets"""
    
    def __init__(self, blur_radius=15, opacity=0.1):
        super().__init__()
        self.blur_radius = blur_radius
        self.opacity = opacity
        
    def draw(self, painter):
        # Apply blur effect
        blur_effect = QGraphicsBlurEffect()
        blur_effect.setBlurRadius(self.blur_radius)
        
        # Draw with glassmorphism effect
        painter.setOpacity(self.opacity)
        self.drawSource(painter)


class AnimationManager(QObject):
    """Manages widget animations"""
    
    def __init__(self):
        super().__init__()
        self.animations = {}
        
    def fade_in(self, widget, duration=300):
        """Fade in animation"""
        animation = QPropertyAnimation(widget, b"windowOpacity")
        animation.setDuration(duration)
        animation.setStartValue(0.0)
        animation.setEndValue(1.0)
        animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        self.animations[id(widget)] = animation
        animation.start()
        return animation
        
    def slide_in(self, widget, direction="up", duration=400):
        """Slide in animation"""
        geometry = widget.geometry()
        
        if direction == "up":
            start_rect = QRect(geometry.x(), geometry.y() + 50, geometry.width(), geometry.height())
        elif direction == "down":
            start_rect = QRect(geometry.x(), geometry.y() - 50, geometry.width(), geometry.height())
        elif direction == "left":
            start_rect = QRect(geometry.x() + 50, geometry.y(), geometry.width(), geometry.height())
        else:  # right
            start_rect = QRect(geometry.x() - 50, geometry.y(), geometry.width(), geometry.height())
            
        animation = QPropertyAnimation(widget, b"geometry")
        animation.setDuration(duration)
        animation.setStartValue(start_rect)
        animation.setEndValue(geometry)
        animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        self.animations[id(widget)] = animation
        animation.start()
        return animation
        
    def pulse(self, widget, duration=1000, scale_factor=1.05):
        """Pulse animation"""
        animation = QPropertyAnimation(widget, b"geometry")
        animation.setDuration(duration)
        animation.setLoopCount(-1)  # Infinite loop
        
        original_geometry = widget.geometry()
        center_x = original_geometry.center().x()
        center_y = original_geometry.center().y()
        
        scaled_width = int(original_geometry.width() * scale_factor)
        scaled_height = int(original_geometry.height() * scale_factor)
        scaled_x = center_x - scaled_width // 2
        scaled_y = center_y - scaled_height // 2
        
        scaled_geometry = QRect(scaled_x, scaled_y, scaled_width, scaled_height)
        
        animation.setKeyValueAt(0.0, original_geometry)
        animation.setKeyValueAt(0.5, scaled_geometry)
        animation.setKeyValueAt(1.0, original_geometry)
        animation.setEasingCurve(QEasingCurve.Type.InOutSine)
        
        self.animations[id(widget)] = animation
        animation.start()
        return animation
        
    def glow_effect(self, widget, color="#007aff", duration=2000):
        """Glow effect animation"""
        # This would be implemented with custom painting
        # For now, we'll use a timer-based approach
        timer = QTimer()
        timer.timeout.connect(lambda: self._update_glow(widget, color))
        timer.start(50)  # Update every 50ms
        
        # Store timer reference
        self.animations[f"glow_{id(widget)}"] = timer
        
        # Auto-stop after duration
        QTimer.singleShot(duration, timer.stop)
        
    def _update_glow(self, widget, color):
        """Update glow effect"""
        # Implementation would involve custom painting
        pass
        
    def stop_animation(self, widget):
        """Stop animation for widget"""
        widget_id = id(widget)
        if widget_id in self.animations:
            animation = self.animations[widget_id]
            if hasattr(animation, 'stop'):
                animation.stop()
            del self.animations[widget_id]


class EnhancedThemeManager(QObject):
    """Enhanced theme manager with glassmorphism and animations"""
    
    theme_changed = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.current_theme = "glassmorphism_dark"
        self.animation_manager = AnimationManager()
        self.themes = self.load_themes()
        
    def load_themes(self):
        """Load enhanced theme definitions"""
        return {
            "glassmorphism_dark": {
                "name": "Glassmorphism Dark",
                "type": "glassmorphism",
                "colors": {
                    "primary": "#000000",
                    "secondary": "#0a0a0a",
                    "glass_bg": "rgba(255, 255, 255, 0.05)",
                    "glass_bg_hover": "rgba(255, 255, 255, 0.08)",
                    "glass_border": "rgba(255, 255, 255, 0.1)",
                    "glass_border_hover": "rgba(255, 255, 255, 0.2)",
                    "accent_blue": "#007aff",
                    "accent_purple": "#5856d6",
                    "accent_green": "#30d158",
                    "accent_orange": "#ff9500",
                    "accent_red": "#ff3b30",
                    "accent_cyan": "#64d2ff",
                    "text_primary": "#ffffff",
                    "text_secondary": "#8e8e93",
                    "text_tertiary": "#636366"
                },
                "gradients": {
                    "primary": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #007aff, stop:1 #5856d6)",
                    "success": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #30d158, stop:1 #64d2ff)",
                    "warning": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ff9500, stop:1 #ff3b30)",
                    "glass": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 rgba(255,255,255,0.1), stop:1 rgba(255,255,255,0.05))"
                },
                "fonts": {
                    "default": "Segoe UI",
                    "monospace": "Consolas",
                    "heading": "Segoe UI"
                },
                "effects": {
                    "blur_radius": 16,
                    "shadow_color": "rgba(0, 0, 0, 0.3)",
                    "glow_color": "rgba(0, 122, 255, 0.3)"
                }
            },
            "glassmorphism_light": {
                "name": "Glassmorphism Light",
                "type": "glassmorphism",
                "colors": {
                    "primary": "#ffffff",
                    "secondary": "#f8f9fa",
                    "glass_bg": "rgba(0, 0, 0, 0.05)",
                    "glass_bg_hover": "rgba(0, 0, 0, 0.08)",
                    "glass_border": "rgba(0, 0, 0, 0.1)",
                    "glass_border_hover": "rgba(0, 0, 0, 0.2)",
                    "accent_blue": "#0078d4",
                    "accent_purple": "#8b5cf6",
                    "accent_green": "#10b981",
                    "accent_orange": "#f59e0b",
                    "accent_red": "#ef4444",
                    "accent_cyan": "#06b6d4",
                    "text_primary": "#1f2937",
                    "text_secondary": "#6b7280",
                    "text_tertiary": "#9ca3af"
                },
                "gradients": {
                    "primary": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #0078d4, stop:1 #8b5cf6)",
                    "success": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #10b981, stop:1 #06b6d4)",
                    "warning": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #f59e0b, stop:1 #ef4444)",
                    "glass": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 rgba(0,0,0,0.1), stop:1 rgba(0,0,0,0.05))"
                },
                "fonts": {
                    "default": "Segoe UI",
                    "monospace": "Consolas",
                    "heading": "Segoe UI"
                },
                "effects": {
                    "blur_radius": 16,
                    "shadow_color": "rgba(0, 0, 0, 0.1)",
                    "glow_color": "rgba(0, 120, 212, 0.3)"
                }
            },
            "cyberpunk": {
                "name": "Cyberpunk",
                "type": "neon",
                "colors": {
                    "primary": "#0a0a0a",
                    "secondary": "#1a1a2e",
                    "accent_blue": "#00d4ff",
                    "accent_purple": "#8b5cf6",
                    "accent_green": "#10b981",
                    "accent_pink": "#ff2d92",
                    "accent_yellow": "#ffff00",
                    "text_primary": "#ffffff",
                    "text_secondary": "#a1a1aa",
                    "text_accent": "#00d4ff"
                },
                "gradients": {
                    "primary": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #00d4ff, stop:1 #ff2d92)",
                    "neon": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ffff00, stop:1 #00d4ff)"
                },
                "fonts": {
                    "default": "Segoe UI",
                    "monospace": "Consolas",
                    "heading": "Segoe UI"
                },
                "effects": {
                    "blur_radius": 20,
                    "shadow_color": "rgba(0, 212, 255, 0.5)",
                    "glow_color": "rgba(0, 212, 255, 0.8)"
                }
            }
        }
        
    def get_current_theme(self):
        """Get current theme configuration"""
        return self.themes.get(self.current_theme, self.themes["glassmorphism_dark"])
        
    def set_theme(self, theme_name: str):
        """Set application theme"""
        if theme_name in self.themes:
            self.current_theme = theme_name
            self.apply_theme()
            self.theme_changed.emit(theme_name)
            
    def apply_theme(self):
        """Apply current theme to application"""
        theme = self.get_current_theme()
        colors = theme["colors"]
        
        # Create application palette
        palette = QPalette()
        
        # Set colors
        palette.setColor(QPalette.ColorRole.Window, QColor(colors["primary"]))
        palette.setColor(QPalette.ColorRole.WindowText, QColor(colors["text_primary"]))
        palette.setColor(QPalette.ColorRole.Base, QColor(colors["secondary"]))
        palette.setColor(QPalette.ColorRole.AlternateBase, QColor(colors.get("glass_border", "#3a3a3c")))
        palette.setColor(QPalette.ColorRole.ToolTipBase, QColor(colors["secondary"]))
        palette.setColor(QPalette.ColorRole.ToolTipText, QColor(colors["text_primary"]))
        palette.setColor(QPalette.ColorRole.Text, QColor(colors["text_primary"]))
        palette.setColor(QPalette.ColorRole.Button, QColor(colors["secondary"]))
        palette.setColor(QPalette.ColorRole.ButtonText, QColor(colors["text_primary"]))
        palette.setColor(QPalette.ColorRole.BrightText, QColor(colors["accent_blue"]))
        palette.setColor(QPalette.ColorRole.Link, QColor(colors["accent_blue"]))
        palette.setColor(QPalette.ColorRole.Highlight, QColor(colors["accent_blue"]))
        palette.setColor(QPalette.ColorRole.HighlightedText, QColor(colors["text_primary"]))
        
        # Apply palette
        QApplication.instance().setPalette(palette)

    def get_glassmorphism_stylesheet(self):
        """Get glassmorphism stylesheet"""
        theme = self.get_current_theme()
        colors = theme["colors"]
        gradients = theme["gradients"]

        return f"""
        /* Glassmorphism Base Styles */
        QWidget {{
            background: transparent;
            color: {colors["text_primary"]};
            font-family: '{theme["fonts"]["default"]}';
        }}

        /* Glass Cards */
        .glass-card {{
            background: {colors["glass_bg"]};
            border: 1px solid {colors["glass_border"]};
            border-radius: 16px;
            backdrop-filter: blur(16px);
        }}

        .glass-card:hover {{
            background: {colors["glass_bg_hover"]};
            border-color: {colors["glass_border_hover"]};
        }}

        /* Glass Buttons */
        QPushButton.glass-btn {{
            background: {colors["glass_bg"]};
            border: 1px solid {colors["glass_border"]};
            border-radius: 12px;
            padding: 12px 24px;
            color: {colors["text_primary"]};
            font-weight: 600;
        }}

        QPushButton.glass-btn:hover {{
            background: {colors["glass_bg_hover"]};
            border-color: {colors["glass_border_hover"]};
        }}

        QPushButton.glass-btn:pressed {{
            background: {colors["accent_blue"]};
            border-color: {colors["accent_blue"]};
        }}

        /* Gradient Buttons */
        QPushButton.gradient-btn {{
            background: {gradients["primary"]};
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            color: white;
            font-weight: 600;
        }}

        QPushButton.gradient-btn:hover {{
            background: {gradients["success"]};
        }}

        /* Glass Panels */
        QFrame.glass-panel {{
            background: {colors["glass_bg"]};
            border: 1px solid {colors["glass_border"]};
            border-radius: 20px;
            backdrop-filter: blur(20px);
        }}

        /* Glass Input Fields */
        QLineEdit.glass-input {{
            background: {colors["glass_bg"]};
            border: 1px solid {colors["glass_border"]};
            border-radius: 8px;
            padding: 8px 12px;
            color: {colors["text_primary"]};
        }}

        QLineEdit.glass-input:focus {{
            border-color: {colors["accent_blue"]};
            background: {colors["glass_bg_hover"]};
        }}

        QTextEdit.glass-text {{
            background: {colors["glass_bg"]};
            border: 1px solid {colors["glass_border"]};
            border-radius: 12px;
            padding: 12px;
            color: {colors["text_primary"]};
        }}

        /* Glass Lists */
        QListWidget.glass-list {{
            background: {colors["glass_bg"]};
            border: 1px solid {colors["glass_border"]};
            border-radius: 12px;
            padding: 8px;
        }}

        QListWidget.glass-list::item {{
            background: transparent;
            border-radius: 8px;
            padding: 8px;
            margin: 2px;
        }}

        QListWidget.glass-list::item:selected {{
            background: {colors["accent_blue"]};
        }}

        QListWidget.glass-list::item:hover {{
            background: {colors["glass_bg_hover"]};
        }}

        /* Glass Tables */
        QTableWidget.glass-table {{
            background: {colors["glass_bg"]};
            border: 1px solid {colors["glass_border"]};
            border-radius: 12px;
            gridline-color: {colors["glass_border"]};
        }}

        QTableWidget.glass-table::item {{
            background: transparent;
            border: none;
            padding: 8px;
        }}

        QTableWidget.glass-table::item:selected {{
            background: {colors["accent_blue"]};
        }}

        /* Glass Progress Bars */
        QProgressBar.glass-progress {{
            background: {colors["glass_bg"]};
            border: 1px solid {colors["glass_border"]};
            border-radius: 8px;
            text-align: center;
        }}

        QProgressBar.glass-progress::chunk {{
            background: {gradients["primary"]};
            border-radius: 6px;
        }}

        /* Glass Tabs */
        QTabWidget.glass-tabs {{
            background: transparent;
        }}

        QTabWidget.glass-tabs::pane {{
            background: {colors["glass_bg"]};
            border: 1px solid {colors["glass_border"]};
            border-radius: 12px;
        }}

        QTabBar::tab {{
            background: {colors["glass_bg"]};
            border: 1px solid {colors["glass_border"]};
            border-radius: 8px;
            padding: 8px 16px;
            margin: 2px;
            color: {colors["text_secondary"]};
        }}

        QTabBar::tab:selected {{
            background: {colors["accent_blue"]};
            color: white;
        }}

        QTabBar::tab:hover {{
            background: {colors["glass_bg_hover"]};
            color: {colors["text_primary"]};
        }}

        /* Glass Scrollbars */
        QScrollBar:vertical {{
            background: {colors["glass_bg"]};
            width: 12px;
            border-radius: 6px;
            border: 1px solid {colors["glass_border"]};
        }}

        QScrollBar::handle:vertical {{
            background: {colors["accent_blue"]};
            border-radius: 5px;
            min-height: 20px;
        }}

        QScrollBar::handle:vertical:hover {{
            background: {colors["accent_purple"]};
        }}

        QScrollBar:horizontal {{
            background: {colors["glass_bg"]};
            height: 12px;
            border-radius: 6px;
            border: 1px solid {colors["glass_border"]};
        }}

        QScrollBar::handle:horizontal {{
            background: {colors["accent_blue"]};
            border-radius: 5px;
            min-width: 20px;
        }}

        /* Glass Tooltips */
        QToolTip {{
            background: {colors["glass_bg"]};
            border: 1px solid {colors["glass_border"]};
            border-radius: 8px;
            padding: 8px;
            color: {colors["text_primary"]};
        }}
        """

    def get_cyberpunk_stylesheet(self):
        """Get cyberpunk/neon stylesheet"""
        theme = self.get_current_theme()
        colors = theme["colors"]
        gradients = theme["gradients"]

        return f"""
        /* Cyberpunk Neon Styles */
        QWidget {{
            background: {colors["primary"]};
            color: {colors["text_primary"]};
            font-family: '{theme["fonts"]["default"]}';
        }}

        /* Neon Buttons */
        QPushButton.neon-btn {{
            background: transparent;
            border: 2px solid {colors["accent_blue"]};
            border-radius: 8px;
            padding: 12px 24px;
            color: {colors["accent_blue"]};
            font-weight: bold;
            text-transform: uppercase;
        }}

        QPushButton.neon-btn:hover {{
            background: {colors["accent_blue"]};
            color: {colors["primary"]};
            box-shadow: 0 0 20px {colors["accent_blue"]};
        }}

        /* Neon Panels */
        QFrame.neon-panel {{
            background: {colors["secondary"]};
            border: 2px solid {colors["accent_blue"]};
            border-radius: 12px;
            box-shadow: inset 0 0 20px {colors["accent_blue"]};
        }}

        /* Neon Text */
        QLabel.neon-text {{
            color: {colors["accent_blue"]};
            font-weight: bold;
            text-shadow: 0 0 10px {colors["accent_blue"]};
        }}

        /* Animated Borders */
        .animated-border {{
            border: 2px solid transparent;
            background: linear-gradient({colors["secondary"]}, {colors["secondary"]}) padding-box,
                       {gradients["primary"]} border-box;
            border-radius: 12px;
        }}
        """

    def get_stylesheet(self, widget_type: str = "default"):
        """Get stylesheet for specific widget type"""
        theme = self.get_current_theme()

        if theme["type"] == "glassmorphism":
            return self.get_glassmorphism_stylesheet()
        elif theme["type"] == "neon":
            return self.get_cyberpunk_stylesheet()
        else:
            return self.get_glassmorphism_stylesheet()  # Default fallback

    def get_color(self, color_name: str):
        """Get color value by name"""
        theme = self.get_current_theme()
        return theme["colors"].get(color_name, "#ffffff")

    def get_gradient(self, gradient_name: str):
        """Get gradient value by name"""
        theme = self.get_current_theme()
        return theme["gradients"].get(gradient_name, "")

    def get_font(self, font_type: str = "default", size: int = 10, weight: int = 400):
        """Get font configuration"""
        theme = self.get_current_theme()
        font_family = theme["fonts"].get(font_type, "Segoe UI")

        font = QFont(font_family, size)
        font.setWeight(weight)
        return font

    def apply_glassmorphism_effect(self, widget, blur_radius=15, opacity=0.1):
        """Apply glassmorphism effect to widget"""
        effect = GlassmorphismEffect(blur_radius, opacity)
        widget.setGraphicsEffect(effect)

    def animate_widget_entrance(self, widget, animation_type="fade_in"):
        """Animate widget entrance"""
        if animation_type == "fade_in":
            return self.animation_manager.fade_in(widget)
        elif animation_type == "slide_in_up":
            return self.animation_manager.slide_in(widget, "up")
        elif animation_type == "slide_in_down":
            return self.animation_manager.slide_in(widget, "down")
        elif animation_type == "slide_in_left":
            return self.animation_manager.slide_in(widget, "left")
        elif animation_type == "slide_in_right":
            return self.animation_manager.slide_in(widget, "right")
        elif animation_type == "pulse":
            return self.animation_manager.pulse(widget)

    def add_glow_effect(self, widget, color=None):
        """Add glow effect to widget"""
        if color is None:
            color = self.get_color("accent_blue")
        return self.animation_manager.glow_effect(widget, color)
