"""
API Client for Offline Sentinel
Handles all communication with the backend Flask API
"""

import requests
import json
from typing import Dict, List, Optional, Any
from datetime import datetime


class APIClient:
    """Client for communicating with the Offline Sentinel backend API"""
    
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.timeout = 10
        
        # Set default headers
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'Offline-Sentinel-PyQt6/1.0'
        })
        
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """Make a request to the API"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, params=data)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data)
            elif method.upper() == 'PUT':
                response = self.session.put(url, json=data)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
                
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"API request failed: {e}")
            return {}
        except json.JSONDecodeError as e:
            print(f"Failed to decode JSON response: {e}")
            return {}
            
    def get_agents(self) -> List[Dict[str, Any]]:
        """Get list of all agents"""
        return self._make_request('GET', '/api/agents')
        
    def get_agent_diagnostics(self) -> Dict[str, Any]:
        """Get agent diagnostics"""
        return self._make_request('GET', '/api/agents/diagnostics')
        
    def get_live_stats(self) -> Dict[str, Any]:
        """Get live system statistics"""
        return self._make_request('GET', '/api/system/live-stats')
        
    def get_recent_logs(self) -> Dict[str, Any]:
        """Get recent log entries"""
        return self._make_request('GET', '/api/logs/recent')
        
    def get_recent_anomalies(self) -> Dict[str, Any]:
        """Get recent anomalies"""
        return self._make_request('GET', '/api/anomalies/recent')
        
    def start_agent(self, agent_name: str) -> Dict[str, Any]:
        """Start a specific agent"""
        return self._make_request('POST', '/api/agents/start', {'agent': agent_name})
        
    def stop_agent(self, agent_name: str) -> Dict[str, Any]:
        """Stop a specific agent"""
        return self._make_request('POST', '/api/agents/stop', {'agent': agent_name})
        
    def restart_agent(self, agent_name: str) -> Dict[str, Any]:
        """Restart a specific agent"""
        return self._make_request('POST', '/api/agents/restart', {'agent': agent_name})
        
    def control_agent(self, agent_name: str, action: str) -> Dict[str, Any]:
        """Control an agent (pause, resume, etc.)"""
        return self._make_request('POST', '/api/agents/control', {
            'agent': agent_name,
            'action': action
        })
        
    def deploy_agent(self, agent_type: str, agent_name: str) -> Dict[str, Any]:
        """Deploy a new agent"""
        return self._make_request('POST', '/api/agents/deploy', {
            'type': agent_type,
            'name': agent_name
        })
        
    def optimize_agents(self) -> Dict[str, Any]:
        """Optimize all agents"""
        return self._make_request('POST', '/api/agents/optimize')
        
    def interact_with_agent(self, prompt: str) -> Dict[str, Any]:
        """Interact with an agent using natural language"""
        return self._make_request('POST', '/api/agent/interact', {'prompt': prompt})
        
    def start_research_task(self, task_type: str, target: str, depth: str = "standard", priority: str = "medium") -> Dict[str, Any]:
        """Start a research task"""
        return self._make_request('POST', '/api/agents/research/start', {
            'task_type': task_type,
            'target': target,
            'depth': depth,
            'priority': priority
        })
        
    def get_research_results(self) -> Dict[str, Any]:
        """Get research results"""
        return self._make_request('GET', '/api/agents/research/results')
        
    def upload_file(self, file_path: str, file_content: bytes) -> Dict[str, Any]:
        """Upload a file for analysis"""
        # Note: This would need to be implemented differently for file uploads
        # For now, return a placeholder
        return {'status': 'success', 'message': 'File upload not implemented in API client yet'}
        
    def get_file_analysis(self, file_id: str) -> Dict[str, Any]:
        """Get analysis results for a file"""
        return self._make_request('GET', f'/api/files/{file_id}/analysis')
        
    def get_system_health(self) -> Dict[str, Any]:
        """Get system health metrics"""
        return self._make_request('GET', '/api/system/health')
        
    def get_network_status(self) -> Dict[str, Any]:
        """Get network status and metrics"""
        return self._make_request('GET', '/api/network/status')
        
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        return self._make_request('GET', '/api/system/performance')
        
    def get_security_status(self) -> Dict[str, Any]:
        """Get security status and alerts"""
        return self._make_request('GET', '/api/security/status')
        
    def get_diagnostics(self) -> Dict[str, Any]:
        """Get system diagnostics"""
        return self._make_request('GET', '/api/agents/diagnostics')
        
    def check_connection(self) -> bool:
        """Check if the API is reachable"""
        try:
            response = self.session.get(f"{self.base_url}/api/agents", timeout=5)
            return response.status_code == 200
        except:
            return False
            
    def get_ai_models(self) -> List[Dict[str, Any]]:
        """Get list of AI models"""
        # This would be implemented based on your backend API
        # For now, return mock data
        return [
            {
                'name': 'Granite AI Agent',
                'model': 'ibm/granite4.0-preview:tiny',
                'size': '4.0 GB',
                'status': 'running',
                'specialization': 'General Intelligence',
                'performance': 95
            },
            {
                'name': 'SmolLM Agent',
                'model': 'smol-1.7b-instruct',
                'size': '1.7 GB',
                'status': 'running',
                'specialization': 'System Monitoring',
                'performance': 88
            },
            {
                'name': 'Llama Research Agent',
                'model': 'llama-3.2-1b-instruct',
                'size': '1.2 GB',
                'status': 'running',
                'specialization': 'Research & Analysis',
                'performance': 92
            },
            {
                'name': 'Phi3 Security Agent',
                'model': 'phi3-128k',
                'size': '2.4 GB',
                'status': 'running',
                'specialization': 'Security Monitoring',
                'performance': 90
            }
        ]
        
    def get_log_entries(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get log entries"""
        return self._make_request('GET', '/api/logs', {'limit': limit})
        
    def get_anomaly_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get anomaly detection history"""
        return self._make_request('GET', '/api/anomalies', {'limit': limit})
        
    def export_data(self, data_type: str, format: str = 'json') -> Dict[str, Any]:
        """Export system data"""
        return self._make_request('POST', '/api/export', {
            'type': data_type,
            'format': format
        })
        
    def get_configuration(self) -> Dict[str, Any]:
        """Get system configuration"""
        return self._make_request('GET', '/api/config')
        
    def update_configuration(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Update system configuration"""
        return self._make_request('PUT', '/api/config', config)
        
    def get_version_info(self) -> Dict[str, Any]:
        """Get version and build information"""
        return self._make_request('GET', '/api/version')
        
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive system statistics"""
        try:
            # Combine multiple API calls for comprehensive stats
            agents = self.get_agents()
            diagnostics = self.get_agent_diagnostics()
            live_stats = self.get_live_stats()
            recent_logs = self.get_recent_logs()
            recent_anomalies = self.get_recent_anomalies()
            
            return {
                'agents': agents,
                'diagnostics': diagnostics,
                'live_stats': live_stats,
                'recent_logs': recent_logs,
                'recent_anomalies': recent_anomalies,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            print(f"Error getting comprehensive statistics: {e}")
            return {}
