"""
Theme Manager for Offline Sentinel PyQt6 Application
Provides dark theme styling and color management
"""

from PyQt6.QtCore import QObject
from PyQt6.QtGui import QColor, QPalette


class ThemeManager(QObject):
    """Manages application themes and styling"""
    
    def __init__(self):
        super().__init__()
        self.current_theme = "dark"
        
        # Define color palette
        self.colors = {
            'dark': {
                'primary': '#0a0a0a',
                'secondary': '#1a1a1a',
                'tertiary': '#2a2a2a',
                'accent_blue': '#3b82f6',
                'accent_green': '#10b981',
                'accent_purple': '#8b5cf6',
                'accent_orange': '#f59e0b',
                'text_primary': '#ffffff',
                'text_secondary': '#a1a1aa',
                'text_muted': '#71717a',
                'border': '#374151',
                'hover': '#374151',
                'success': '#10b981',
                'warning': '#f59e0b',
                'error': '#ef4444',
                'info': '#3b82f6'
            }
        }
        
    def get_stylesheet(self) -> str:
        """Get the complete stylesheet for the application"""
        colors = self.colors[self.current_theme]
        
        return f"""
        /* Main Application Styling */
        QMainWindow {{
            background-color: {colors['primary']};
            color: {colors['text_primary']};
        }}
        
        /* Sidebar Styling */
        QFrame#sidebar {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {colors['secondary']}, stop:1 {colors['tertiary']});
            border-right: 1px solid {colors['border']};
        }}
        
        /* Logo Styling */
        QLabel#logo-icon {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {colors['accent_blue']}, stop:1 {colors['accent_purple']});
            border-radius: 20px;
            color: white;
            font-weight: bold;
            font-size: 18px;
        }}
        
        QLabel#logo-text {{
            color: {colors['text_primary']};
            font-weight: bold;
        }}
        
        /* Navigation Buttons */
        QPushButton#nav-button {{
            background: transparent;
            border: none;
            color: {colors['text_secondary']};
            padding: 12px 16px;
            text-align: left;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
        }}
        
        QPushButton#nav-button:hover {{
            background-color: {colors['hover']};
            color: {colors['text_primary']};
        }}
        
        QPushButton#nav-button:checked {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {colors['accent_blue']}, stop:1 {colors['accent_purple']});
            color: white;
        }}
        
        /* Live Indicator */
        QLabel#live-indicator {{
            background-color: {colors['accent_green']};
            color: white;
            padding: 8px 16px;
            border-radius: 15px;
            font-weight: bold;
            font-size: 12px;
        }}
        
        /* Header Styling */
        QWidget#header {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['secondary']}, stop:1 {colors['primary']});
            border-bottom: 1px solid {colors['border']};
        }}
        
        QLabel#page-title {{
            color: {colors['text_primary']};
            font-weight: bold;
        }}
        
        QLabel#page-subtitle {{
            color: {colors['text_secondary']};
        }}
        
        QLabel#status-text {{
            color: {colors['text_secondary']};
            font-size: 12px;
        }}
        
        /* Cards and Containers */
        QFrame.card {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['secondary']}, stop:1 {colors['tertiary']});
            border: 1px solid {colors['border']};
            border-radius: 12px;
            padding: 20px;
        }}
        
        QFrame.stat-card {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {colors['secondary']}, stop:1 {colors['tertiary']});
            border: 1px solid {colors['border']};
            border-radius: 12px;
            padding: 16px;
        }}
        
        QFrame.stat-card:hover {{
            border-color: {colors['accent_blue']};
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {colors['tertiary']}, stop:1 {colors['secondary']});
        }}
        
        /* Buttons */
        QPushButton {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {colors['accent_blue']}, stop:1 {colors['accent_purple']});
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 500;
        }}
        
        QPushButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {colors['accent_purple']}, stop:1 {colors['accent_blue']});
        }}
        
        QPushButton:pressed {{
            background: {colors['tertiary']};
        }}
        
        QPushButton:disabled {{
            background-color: {colors['tertiary']};
            color: {colors['text_muted']};
        }}
        
        /* Success Button */
        QPushButton.success {{
            background-color: {colors['success']};
        }}
        
        QPushButton.success:hover {{
            background-color: #059669;
        }}
        
        /* Warning Button */
        QPushButton.warning {{
            background-color: {colors['warning']};
        }}
        
        QPushButton.warning:hover {{
            background-color: #d97706;
        }}
        
        /* Error Button */
        QPushButton.error {{
            background-color: {colors['error']};
        }}
        
        QPushButton.error:hover {{
            background-color: #dc2626;
        }}
        
        /* Text Inputs */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            background-color: {colors['tertiary']};
            border: 1px solid {colors['border']};
            border-radius: 6px;
            padding: 8px 12px;
            color: {colors['text_primary']};
            font-size: 14px;
        }}
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {colors['accent_blue']};
            background-color: {colors['secondary']};
        }}
        
        /* Progress Bars */
        QProgressBar {{
            background-color: {colors['tertiary']};
            border: 1px solid {colors['border']};
            border-radius: 6px;
            text-align: center;
            color: {colors['text_primary']};
        }}
        
        QProgressBar::chunk {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {colors['accent_blue']}, stop:1 {colors['accent_green']});
            border-radius: 5px;
        }}
        
        /* Tabs */
        QTabWidget::pane {{
            border: 1px solid {colors['border']};
            background-color: {colors['secondary']};
        }}
        
        QTabBar::tab {{
            background-color: {colors['tertiary']};
            color: {colors['text_secondary']};
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
        }}
        
        QTabBar::tab:selected {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 {colors['accent_blue']}, stop:1 {colors['accent_purple']});
            color: white;
        }}
        
        QTabBar::tab:hover {{
            background-color: {colors['hover']};
            color: {colors['text_primary']};
        }}
        
        /* Scroll Areas */
        QScrollArea {{
            background-color: transparent;
            border: none;
        }}
        
        QScrollBar:vertical {{
            background-color: {colors['tertiary']};
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {colors['accent_blue']};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {colors['accent_purple']};
        }}
        
        /* Labels */
        QLabel {{
            color: {colors['text_primary']};
        }}
        
        QLabel.secondary {{
            color: {colors['text_secondary']};
        }}
        
        QLabel.muted {{
            color: {colors['text_muted']};
        }}
        
        QLabel.success {{
            color: {colors['success']};
        }}
        
        QLabel.warning {{
            color: {colors['warning']};
        }}
        
        QLabel.error {{
            color: {colors['error']};
        }}
        
        /* Status Bar */
        QStatusBar {{
            background-color: {colors['secondary']};
            border-top: 1px solid {colors['border']};
            color: {colors['text_secondary']};
        }}
        
        /* Splitter */
        QSplitter::handle {{
            background-color: {colors['border']};
        }}
        
        QSplitter::handle:horizontal {{
            width: 2px;
        }}
        
        QSplitter::handle:vertical {{
            height: 2px;
        }}
        
        /* List Widgets */
        QListWidget {{
            background-color: {colors['secondary']};
            border: 1px solid {colors['border']};
            border-radius: 6px;
            color: {colors['text_primary']};
        }}
        
        QListWidget::item {{
            padding: 8px;
            border-bottom: 1px solid {colors['border']};
        }}
        
        QListWidget::item:selected {{
            background-color: {colors['accent_blue']};
        }}
        
        QListWidget::item:hover {{
            background-color: {colors['hover']};
        }}
        
        /* Tree Widgets */
        QTreeWidget {{
            background-color: {colors['secondary']};
            border: 1px solid {colors['border']};
            border-radius: 6px;
            color: {colors['text_primary']};
        }}
        
        QTreeWidget::item {{
            padding: 4px;
        }}
        
        QTreeWidget::item:selected {{
            background-color: {colors['accent_blue']};
        }}
        
        QTreeWidget::item:hover {{
            background-color: {colors['hover']};
        }}
        
        /* Combo Boxes */
        QComboBox {{
            background-color: {colors['tertiary']};
            border: 1px solid {colors['border']};
            border-radius: 6px;
            padding: 6px 12px;
            color: {colors['text_primary']};
        }}
        
        QComboBox:hover {{
            border-color: {colors['accent_blue']};
        }}
        
        QComboBox::drop-down {{
            border: none;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {colors['text_secondary']};
        }}
        
        /* Tooltips */
        QToolTip {{
            background-color: {colors['tertiary']};
            color: {colors['text_primary']};
            border: 1px solid {colors['border']};
            border-radius: 4px;
            padding: 4px 8px;
        }}

        /* Chat Widget Styling - Responsive */
        QFrame#chat-message-user {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {colors['accent_blue']}, stop:1 {colors['accent_purple']});
            border-radius: 10px;
            margin-left: 30px;
            margin-right: 5px;
            padding: 5px;
        }}

        QFrame#chat-message-agent {{
            background-color: {colors['tertiary']};
            border: 1px solid {colors['border']};
            border-radius: 10px;
            margin-left: 5px;
            margin-right: 30px;
            padding: 5px;
        }}

        QLabel#chat-sender {{
            color: {colors['text_primary']};
            font-weight: bold;
        }}

        QLabel#chat-message-text {{
            color: {colors['text_primary']};
            padding: 5px;
        }}

        /* Sliders */
        QSlider::groove:horizontal {{
            background-color: {colors['tertiary']};
            height: 6px;
            border-radius: 3px;
        }}

        QSlider::handle:horizontal {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {colors['accent_blue']}, stop:1 {colors['accent_purple']});
            width: 16px;
            height: 16px;
            border-radius: 8px;
            margin: -5px 0;
        }}

        QSlider::handle:horizontal:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {colors['accent_purple']}, stop:1 {colors['accent_blue']});
        }}

        QSlider::sub-page:horizontal {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {colors['accent_blue']}, stop:1 {colors['accent_purple']});
            border-radius: 3px;
        }}

        /* Splitter Styling */
        QSplitter::handle {{
            background-color: {colors['border']};
            width: 2px;
            height: 2px;
        }}

        QSplitter::handle:hover {{
            background-color: {colors['accent_blue']};
        }}

        QSplitter::handle:pressed {{
            background-color: {colors['accent_purple']};
        }}

        /* Responsive Button Styling */
        QPushButton {{
            min-width: 30px;
            min-height: 28px;
        }}

        QPushButton:small {{
            min-width: 25px;
            min-height: 25px;
            font-size: 12px;
        }}
        """
        
    def get_color(self, color_name: str) -> str:
        """Get a specific color from the current theme"""
        return self.colors[self.current_theme].get(color_name, '#ffffff')
        
    def set_theme(self, theme_name: str):
        """Set the current theme"""
        if theme_name in self.colors:
            self.current_theme = theme_name
