"""
Configuration Manager for Offline Sentinel PyQt6 Application
Handles application settings and preferences
"""

import json
import os
from typing import Dict, Any, Optional
from PyQt6.QtCore import QObject, pyqtSignal


class ConfigManager(QObject):
    """Manages application configuration and settings"""
    
    config_changed = pyqtSignal(str, object)  # Signal emitted when config changes
    
    def __init__(self, config_file: str = "config/sentinel_config.json"):
        super().__init__()
        self.config_file = config_file
        self.config_dir = os.path.dirname(config_file)
        self.config = self.load_config()
        
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from file"""
        # Create config directory if it doesn't exist
        if self.config_dir and not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir, exist_ok=True)
            
        # Default configuration
        default_config = {
            "api": {
                "base_url": "http://localhost:8001",
                "timeout": 10,
                "retry_attempts": 3,
                "retry_delay": 1
            },
            "ui": {
                "theme": "dark",
                "update_interval": 5,
                "show_notifications": True,
                "minimize_to_tray": True,
                "start_minimized": False,
                "window_geometry": {
                    "width": 1600,
                    "height": 1000,
                    "x": 100,
                    "y": 100
                }
            },
            "agents": {
                "auto_start": True,
                "default_mode": "autonomous",
                "performance_level": "balanced",
                "enable_logging": True,
                "log_level": "INFO"
            },
            "monitoring": {
                "enable_live_updates": True,
                "update_frequency": 5,
                "show_performance_metrics": True,
                "alert_thresholds": {
                    "cpu_usage": 80,
                    "memory_usage": 85,
                    "disk_usage": 90,
                    "response_time": 1000
                }
            },
            "security": {
                "enable_anomaly_detection": True,
                "anomaly_threshold": 0.8,
                "enable_threat_monitoring": True,
                "auto_quarantine": False
            },
            "research": {
                "default_depth": "standard",
                "default_priority": "medium",
                "auto_save_results": True,
                "max_concurrent_tasks": 3
            },
            "network": {
                "enable_monitoring": True,
                "scan_interval": 30,
                "port_scan_enabled": False,
                "traffic_analysis": True
            },
            "export": {
                "default_format": "json",
                "include_metadata": True,
                "compress_exports": True,
                "auto_backup": True,
                "backup_interval": 24
            },
            "notifications": {
                "enable_desktop_notifications": True,
                "enable_sound": False,
                "notification_types": {
                    "agent_status": True,
                    "security_alerts": True,
                    "system_health": True,
                    "research_complete": True
                }
            }
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    loaded_config = json.load(f)
                    # Merge with defaults to ensure all keys exist
                    return self._merge_configs(default_config, loaded_config)
            else:
                # Save default config
                self.save_config(default_config)
                return default_config
                
        except (json.JSONDecodeError, IOError) as e:
            print(f"Error loading config: {e}. Using defaults.")
            return default_config
            
    def _merge_configs(self, default: Dict[str, Any], loaded: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively merge loaded config with defaults"""
        result = default.copy()
        
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
                
        return result
        
    def save_config(self, config: Optional[Dict[str, Any]] = None):
        """Save configuration to file"""
        if config is None:
            config = self.config
            
        try:
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
        except IOError as e:
            print(f"Error saving config: {e}")
            
    def get(self, key_path: str, default: Any = None) -> Any:
        """Get a configuration value using dot notation (e.g., 'ui.theme')"""
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
            
    def set(self, key_path: str, value: Any):
        """Set a configuration value using dot notation"""
        keys = key_path.split('.')
        config = self.config
        
        # Navigate to the parent of the target key
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
            
        # Set the value
        config[keys[-1]] = value
        
        # Save and emit signal
        self.save_config()
        self.config_changed.emit(key_path, value)
        
    def get_api_config(self) -> Dict[str, Any]:
        """Get API configuration"""
        return self.config.get('api', {})
        
    def get_ui_config(self) -> Dict[str, Any]:
        """Get UI configuration"""
        return self.config.get('ui', {})
        
    def get_agents_config(self) -> Dict[str, Any]:
        """Get agents configuration"""
        return self.config.get('agents', {})
        
    def get_monitoring_config(self) -> Dict[str, Any]:
        """Get monitoring configuration"""
        return self.config.get('monitoring', {})
        
    def get_security_config(self) -> Dict[str, Any]:
        """Get security configuration"""
        return self.config.get('security', {})
        
    def get_research_config(self) -> Dict[str, Any]:
        """Get research configuration"""
        return self.config.get('research', {})
        
    def get_network_config(self) -> Dict[str, Any]:
        """Get network configuration"""
        return self.config.get('network', {})
        
    def get_export_config(self) -> Dict[str, Any]:
        """Get export configuration"""
        return self.config.get('export', {})
        
    def get_notifications_config(self) -> Dict[str, Any]:
        """Get notifications configuration"""
        return self.config.get('notifications', {})
        
    def reset_to_defaults(self):
        """Reset configuration to defaults"""
        self.config = self.load_config()
        self.save_config()
        
    def export_config(self, file_path: str):
        """Export configuration to a file"""
        try:
            with open(file_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except IOError as e:
            print(f"Error exporting config: {e}")
            
    def import_config(self, file_path: str):
        """Import configuration from a file"""
        try:
            with open(file_path, 'r') as f:
                imported_config = json.load(f)
                self.config = self._merge_configs(self.load_config(), imported_config)
                self.save_config()
        except (json.JSONDecodeError, IOError) as e:
            print(f"Error importing config: {e}")
            
    def validate_config(self) -> bool:
        """Validate the current configuration"""
        required_sections = ['api', 'ui', 'agents', 'monitoring', 'security']
        
        for section in required_sections:
            if section not in self.config:
                return False
                
        # Validate API URL
        api_url = self.get('api.base_url')
        if not api_url or not api_url.startswith(('http://', 'https://')):
            return False
            
        # Validate numeric values
        numeric_configs = [
            ('api.timeout', int),
            ('ui.update_interval', int),
            ('monitoring.update_frequency', int),
            ('monitoring.alert_thresholds.cpu_usage', int),
            ('monitoring.alert_thresholds.memory_usage', int)
        ]
        
        for config_path, expected_type in numeric_configs:
            value = self.get(config_path)
            if not isinstance(value, expected_type) or value <= 0:
                return False
                
        return True
        
    def get_window_geometry(self) -> Dict[str, int]:
        """Get window geometry settings"""
        return self.get('ui.window_geometry', {
            'width': 1600,
            'height': 1000,
            'x': 100,
            'y': 100
        })
        
    def set_window_geometry(self, width: int, height: int, x: int, y: int):
        """Set window geometry settings"""
        self.set('ui.window_geometry.width', width)
        self.set('ui.window_geometry.height', height)
        self.set('ui.window_geometry.x', x)
        self.set('ui.window_geometry.y', y)
        
    def get_alert_thresholds(self) -> Dict[str, int]:
        """Get alert thresholds"""
        return self.get('monitoring.alert_thresholds', {
            'cpu_usage': 80,
            'memory_usage': 85,
            'disk_usage': 90,
            'response_time': 1000
        })
        
    def is_feature_enabled(self, feature_path: str) -> bool:
        """Check if a feature is enabled"""
        return bool(self.get(feature_path, False))
