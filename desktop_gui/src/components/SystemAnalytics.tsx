import React, { useState, useEffect } from 'react';
import './SystemAnalytics.css';

interface SystemMetrics {
  cpu: number;
  memory: number;
  disk: number;
  network: number;
  uptime: number;
  activeConnections: number;
  totalRequests: number;
  errorRate: number;
  responseTime: number;
}

interface PerformanceData {
  timestamp: Date;
  metrics: SystemMetrics;
}

interface SecurityEvent {
  id: string;
  timestamp: Date;
  type: 'info' | 'warning' | 'critical';
  source: string;
  message: string;
  resolved: boolean;
}

interface AnalyticsData {
  totalSessions: number;
  averageSessionDuration: number;
  mostUsedFeatures: Array<{ name: string; usage: number }>;
  userSatisfaction: number;
  errorFrequency: Array<{ error: string; count: number }>;
  performanceTrends: PerformanceData[];
}

const SystemAnalytics: React.FC = () => {
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics>({
    cpu: 0,
    memory: 0,
    disk: 0,
    network: 0,
    uptime: 0,
    activeConnections: 0,
    totalRequests: 0,
    errorRate: 0,
    responseTime: 0
  });

  const [performanceHistory, setPerformanceHistory] = useState<PerformanceData[]>([]);
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [analytics, setAnalytics] = useState<AnalyticsData>({
    totalSessions: 0,
    averageSessionDuration: 0,
    mostUsedFeatures: [],
    userSatisfaction: 0,
    errorFrequency: [],
    performanceTrends: []
  });

  const [activeTab, setActiveTab] = useState('overview');
  const [isMonitoring, setIsMonitoring] = useState(true);

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      if (isMonitoring) {
        updateSystemMetrics();
        updatePerformanceHistory();
        updateSecurityEvents();
        updateAnalytics();
      }
    }, 2000);

    return () => clearInterval(interval);
  }, [isMonitoring]);

  const updateSystemMetrics = () => {
    setSystemMetrics(prev => ({
      cpu: Math.max(0, Math.min(100, prev.cpu + (Math.random() - 0.5) * 10)),
      memory: Math.max(0, Math.min(100, prev.memory + (Math.random() - 0.5) * 5)),
      disk: Math.max(0, Math.min(100, prev.disk + (Math.random() - 0.5) * 2)),
      network: Math.max(0, Math.min(100, prev.network + (Math.random() - 0.5) * 15)),
      uptime: prev.uptime + 2,
      activeConnections: Math.max(0, prev.activeConnections + Math.floor((Math.random() - 0.5) * 3)),
      totalRequests: prev.totalRequests + Math.floor(Math.random() * 5),
      errorRate: Math.max(0, Math.min(10, prev.errorRate + (Math.random() - 0.5) * 0.5)),
      responseTime: Math.max(0, prev.responseTime + (Math.random() - 0.5) * 50)
    }));
  };

  const updatePerformanceHistory = () => {
    setPerformanceHistory(prev => {
      const newData: PerformanceData = {
        timestamp: new Date(),
        metrics: systemMetrics
      };
      
      const updated = [...prev, newData];
      // Keep only last 50 data points
      return updated.slice(-50);
    });
  };

  const updateSecurityEvents = () => {
    // Randomly add security events
    if (Math.random() < 0.1) {
      const eventTypes = ['info', 'warning', 'critical'] as const;
      const sources = ['Authentication', 'Network', 'File System', 'API', 'Database'];
      const messages = [
        'Successful login attempt',
        'Failed authentication detected',
        'Unusual network activity',
        'High CPU usage detected',
        'Memory usage threshold exceeded',
        'Disk space running low',
        'API rate limit exceeded',
        'Database connection timeout'
      ];

      const newEvent: SecurityEvent = {
        id: Date.now().toString(),
        timestamp: new Date(),
        type: eventTypes[Math.floor(Math.random() * eventTypes.length)],
        source: sources[Math.floor(Math.random() * sources.length)],
        message: messages[Math.floor(Math.random() * messages.length)],
        resolved: false
      };

      setSecurityEvents(prev => [newEvent, ...prev.slice(0, 49)]);
    }
  };

  const updateAnalytics = () => {
    setAnalytics(prev => ({
      totalSessions: prev.totalSessions + Math.floor(Math.random() * 2),
      averageSessionDuration: Math.max(0, prev.averageSessionDuration + (Math.random() - 0.5) * 30),
      mostUsedFeatures: [
        { name: 'Chat Interface', usage: 85 + Math.random() * 10 },
        { name: 'PDF Processing', usage: 72 + Math.random() * 10 },
        { name: 'Voice Commands', usage: 58 + Math.random() * 10 },
        { name: 'Sub-Agent Network', usage: 45 + Math.random() * 10 },
        { name: 'System Analytics', usage: 32 + Math.random() * 10 }
      ],
      userSatisfaction: Math.max(0, Math.min(100, prev.userSatisfaction + (Math.random() - 0.5) * 2)),
      errorFrequency: [
        { error: 'Network Timeout', count: Math.floor(Math.random() * 5) },
        { error: 'PDF Parse Error', count: Math.floor(Math.random() * 3) },
        { error: 'Voice Recognition Failed', count: Math.floor(Math.random() * 2) },
        { error: 'Agent Communication Error', count: Math.floor(Math.random() * 4) }
      ],
      performanceTrends: performanceHistory
    }));
  };

  const resolveSecurityEvent = (eventId: string) => {
    setSecurityEvents(prev =>
      prev.map(event =>
        event.id === eventId ? { ...event, resolved: true } : event
      )
    );
  };

  const getMetricColor = (value: number, threshold: number = 80) => {
    if (value < threshold * 0.6) return '#30d158';
    if (value < threshold) return '#ff9500';
    return '#ff3b30';
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };

  const renderOverviewTab = () => (
    <div className="overview-tab">
      <div className="metrics-grid">
        <div className="metric-card">
          <div className="metric-header">
            <h3>💻 CPU Usage</h3>
            <span className="metric-value" style={{ color: getMetricColor(systemMetrics.cpu) }}>
              {systemMetrics.cpu.toFixed(1)}%
            </span>
          </div>
          <div className="metric-bar">
            <div 
              className="metric-fill" 
              style={{ 
                width: `${systemMetrics.cpu}%`,
                backgroundColor: getMetricColor(systemMetrics.cpu)
              }}
            ></div>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <h3>🧠 Memory</h3>
            <span className="metric-value" style={{ color: getMetricColor(systemMetrics.memory) }}>
              {systemMetrics.memory.toFixed(1)}%
            </span>
          </div>
          <div className="metric-bar">
            <div 
              className="metric-fill" 
              style={{ 
                width: `${systemMetrics.memory}%`,
                backgroundColor: getMetricColor(systemMetrics.memory)
              }}
            ></div>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <h3>💾 Disk Usage</h3>
            <span className="metric-value" style={{ color: getMetricColor(systemMetrics.disk) }}>
              {systemMetrics.disk.toFixed(1)}%
            </span>
          </div>
          <div className="metric-bar">
            <div 
              className="metric-fill" 
              style={{ 
                width: `${systemMetrics.disk}%`,
                backgroundColor: getMetricColor(systemMetrics.disk)
              }}
            ></div>
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <h3>🌐 Network</h3>
            <span className="metric-value" style={{ color: getMetricColor(systemMetrics.network) }}>
              {systemMetrics.network.toFixed(1)}%
            </span>
          </div>
          <div className="metric-bar">
            <div 
              className="metric-fill" 
              style={{ 
                width: `${systemMetrics.network}%`,
                backgroundColor: getMetricColor(systemMetrics.network)
              }}
            ></div>
          </div>
        </div>
      </div>

      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">⏱️</div>
          <div className="stat-info">
            <div className="stat-label">Uptime</div>
            <div className="stat-value">{formatUptime(systemMetrics.uptime)}</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">🔗</div>
          <div className="stat-info">
            <div className="stat-label">Active Connections</div>
            <div className="stat-value">{systemMetrics.activeConnections}</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">📊</div>
          <div className="stat-info">
            <div className="stat-label">Total Requests</div>
            <div className="stat-value">{systemMetrics.totalRequests.toLocaleString()}</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">⚡</div>
          <div className="stat-info">
            <div className="stat-label">Response Time</div>
            <div className="stat-value">{systemMetrics.responseTime.toFixed(0)}ms</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">❌</div>
          <div className="stat-info">
            <div className="stat-label">Error Rate</div>
            <div className="stat-value">{systemMetrics.errorRate.toFixed(2)}%</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">😊</div>
          <div className="stat-info">
            <div className="stat-label">User Satisfaction</div>
            <div className="stat-value">{analytics.userSatisfaction.toFixed(1)}%</div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSecurityTab = () => (
    <div className="security-tab">
      <div className="security-header">
        <h3>🛡️ Security Events</h3>
        <div className="security-stats">
          <span className="security-stat critical">
            {securityEvents.filter(e => e.type === 'critical' && !e.resolved).length} Critical
          </span>
          <span className="security-stat warning">
            {securityEvents.filter(e => e.type === 'warning' && !e.resolved).length} Warnings
          </span>
          <span className="security-stat info">
            {securityEvents.filter(e => e.type === 'info' && !e.resolved).length} Info
          </span>
        </div>
      </div>

      <div className="security-events">
        {securityEvents.map(event => (
          <div 
            key={event.id} 
            className={`security-event ${event.type} ${event.resolved ? 'resolved' : ''}`}
          >
            <div className="event-header">
              <div className="event-type-icon">
                {event.type === 'critical' ? '🚨' : event.type === 'warning' ? '⚠️' : 'ℹ️'}
              </div>
              <div className="event-info">
                <div className="event-source">{event.source}</div>
                <div className="event-time">{event.timestamp.toLocaleTimeString()}</div>
              </div>
              {!event.resolved && (
                <button 
                  className="resolve-btn"
                  onClick={() => resolveSecurityEvent(event.id)}
                >
                  Resolve
                </button>
              )}
            </div>
            <div className="event-message">{event.message}</div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderAnalyticsTab = () => (
    <div className="analytics-tab">
      <div className="analytics-section">
        <h3>📈 Usage Analytics</h3>
        <div className="usage-stats">
          <div className="usage-stat">
            <span className="usage-label">Total Sessions:</span>
            <span className="usage-value">{analytics.totalSessions.toLocaleString()}</span>
          </div>
          <div className="usage-stat">
            <span className="usage-label">Avg Session Duration:</span>
            <span className="usage-value">{Math.round(analytics.averageSessionDuration)}m</span>
          </div>
        </div>
      </div>

      <div className="analytics-section">
        <h3>🎯 Feature Usage</h3>
        <div className="feature-usage">
          {analytics.mostUsedFeatures.map((feature, index) => (
            <div key={index} className="feature-item">
              <div className="feature-name">{feature.name}</div>
              <div className="feature-bar">
                <div 
                  className="feature-fill" 
                  style={{ width: `${feature.usage}%` }}
                ></div>
              </div>
              <div className="feature-percentage">{feature.usage.toFixed(1)}%</div>
            </div>
          ))}
        </div>
      </div>

      <div className="analytics-section">
        <h3>🐛 Error Frequency</h3>
        <div className="error-frequency">
          {analytics.errorFrequency.map((error, index) => (
            <div key={index} className="error-item">
              <span className="error-name">{error.error}</span>
              <span className="error-count">{error.count}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div className="system-analytics">
      <div className="analytics-header">
        <div className="header-left">
          <h2>📊 System Analytics</h2>
          <div className="monitoring-status">
            <div className={`status-indicator ${isMonitoring ? 'active' : 'inactive'}`}></div>
            <span>{isMonitoring ? 'Monitoring Active' : 'Monitoring Paused'}</span>
          </div>
        </div>

        <div className="header-controls">
          <button 
            className={`monitor-btn ${isMonitoring ? 'active' : ''}`}
            onClick={() => setIsMonitoring(!isMonitoring)}
          >
            {isMonitoring ? '⏸️ Pause' : '▶️ Start'} Monitoring
          </button>
          <button className="export-btn">📤 Export Data</button>
        </div>
      </div>

      <div className="analytics-tabs">
        <button 
          className={activeTab === 'overview' ? 'active' : ''}
          onClick={() => setActiveTab('overview')}
        >
          📊 Overview
        </button>
        <button 
          className={activeTab === 'security' ? 'active' : ''}
          onClick={() => setActiveTab('security')}
        >
          🛡️ Security
        </button>
        <button 
          className={activeTab === 'analytics' ? 'active' : ''}
          onClick={() => setActiveTab('analytics')}
        >
          📈 Analytics
        </button>
      </div>

      <div className="analytics-content">
        {activeTab === 'overview' && renderOverviewTab()}
        {activeTab === 'security' && renderSecurityTab()}
        {activeTab === 'analytics' && renderAnalyticsTab()}
      </div>
    </div>
  );
};

export default SystemAnalytics;
