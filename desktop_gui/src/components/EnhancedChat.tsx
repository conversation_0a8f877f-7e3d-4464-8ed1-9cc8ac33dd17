import React, { useState, useEffect, useRef, useCallback } from 'react';
import { apiService } from '../services/api.service';
import './EnhancedChat.css';

interface Message {
  id: string;
  content: string;
  sender: string;
  timestamp: Date;
  isUser: boolean;
  type: 'text' | 'code' | 'file' | 'system' | 'error';
  attachments?: string[];
}

interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  agent: string;
  createdAt: Date;
}

interface VoiceRecognition {
  start: () => void;
  stop: () => void;
  onresult: (event: any) => void;
  onerror: (event: any) => void;
  onstart: () => void;
  onend: () => void;
  continuous: boolean;
  interimResults: boolean;
}

declare global {
  interface Window {
    webkitSpeechRecognition: new () => VoiceRecognition;
    SpeechRecognition: new () => VoiceRecognition;
  }
}

const EnhancedChat: React.FC = () => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversationId, setCurrentConversationId] = useState<string>('default');
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [currentAgent, setCurrentAgent] = useState('🔬 Research Agent');
  const [isRecording, setIsRecording] = useState(false);
  const [autoTTS, setAutoTTS] = useState(true);
  const [voiceEnabled, setVoiceEnabled] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const recognitionRef = useRef<VoiceRecognition | null>(null);
  const synthRef = useRef<SpeechSynthesis | null>(null);
  
  // Initialize speech recognition and synthesis
  useEffect(() => {
    // Check for speech recognition support
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (SpeechRecognition) {
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = false;
      recognitionRef.current.interimResults = false;
      
      recognitionRef.current.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setInputText(transcript);
        setIsRecording(false);
      };
      
      recognitionRef.current.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        setIsRecording(false);
      };
      
      recognitionRef.current.onend = () => {
        setIsRecording(false);
      };
      
      setVoiceEnabled(true);
    }
    
    // Initialize speech synthesis
    if ('speechSynthesis' in window) {
      synthRef.current = window.speechSynthesis;
    }
    
    // Initialize default conversation
    initializeDefaultConversation();
  }, []);

  const initializeDefaultConversation = () => {
    const defaultConv: Conversation = {
      id: 'default',
      title: 'General Chat',
      messages: [{
        id: '1',
        content: 'Welcome! I\'m your AI assistant. How can I help you today?',
        sender: '🤖 AI Assistant',
        timestamp: new Date(),
        isUser: false,
        type: 'text'
      }],
      agent: '🔬 Research Agent',
      createdAt: new Date()
    };
    
    setConversations([defaultConv]);
    setMessages(defaultConv.messages);
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const addMessage = useCallback((content: string, sender: string, isUser: boolean, type: 'text' | 'code' | 'file' | 'system' | 'error' = 'text') => {
    const newMessage: Message = {
      id: Date.now().toString(),
      content,
      sender,
      timestamp: new Date(),
      isUser,
      type
    };

    setMessages(prev => [...prev, newMessage]);
    
    // Update conversation
    setConversations(prev => prev.map(conv => 
      conv.id === currentConversationId 
        ? { ...conv, messages: [...conv.messages, newMessage] }
        : conv
    ));

    // Auto TTS for agent messages
    if (!isUser && autoTTS && synthRef.current) {
      const utterance = new SpeechSynthesisUtterance(content);
      utterance.rate = 0.9;
      utterance.pitch = 1;
      synthRef.current.speak(utterance);
    }
  }, [currentConversationId, autoTTS]);

  const sendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage = inputText.trim();
    setInputText('');
    
    // Add user message
    addMessage(userMessage, '👤 You', true);
    
    // Show typing indicator
    setIsTyping(true);
    
    try {
      // Send to API
      const response = await apiService.sendChatMessage(userMessage, currentAgent);
      
      setIsTyping(false);
      
      if (response && response.response) {
        addMessage(response.response, currentAgent, false);
      } else {
        // Fallback response
        const fallbackResponse = generateFallbackResponse(userMessage);
        addMessage(fallbackResponse, currentAgent, false);
      }
    } catch (error) {
      setIsTyping(false);
      addMessage(`Sorry, I'm having trouble connecting. Error: ${error}`, currentAgent, false, 'error');
    }
  };

  const generateFallbackResponse = (message: string): string => {
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('status')) {
      return 'System status: All agents are operational. CPU: 45%, Memory: 68%, Network: Online.';
    } else if (lowerMessage.includes('health')) {
      return 'Health check complete: System health is at 94%. All components are functioning normally.';
    } else if (lowerMessage.includes('agent')) {
      return 'Agent status: 4 agents active - Research Agent (85%), System Agent (99%), Upgrade Agent (73%), Communication Agent (95%).';
    } else if (lowerMessage.includes('help')) {
      return 'I can help you with system monitoring, agent management, security analysis, and general questions about the Offline Sentinel system.';
    } else {
      return `I understand you're asking about: "${message}". I'm currently processing your request and will provide detailed information shortly.`;
    }
  };

  const startVoiceRecording = () => {
    if (recognitionRef.current && voiceEnabled) {
      setIsRecording(true);
      recognitionRef.current.start();
    }
  };

  const stopVoiceRecording = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
      setIsRecording(false);
    }
  };

  const createNewConversation = () => {
    const newConv: Conversation = {
      id: Date.now().toString(),
      title: `Chat ${conversations.length + 1}`,
      messages: [{
        id: Date.now().toString(),
        content: 'New conversation started. How can I help you?',
        sender: currentAgent,
        timestamp: new Date(),
        isUser: false,
        type: 'text'
      }],
      agent: currentAgent,
      createdAt: new Date()
    };
    
    setConversations(prev => [...prev, newConv]);
    setCurrentConversationId(newConv.id);
    setMessages(newConv.messages);
  };

  const switchConversation = (convId: string) => {
    const conv = conversations.find(c => c.id === convId);
    if (conv) {
      setCurrentConversationId(convId);
      setMessages(conv.messages);
      setCurrentAgent(conv.agent);
    }
  };

  const clearCurrentChat = () => {
    const welcomeMessage: Message = {
      id: Date.now().toString(),
      content: 'Chat cleared. How can I help you?',
      sender: currentAgent,
      timestamp: new Date(),
      isUser: false,
      type: 'text'
    };
    
    setMessages([welcomeMessage]);
    setConversations(prev => prev.map(conv => 
      conv.id === currentConversationId 
        ? { ...conv, messages: [welcomeMessage] }
        : conv
    ));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  const speakMessage = (content: string) => {
    if (synthRef.current) {
      const utterance = new SpeechSynthesisUtterance(content);
      synthRef.current.speak(utterance);
    }
  };

  return (
    <div className="enhanced-chat">
      {/* Conversations Sidebar */}
      <div className="conversations-sidebar">
        <div className="conversations-header">
          <h3>💬 Conversations</h3>
          <button className="new-chat-btn" onClick={createNewConversation}>
            ➕
          </button>
        </div>
        <div className="conversations-list">
          {conversations.map(conv => (
            <div 
              key={conv.id}
              className={`conversation-item ${conv.id === currentConversationId ? 'active' : ''}`}
              onClick={() => switchConversation(conv.id)}
            >
              <div className="conversation-title">{conv.title}</div>
              <div className="conversation-preview">
                {conv.messages[conv.messages.length - 1]?.content.substring(0, 50)}...
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="chat-main">
        {/* Chat Header */}
        <div className="chat-header">
          <div className="chat-title">
            <h2>AI Assistant</h2>
            <p>Chat with your AI agents</p>
          </div>
          <div className="chat-controls">
            <label className="auto-tts-toggle">
              <input 
                type="checkbox" 
                checked={autoTTS} 
                onChange={(e) => setAutoTTS(e.target.checked)}
              />
              🔊 Auto TTS
            </label>
            <button className="clear-chat-btn" onClick={clearCurrentChat}>
              🗑️
            </button>
          </div>
        </div>

        {/* Messages Area */}
        <div className="messages-container">
          <div className="messages-list">
            {messages.map(message => (
              <div key={message.id} className={`message ${message.isUser ? 'user' : 'agent'} ${message.type}`}>
                <div className="message-bubble">
                  <div className="message-header">
                    {!message.isUser && <span className="sender">{message.sender}</span>}
                    <div className="message-actions">
                      <button onClick={() => copyMessage(message.content)} title="Copy">📋</button>
                      {!message.isUser && (
                        <button onClick={() => speakMessage(message.content)} title="Speak">🔊</button>
                      )}
                    </div>
                    <span className="timestamp">{message.timestamp.toLocaleTimeString()}</span>
                  </div>
                  <div className="message-content">
                    {message.type === 'code' ? (
                      <pre><code>{message.content}</code></pre>
                    ) : (
                      <p>{message.content}</p>
                    )}
                  </div>
                </div>
              </div>
            ))}
            {isTyping && (
              <div className="message agent">
                <div className="message-bubble typing">
                  <div className="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Input Area */}
        <div className="input-container">
          <div className="input-wrapper">
            <input
              type="text"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message..."
              className="message-input"
            />
            <div className="input-buttons">
              {voiceEnabled && (
                <button 
                  className={`voice-btn ${isRecording ? 'recording' : ''}`}
                  onClick={isRecording ? stopVoiceRecording : startVoiceRecording}
                >
                  {isRecording ? '🛑' : '🎤'}
                </button>
              )}
              <button className="send-btn" onClick={sendMessage}>
                ➤
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Controls Sidebar */}
      <div className="controls-sidebar">
        {/* Agent Selector */}
        <div className="control-section">
          <h4>🤖 AI Agent</h4>
          <select 
            value={currentAgent} 
            onChange={(e) => setCurrentAgent(e.target.value)}
            className="agent-selector"
          >
            <option value="🔬 Research Agent">🔬 Research Agent</option>
            <option value="⚙️ System Agent">⚙️ System Agent</option>
            <option value="🔧 Upgrade Agent">🔧 Upgrade Agent</option>
            <option value="📡 Communication Agent">📡 Communication Agent</option>
            <option value="🧠 General AI Assistant">🧠 General AI Assistant</option>
          </select>
          <div className="agent-status">
            <span className="status-dot online"></span>
            <span>Online</span>
          </div>
        </div>

        {/* Voice Settings */}
        <div className="control-section">
          <h4>🎤 Voice Settings</h4>
          <label>
            <input 
              type="checkbox" 
              checked={voiceEnabled} 
              onChange={(e) => setVoiceEnabled(e.target.checked)}
              disabled={!recognitionRef.current}
            />
            Enable Voice Input
          </label>
          <label>
            <input 
              type="checkbox" 
              checked={autoTTS} 
              onChange={(e) => setAutoTTS(e.target.checked)}
            />
            Auto Text-to-Speech
          </label>
        </div>

        {/* Quick Actions */}
        <div className="control-section">
          <h4>⚡ Quick Actions</h4>
          <button onClick={() => setInputText("What's the current system status?")}>
            📊 System Status
          </button>
          <button onClick={() => setInputText("Run a system health check")}>
            💚 Health Check
          </button>
          <button onClick={() => setInputText("Show me the status of all agents")}>
            🤖 Agent Status
          </button>
        </div>
      </div>
    </div>
  );
};

export default EnhancedChat;
