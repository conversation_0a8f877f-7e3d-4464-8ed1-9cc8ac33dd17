import React, { useState, useEffect, useCallback } from 'react';
import { apiService } from '../services/api.service';
import './PDFProcessor.css';

interface PDFDocument {
  id: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  uploadedAt: Date;
  processed: boolean;
  processing: boolean;
  progress: number;
  metadata?: {
    title?: string;
    author?: string;
    pageCount?: number;
    creationDate?: string;
  };
  content?: {
    text: string;
    wordCount: number;
    summary?: string;
    keywords?: string[];
  };
  analysis?: {
    sentiment: string;
    topics: string[];
    language: string;
  };
  activeInAI: boolean;
}

interface ProcessingOptions {
  enableOCR: boolean;
  extractImages: boolean;
  deepAnalysis: boolean;
  generateSummary: boolean;
}

const PDFProcessor: React.FC = () => {
  const [documents, setDocuments] = useState<PDFDocument[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<PDFDocument | null>(null);
  const [processingQueue, setProcessingQueue] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('overview');
  const [processingOptions, setProcessingOptions] = useState<ProcessingOptions>({
    enableOCR: false,
    extractImages: true,
    deepAnalysis: true,
    generateSummary: true
  });
  const [aiResponse, setAiResponse] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // Filter documents based on search term
  const filteredDocuments = documents.filter(doc =>
    doc.fileName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doc.metadata?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doc.metadata?.author?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get active PDFs (shared with AI)
  const activePDFs = documents.filter(doc => doc.activeInAI);

  const handleFileUpload = useCallback(async (files: FileList) => {
    const pdfFiles = Array.from(files).filter(file => 
      file.type === 'application/pdf' && file.size <= 100 * 1024 * 1024 // 100MB limit
    );

    for (const file of pdfFiles) {
      const newDoc: PDFDocument = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        fileName: file.name,
        filePath: URL.createObjectURL(file),
        fileSize: file.size,
        uploadedAt: new Date(),
        processed: false,
        processing: true,
        progress: 0,
        activeInAI: false
      };

      setDocuments(prev => [...prev, newDoc]);
      setProcessingQueue(prev => [...prev, newDoc.id]);

      // Simulate processing
      await processDocument(newDoc, file);
    }
  }, [processingOptions]);

  const processDocument = async (doc: PDFDocument, file: File) => {
    try {
      // Simulate processing steps
      const steps = [
        { progress: 20, status: 'Extracting metadata...' },
        { progress: 40, status: 'Extracting text content...' },
        { progress: 60, status: 'Analyzing content...' },
        { progress: 80, status: 'Generating summary...' },
        { progress: 100, status: 'Complete' }
      ];

      for (const step of steps) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setDocuments(prev => prev.map(d => 
          d.id === doc.id 
            ? { ...d, progress: step.progress }
            : d
        ));
      }

      // Simulate extracted data
      const processedDoc: PDFDocument = {
        ...doc,
        processed: true,
        processing: false,
        progress: 100,
        metadata: {
          title: `Document Title - ${doc.fileName}`,
          author: 'Unknown Author',
          pageCount: Math.floor(Math.random() * 50) + 1,
          creationDate: new Date().toISOString()
        },
        content: {
          text: 'This is simulated extracted text content from the PDF document. In a real implementation, this would contain the actual text extracted from the PDF file.',
          wordCount: Math.floor(Math.random() * 5000) + 500,
          summary: 'This document discusses important topics related to the subject matter. Key findings include various insights and recommendations.',
          keywords: ['keyword1', 'keyword2', 'keyword3', 'analysis', 'research']
        },
        analysis: {
          sentiment: 'neutral',
          topics: ['Technology', 'Research', 'Analysis'],
          language: 'English'
        }
      };

      setDocuments(prev => prev.map(d => 
        d.id === doc.id ? processedDoc : d
      ));

      setProcessingQueue(prev => prev.filter(id => id !== doc.id));

      // Auto-select if first document
      if (documents.length === 0) {
        setSelectedDocument(processedDoc);
      }

    } catch (error) {
      console.error('Processing error:', error);
      setDocuments(prev => prev.map(d => 
        d.id === doc.id 
          ? { ...d, processing: false, processed: false }
          : d
      ));
      setProcessingQueue(prev => prev.filter(id => id !== doc.id));
    }
  };

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files);
    }
  }, [handleFileUpload]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const selectDocument = (doc: PDFDocument) => {
    setSelectedDocument(doc);
    setActiveTab('overview');
  };

  const shareWithAI = (doc: PDFDocument) => {
    setDocuments(prev => prev.map(d => 
      d.id === doc.id ? { ...d, activeInAI: true } : d
    ));
  };

  const removeFromAI = (doc: PDFDocument) => {
    setDocuments(prev => prev.map(d => 
      d.id === doc.id ? { ...d, activeInAI: false } : d
    ));
  };

  const removeDocument = (doc: PDFDocument) => {
    setDocuments(prev => prev.filter(d => d.id !== doc.id));
    if (selectedDocument?.id === doc.id) {
      setSelectedDocument(null);
    }
  };

  const performAIAnalysis = async (type: string) => {
    if (!selectedDocument) return;

    setIsAnalyzing(true);
    setAiResponse(`🤖 Performing ${type} analysis...\n\nPlease wait while I analyze the document.`);

    // Simulate AI processing
    await new Promise(resolve => setTimeout(resolve, 2000));

    let response = '';
    switch (type) {
      case 'summarize':
        response = `📝 Document Summary:\n\n${selectedDocument.content?.summary || 'No summary available'}\n\nThis analysis provides key insights from the document content.`;
        break;
      case 'extract-key-points':
        response = `🔑 Key Points:\n\n• Main topic: ${selectedDocument.metadata?.title || 'Document analysis'}\n• Word count: ${selectedDocument.content?.wordCount || 0}\n• Key themes: ${selectedDocument.analysis?.topics?.join(', ') || 'Various topics'}\n• Language: ${selectedDocument.analysis?.language || 'Unknown'}`;
        break;
      case 'translate':
        response = `🌐 Translation:\n\nDetected language: ${selectedDocument.analysis?.language || 'Unknown'}\n\nTranslation feature would be implemented here with actual translation service.`;
        break;
      case 'compare':
        response = `⚖️ Document Comparison:\n\nComparing with other documents in the collection...\n\nComparison analysis would be implemented here.`;
        break;
      default:
        response = 'Analysis complete.';
    }

    setAiResponse(response);
    setIsAnalyzing(false);
  };

  const exportAnalysis = () => {
    if (!selectedDocument) return;

    const analysisData = {
      document: selectedDocument,
      exportedAt: new Date().toISOString(),
      analysis: aiResponse
    };

    const blob = new Blob([JSON.stringify(analysisData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `analysis_${selectedDocument.fileName}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="pdf-processor">
      {/* Left Panel - Upload and Document List */}
      <div className="pdf-left-panel">
        <div className="panel-header">
          <h3>📄 PDF Documents</h3>
        </div>

        {/* Upload Area */}
        <div className="upload-section">
          <h4>Upload Documents</h4>
          <div 
            className="drop-zone"
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onClick={() => document.getElementById('file-input')?.click()}
          >
            <div className="drop-icon">📄</div>
            <div className="drop-text">
              <p>Drop PDF files here</p>
              <span>or click to browse</span>
            </div>
          </div>
          <input
            id="file-input"
            type="file"
            multiple
            accept=".pdf"
            style={{ display: 'none' }}
            onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
          />

          {/* Processing Options */}
          <div className="processing-options">
            <label>
              <input
                type="checkbox"
                checked={processingOptions.enableOCR}
                onChange={(e) => setProcessingOptions(prev => ({ ...prev, enableOCR: e.target.checked }))}
              />
              Enable OCR
            </label>
            <label>
              <input
                type="checkbox"
                checked={processingOptions.deepAnalysis}
                onChange={(e) => setProcessingOptions(prev => ({ ...prev, deepAnalysis: e.target.checked }))}
              />
              Deep Analysis
            </label>
          </div>
        </div>

        {/* Document Search */}
        <div className="search-section">
          <input
            type="text"
            placeholder="Search documents..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        {/* Documents List */}
        <div className="documents-list">
          <h4>Processed Documents</h4>
          <div className="document-items">
            {filteredDocuments.map(doc => (
              <div
                key={doc.id}
                className={`document-item ${selectedDocument?.id === doc.id ? 'selected' : ''}`}
                onClick={() => selectDocument(doc)}
              >
                <div className="doc-icon">
                  {doc.processing ? '⏳' : doc.processed ? '📄' : '❌'}
                </div>
                <div className="doc-info">
                  <div className="doc-name">{doc.fileName}</div>
                  <div className="doc-details">
                    {doc.processing ? (
                      <span>Processing... {doc.progress}%</span>
                    ) : (
                      <span>{formatFileSize(doc.fileSize)} • {doc.metadata?.pageCount || 0} pages</span>
                    )}
                  </div>
                  {doc.processing && (
                    <div className="progress-bar">
                      <div className="progress-fill" style={{ width: `${doc.progress}%` }}></div>
                    </div>
                  )}
                </div>
                {doc.activeInAI && <div className="ai-indicator">🔗</div>}
              </div>
            ))}
          </div>
        </div>

        {/* Active PDFs */}
        <div className="active-pdfs">
          <h4>Active in AI</h4>
          <div className="active-list">
            {activePDFs.map(doc => (
              <div key={doc.id} className="active-item">
                <span>🔗 {doc.fileName}</span>
                <button onClick={() => removeFromAI(doc)}>✕</button>
              </div>
            ))}
            {activePDFs.length === 0 && (
              <div className="empty-state">No documents shared with AI</div>
            )}
          </div>
        </div>
      </div>

      {/* Center Panel - Document Viewer */}
      <div className="pdf-center-panel">
        {selectedDocument ? (
          <>
            <div className="document-header">
              <h2>{selectedDocument.fileName}</h2>
              <div className="document-status">
                {selectedDocument.processing ? 'Processing...' : 'Ready'}
              </div>
            </div>

            <div className="document-tabs">
              <button
                className={activeTab === 'overview' ? 'active' : ''}
                onClick={() => setActiveTab('overview')}
              >
                📊 Overview
              </button>
              <button
                className={activeTab === 'content' ? 'active' : ''}
                onClick={() => setActiveTab('content')}
              >
                📄 Content
              </button>
              <button
                className={activeTab === 'analysis' ? 'active' : ''}
                onClick={() => setActiveTab('analysis')}
              >
                🔍 Analysis
              </button>
            </div>

            <div className="document-content">
              {activeTab === 'overview' && (
                <div className="overview-tab">
                  <div className="info-grid">
                    <div className="info-item">
                      <label>File Name:</label>
                      <span>{selectedDocument.fileName}</span>
                    </div>
                    <div className="info-item">
                      <label>File Size:</label>
                      <span>{formatFileSize(selectedDocument.fileSize)}</span>
                    </div>
                    <div className="info-item">
                      <label>Pages:</label>
                      <span>{selectedDocument.metadata?.pageCount || 0}</span>
                    </div>
                    <div className="info-item">
                      <label>Title:</label>
                      <span>{selectedDocument.metadata?.title || 'Unknown'}</span>
                    </div>
                    <div className="info-item">
                      <label>Author:</label>
                      <span>{selectedDocument.metadata?.author || 'Unknown'}</span>
                    </div>
                    <div className="info-item">
                      <label>Uploaded:</label>
                      <span>{selectedDocument.uploadedAt.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'content' && (
                <div className="content-tab">
                  <div className="content-text">
                    {selectedDocument.content?.text || 'No text content available'}
                  </div>
                </div>
              )}

              {activeTab === 'analysis' && (
                <div className="analysis-tab">
                  <div className="stats-grid">
                    <div className="stat-item">
                      <label>Word Count:</label>
                      <span>{selectedDocument.content?.wordCount?.toLocaleString() || 0}</span>
                    </div>
                    <div className="stat-item">
                      <label>Language:</label>
                      <span>{selectedDocument.analysis?.language || 'Unknown'}</span>
                    </div>
                    <div className="stat-item">
                      <label>Sentiment:</label>
                      <span>{selectedDocument.analysis?.sentiment || 'Neutral'}</span>
                    </div>
                  </div>

                  {selectedDocument.content?.keywords && (
                    <div className="keywords-section">
                      <h4>Keywords</h4>
                      <div className="keywords-list">
                        {selectedDocument.content.keywords.map((keyword, index) => (
                          <span key={index} className="keyword-tag">{keyword}</span>
                        ))}
                      </div>
                    </div>
                  )}

                  {selectedDocument.content?.summary && (
                    <div className="summary-section">
                      <h4>Summary</h4>
                      <p>{selectedDocument.content.summary}</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </>
        ) : (
          <div className="no-document">
            <div className="no-doc-icon">📄</div>
            <h3>No document selected</h3>
            <p>Select a document from the list to view its details</p>
          </div>
        )}
      </div>

      {/* Right Panel - AI Analysis and Controls */}
      <div className="pdf-right-panel">
        <div className="ai-analysis-section">
          <h4>🤖 AI Analysis</h4>
          
          <div className="analysis-buttons">
            <button onClick={() => performAIAnalysis('summarize')} disabled={!selectedDocument || isAnalyzing}>
              📝 Summarize
            </button>
            <button onClick={() => performAIAnalysis('extract-key-points')} disabled={!selectedDocument || isAnalyzing}>
              🔑 Key Points
            </button>
            <button onClick={() => performAIAnalysis('translate')} disabled={!selectedDocument || isAnalyzing}>
              🌐 Translate
            </button>
            <button onClick={() => performAIAnalysis('compare')} disabled={!selectedDocument || isAnalyzing}>
              ⚖️ Compare
            </button>
          </div>

          <div className="ai-response">
            <textarea
              value={aiResponse}
              readOnly
              placeholder="AI analysis results will appear here..."
            />
          </div>
        </div>

        <div className="document-actions">
          <h4>Document Actions</h4>
          <button onClick={exportAnalysis} disabled={!selectedDocument}>
            📤 Export Analysis
          </button>
          <button 
            onClick={() => selectedDocument && shareWithAI(selectedDocument)} 
            disabled={!selectedDocument || selectedDocument?.activeInAI}
          >
            🔗 Share with AI
          </button>
          <button 
            onClick={() => selectedDocument && removeDocument(selectedDocument)} 
            disabled={!selectedDocument}
            className="danger"
          >
            🗑️ Remove Document
          </button>
        </div>

        {processingQueue.length > 0 && (
          <div className="processing-queue">
            <h4>Processing Queue</h4>
            <div className="queue-items">
              {processingQueue.map(docId => {
                const doc = documents.find(d => d.id === docId);
                return doc ? (
                  <div key={docId} className="queue-item">
                    <span>{doc.fileName}</span>
                    <span>{doc.progress}%</span>
                  </div>
                ) : null;
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PDFProcessor;
