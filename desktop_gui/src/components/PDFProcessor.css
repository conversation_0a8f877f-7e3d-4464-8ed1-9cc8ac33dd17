.pdf-processor {
  display: flex;
  height: 100vh;
  background: #000000;
  color: #ffffff;
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Left Panel */
.pdf-left-panel {
  width: 300px;
  background: #1c1c1e;
  border-right: 1px solid #3a3a3c;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #3a3a3c;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}

.upload-section {
  padding: 16px;
  border-bottom: 1px solid #3a3a3c;
}

.upload-section h4 {
  margin: 0 0 12px 0;
  font-size: 12px;
  font-weight: 600;
  color: #ffffff;
}

.drop-zone {
  border: 2px dashed #3a3a3c;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  background: rgba(255, 255, 255, 0.02);
  margin-bottom: 12px;
}

.drop-zone:hover {
  border-color: #007aff;
  background: rgba(0, 122, 255, 0.05);
}

.drop-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.drop-text p {
  margin: 0 0 4px 0;
  font-weight: 600;
  color: #ffffff;
}

.drop-text span {
  font-size: 11px;
  color: #8e8e93;
}

.processing-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.processing-options label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 11px;
  color: #ffffff;
  cursor: pointer;
}

.processing-options input[type="checkbox"] {
  width: 14px;
  height: 14px;
  accent-color: #007aff;
}

.search-section {
  padding: 16px;
  border-bottom: 1px solid #3a3a3c;
}

.search-input {
  width: 100%;
  background: #2c2c2e;
  border: 1px solid #3a3a3c;
  border-radius: 8px;
  padding: 8px 12px;
  color: #ffffff;
  font-size: 12px;
}

.search-input:focus {
  outline: none;
  border-color: #007aff;
}

.documents-list {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.documents-list h4 {
  margin: 0 0 12px 0;
  font-size: 12px;
  font-weight: 600;
  color: #ffffff;
}

.document-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.document-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #2c2c2e;
  border: 1px solid #3a3a3c;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.document-item:hover {
  background: #3a3a3c;
}

.document-item.selected {
  background: #007aff;
  border-color: #007aff;
}

.doc-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.doc-info {
  flex: 1;
  min-width: 0;
}

.doc-name {
  font-size: 11px;
  font-weight: 600;
  color: #ffffff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.doc-details {
  font-size: 9px;
  color: #8e8e93;
  margin-top: 2px;
}

.document-item.selected .doc-details {
  color: rgba(255, 255, 255, 0.8);
}

.progress-bar {
  width: 100%;
  height: 3px;
  background: #3a3a3c;
  border-radius: 2px;
  overflow: hidden;
  margin-top: 4px;
}

.progress-fill {
  height: 100%;
  background: #007aff;
  transition: width 0.3s;
}

.ai-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
  font-size: 10px;
}

.active-pdfs {
  padding: 16px;
  border-top: 1px solid #3a3a3c;
}

.active-pdfs h4 {
  margin: 0 0 12px 0;
  font-size: 12px;
  font-weight: 600;
  color: #ffffff;
}

.active-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.active-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  background: #2c2c2e;
  border-radius: 6px;
  font-size: 10px;
}

.active-item button {
  background: none;
  border: none;
  color: #ff3b30;
  cursor: pointer;
  font-size: 10px;
  padding: 2px;
}

.empty-state {
  text-align: center;
  color: #8e8e93;
  font-size: 10px;
  padding: 12px;
}

/* Center Panel */
.pdf-center-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #000000;
}

.document-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: linear-gradient(180deg, #1c1c1e 0%, #2c2c2e 100%);
  border-bottom: 1px solid #3a3a3c;
}

.document-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
}

.document-status {
  font-size: 11px;
  color: #8e8e93;
}

.document-tabs {
  display: flex;
  background: #1c1c1e;
  border-bottom: 1px solid #3a3a3c;
}

.document-tabs button {
  background: none;
  border: none;
  padding: 12px 20px;
  color: #8e8e93;
  cursor: pointer;
  font-size: 12px;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.document-tabs button:hover {
  color: #ffffff;
  background: #2c2c2e;
}

.document-tabs button.active {
  color: #007aff;
  border-bottom-color: #007aff;
}

.document-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.overview-tab .info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item label {
  font-size: 11px;
  font-weight: 600;
  color: #8e8e93;
}

.info-item span {
  font-size: 12px;
  color: #ffffff;
}

.content-tab .content-text {
  background: #2c2c2e;
  border: 1px solid #3a3a3c;
  border-radius: 8px;
  padding: 16px;
  min-height: 400px;
  font-family: 'Consolas', monospace;
  font-size: 11px;
  line-height: 1.5;
  white-space: pre-wrap;
  overflow-y: auto;
}

.analysis-tab .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-item {
  background: #2c2c2e;
  border: 1px solid #3a3a3c;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
}

.stat-item label {
  display: block;
  font-size: 10px;
  color: #8e8e93;
  margin-bottom: 4px;
}

.stat-item span {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}

.keywords-section, .summary-section {
  margin-bottom: 24px;
}

.keywords-section h4, .summary-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #ffffff;
}

.keywords-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.keyword-tag {
  background: #007aff;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
}

.summary-section p {
  background: #2c2c2e;
  border: 1px solid #3a3a3c;
  border-radius: 8px;
  padding: 16px;
  margin: 0;
  line-height: 1.5;
  font-size: 12px;
}

.no-document {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.no-doc-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-document h3 {
  margin: 0 0 8px 0;
  color: #ffffff;
}

.no-document p {
  margin: 0;
  color: #8e8e93;
  font-size: 12px;
}

/* Right Panel */
.pdf-right-panel {
  width: 350px;
  background: #1c1c1e;
  border-left: 1px solid #3a3a3c;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.ai-analysis-section h4,
.document-actions h4,
.processing-queue h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
}

.analysis-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 16px;
}

.analysis-buttons button {
  background: #2c2c2e;
  border: 1px solid #3a3a3c;
  border-radius: 8px;
  padding: 8px 12px;
  color: #ffffff;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s;
}

.analysis-buttons button:hover:not(:disabled) {
  background: #3a3a3c;
}

.analysis-buttons button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.ai-response textarea {
  width: 100%;
  height: 200px;
  background: #2c2c2e;
  border: 1px solid #3a3a3c;
  border-radius: 8px;
  padding: 12px;
  color: #ffffff;
  font-size: 11px;
  font-family: inherit;
  resize: vertical;
  outline: none;
}

.ai-response textarea:focus {
  border-color: #007aff;
}

.document-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.document-actions button {
  background: #007aff;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  color: white;
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.document-actions button:hover:not(:disabled) {
  background: #0056cc;
}

.document-actions button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.document-actions button.danger {
  background: #ff3b30;
}

.document-actions button.danger:hover:not(:disabled) {
  background: #cc2e24;
}

.processing-queue {
  background: #2c2c2e;
  border: 1px solid #3a3a3c;
  border-radius: 8px;
  padding: 12px;
}

.queue-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.queue-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 10px;
  color: #8e8e93;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #1c1c1e;
}

::-webkit-scrollbar-thumb {
  background: #3a3a3c;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #007aff;
}
