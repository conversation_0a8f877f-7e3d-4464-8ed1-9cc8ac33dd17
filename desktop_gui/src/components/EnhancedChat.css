.enhanced-chat {
  display: flex;
  height: 100vh;
  background: #000000;
  color: #ffffff;
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Conversations Sidebar */
.conversations-sidebar {
  width: 250px;
  background: #1c1c1e;
  border-right: 1px solid #3a3a3c;
  display: flex;
  flex-direction: column;
}

.conversations-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #3a3a3c;
}

.conversations-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
}

.new-chat-btn {
  background: #2c2c2e;
  border: 1px solid #3a3a3c;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  color: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.new-chat-btn:hover {
  background: #3a3a3c;
}

.conversations-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.conversation-item {
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  margin-bottom: 4px;
  transition: background-color 0.2s;
}

.conversation-item:hover {
  background: #2c2c2e;
}

.conversation-item.active {
  background: #007aff;
}

.conversation-title {
  font-weight: 600;
  font-size: 12px;
  margin-bottom: 4px;
}

.conversation-preview {
  font-size: 10px;
  color: #8e8e93;
  opacity: 0.8;
}

/* Main Chat Area */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #000000;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: linear-gradient(180deg, #1c1c1e 0%, #2c2c2e 100%);
  border-bottom: 1px solid #3a3a3c;
}

.chat-title h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
}

.chat-title p {
  margin: 2px 0 0 0;
  font-size: 11px;
  color: #8e8e93;
}

.chat-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.auto-tts-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ffffff;
  font-size: 12px;
  cursor: pointer;
}

.auto-tts-toggle input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #007aff;
}

.clear-chat-btn {
  background: #2c2c2e;
  border: 1px solid #3a3a3c;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  color: #8e8e93;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.clear-chat-btn:hover {
  background: #3a3a3c;
  color: #ffffff;
}

/* Messages Container */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px 0;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 0 20px;
}

.message {
  display: flex;
  align-items: flex-start;
}

.message.user {
  justify-content: flex-end;
}

.message.agent {
  justify-content: flex-start;
}

.message-bubble {
  max-width: 400px;
  padding: 12px 16px;
  border-radius: 18px;
  position: relative;
}

.message.user .message-bubble {
  background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);
  color: white;
}

.message.agent .message-bubble {
  background: #2c2c2e;
  border: 1px solid #3a3a3c;
  color: #ffffff;
}

.message.error .message-bubble {
  background: #ff3b30;
  color: white;
}

.message.system .message-bubble {
  background: #30d158;
  color: white;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  font-size: 9px;
}

.sender {
  font-weight: 600;
  color: #007aff;
}

.message.user .sender {
  color: rgba(255, 255, 255, 0.8);
}

.message-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.message-bubble:hover .message-actions {
  opacity: 1;
}

.message-actions button {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  font-size: 10px;
  padding: 2px;
  border-radius: 3px;
  opacity: 0.7;
}

.message-actions button:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
}

.timestamp {
  color: #8e8e93;
  font-size: 8px;
}

.message.user .timestamp {
  color: rgba(255, 255, 255, 0.8);
}

.message-content {
  line-height: 1.4;
}

.message-content p {
  margin: 0;
  word-wrap: break-word;
}

.message-content pre {
  background: rgba(0, 0, 0, 0.3);
  padding: 8px;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'Consolas', monospace;
  font-size: 11px;
  margin: 4px 0;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
  padding: 8px 0;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  background: #8e8e93;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

/* Input Container */
.input-container {
  padding: 16px 24px;
  background: #1c1c1e;
  border-top: 1px solid #3a3a3c;
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #2c2c2e;
  border: 2px solid #3a3a3c;
  border-radius: 24px;
  padding: 12px 20px;
  transition: border-color 0.2s;
}

.input-wrapper:focus-within {
  border-color: #007aff;
}

.message-input {
  flex: 1;
  background: none;
  border: none;
  color: #ffffff;
  font-size: 14px;
  outline: none;
}

.message-input::placeholder {
  color: #8e8e93;
}

.input-buttons {
  display: flex;
  gap: 8px;
}

.voice-btn, .send-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.2s;
}

.voice-btn {
  background: #2c2c2e;
  color: #8e8e93;
  border: 1px solid #3a3a3c;
}

.voice-btn:hover {
  background: #3a3a3c;
  color: #ffffff;
}

.voice-btn.recording {
  background: #ff3b30;
  color: white;
  animation: pulse 1s infinite;
}

.send-btn {
  background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);
  color: white;
}

.send-btn:hover {
  background: linear-gradient(135deg, #0056cc 0%, #4a44b8 100%);
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Controls Sidebar */
.controls-sidebar {
  width: 300px;
  background: #1c1c1e;
  border-left: 1px solid #3a3a3c;
  padding: 16px;
  overflow-y: auto;
}

.control-section {
  margin-bottom: 24px;
  padding: 16px;
  background: #2c2c2e;
  border-radius: 12px;
  border: 1px solid #3a3a3c;
}

.control-section h4 {
  margin: 0 0 12px 0;
  font-size: 12px;
  font-weight: 600;
  color: #ffffff;
}

.agent-selector {
  width: 100%;
  background: #1c1c1e;
  border: 1px solid #3a3a3c;
  border-radius: 8px;
  padding: 8px 12px;
  color: #ffffff;
  font-size: 11px;
  margin-bottom: 8px;
}

.agent-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 10px;
  color: #30d158;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #30d158;
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.control-section label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 11px;
  color: #ffffff;
  cursor: pointer;
}

.control-section input[type="checkbox"] {
  width: 14px;
  height: 14px;
  accent-color: #007aff;
}

.control-section button {
  width: 100%;
  background: #1c1c1e;
  border: 1px solid #3a3a3c;
  border-radius: 8px;
  padding: 8px 12px;
  color: #ffffff;
  font-size: 11px;
  cursor: pointer;
  margin-bottom: 6px;
  text-align: left;
  transition: background-color 0.2s;
}

.control-section button:hover {
  background: #3a3a3c;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #1c1c1e;
}

::-webkit-scrollbar-thumb {
  background: #3a3a3c;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #007aff;
}
