import React, { useState, useEffect, useRef } from 'react';
import './SubAgentNetwork.css';

interface SubAgent {
  id: string;
  name: string;
  type: 'research' | 'analysis' | 'communication' | 'security' | 'system' | 'custom';
  status: 'idle' | 'active' | 'busy' | 'error' | 'completed';
  currentTask: string;
  progress: number;
  createdAt: Date;
  lastActivity: Date;
  capabilities: string[];
  connections: string[]; // IDs of connected agents
  metrics: {
    tasksCompleted: number;
    successRate: number;
    avgResponseTime: number;
  };
  position?: { x: number; y: number };
}

interface NetworkConnection {
  from: string;
  to: string;
  type: 'data' | 'command' | 'collaboration';
  active: boolean;
  strength: number;
}

const SubAgentNetwork: React.FC = () => {
  const [agents, setAgents] = useState<SubAgent[]>([]);
  const [connections, setConnections] = useState<NetworkConnection[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<SubAgent | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'network' | 'hierarchy'>('network');
  const [isCreatingAgent, setIsCreatingAgent] = useState(false);
  const networkRef = useRef<HTMLDivElement>(null);

  // Initialize with some sample agents
  useEffect(() => {
    const sampleAgents: SubAgent[] = [
      {
        id: 'main-ai',
        name: 'Main AI Assistant',
        type: 'system',
        status: 'active',
        currentTask: 'Coordinating sub-agents',
        progress: 100,
        createdAt: new Date(Date.now() - 86400000),
        lastActivity: new Date(),
        capabilities: ['coordination', 'task-delegation', 'decision-making'],
        connections: ['research-01', 'analysis-01', 'comm-01'],
        metrics: { tasksCompleted: 156, successRate: 98.5, avgResponseTime: 1.2 },
        position: { x: 400, y: 300 }
      },
      {
        id: 'research-01',
        name: 'Research Specialist',
        type: 'research',
        status: 'busy',
        currentTask: 'Analyzing PDF documents',
        progress: 67,
        createdAt: new Date(Date.now() - 3600000),
        lastActivity: new Date(Date.now() - 30000),
        capabilities: ['document-analysis', 'web-research', 'data-extraction'],
        connections: ['main-ai', 'analysis-01'],
        metrics: { tasksCompleted: 23, successRate: 95.7, avgResponseTime: 3.4 },
        position: { x: 200, y: 150 }
      },
      {
        id: 'analysis-01',
        name: 'Data Analyst',
        type: 'analysis',
        status: 'active',
        currentTask: 'Processing research findings',
        progress: 45,
        createdAt: new Date(Date.now() - 7200000),
        lastActivity: new Date(Date.now() - 15000),
        capabilities: ['statistical-analysis', 'pattern-recognition', 'visualization'],
        connections: ['main-ai', 'research-01'],
        metrics: { tasksCompleted: 34, successRate: 97.1, avgResponseTime: 2.8 },
        position: { x: 600, y: 150 }
      },
      {
        id: 'comm-01',
        name: 'Communication Handler',
        type: 'communication',
        status: 'idle',
        currentTask: 'Monitoring chat channels',
        progress: 0,
        createdAt: new Date(Date.now() - 1800000),
        lastActivity: new Date(Date.now() - 120000),
        capabilities: ['natural-language', 'translation', 'sentiment-analysis'],
        connections: ['main-ai'],
        metrics: { tasksCompleted: 89, successRate: 99.2, avgResponseTime: 0.8 },
        position: { x: 400, y: 450 }
      },
      {
        id: 'security-01',
        name: 'Security Monitor',
        type: 'security',
        status: 'active',
        currentTask: 'System health check',
        progress: 80,
        createdAt: new Date(Date.now() - 5400000),
        lastActivity: new Date(Date.now() - 5000),
        capabilities: ['threat-detection', 'access-control', 'audit-logging'],
        connections: ['main-ai'],
        metrics: { tasksCompleted: 12, successRate: 100, avgResponseTime: 1.5 },
        position: { x: 200, y: 450 }
      }
    ];

    setAgents(sampleAgents);

    // Create connections
    const sampleConnections: NetworkConnection[] = [
      { from: 'main-ai', to: 'research-01', type: 'command', active: true, strength: 0.9 },
      { from: 'main-ai', to: 'analysis-01', type: 'command', active: true, strength: 0.8 },
      { from: 'main-ai', to: 'comm-01', type: 'command', active: false, strength: 0.6 },
      { from: 'main-ai', to: 'security-01', type: 'command', active: true, strength: 0.7 },
      { from: 'research-01', to: 'analysis-01', type: 'data', active: true, strength: 0.95 }
    ];

    setConnections(sampleConnections);
  }, []);

  const getAgentIcon = (type: SubAgent['type']) => {
    const icons = {
      research: '🔍',
      analysis: '📊',
      communication: '💬',
      security: '🛡️',
      system: '⚙️',
      custom: '🤖'
    };
    return icons[type];
  };

  const getStatusColor = (status: SubAgent['status']) => {
    const colors = {
      idle: '#8e8e93',
      active: '#30d158',
      busy: '#ff9500',
      error: '#ff3b30',
      completed: '#64d2ff'
    };
    return colors[status];
  };

  const getStatusText = (status: SubAgent['status']) => {
    const texts = {
      idle: 'Idle',
      active: 'Active',
      busy: 'Busy',
      error: 'Error',
      completed: 'Completed'
    };
    return texts[status];
  };

  const createNewAgent = () => {
    const newAgent: SubAgent = {
      id: `agent-${Date.now()}`,
      name: `New Agent ${agents.length + 1}`,
      type: 'custom',
      status: 'idle',
      currentTask: 'Initializing...',
      progress: 0,
      createdAt: new Date(),
      lastActivity: new Date(),
      capabilities: ['general-purpose'],
      connections: ['main-ai'],
      metrics: { tasksCompleted: 0, successRate: 0, avgResponseTime: 0 },
      position: { x: Math.random() * 600 + 100, y: Math.random() * 400 + 100 }
    };

    setAgents(prev => [...prev, newAgent]);
    setIsCreatingAgent(false);
  };

  const removeAgent = (agentId: string) => {
    if (agentId === 'main-ai') return; // Can't remove main AI
    
    setAgents(prev => prev.filter(agent => agent.id !== agentId));
    setConnections(prev => prev.filter(conn => conn.from !== agentId && conn.to !== agentId));
    
    if (selectedAgent?.id === agentId) {
      setSelectedAgent(null);
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  const renderNetworkView = () => (
    <div className="network-view" ref={networkRef}>
      <svg className="network-svg" width="100%" height="100%">
        {/* Render connections */}
        {connections.map((conn, index) => {
          const fromAgent = agents.find(a => a.id === conn.from);
          const toAgent = agents.find(a => a.id === conn.to);
          
          if (!fromAgent?.position || !toAgent?.position) return null;
          
          return (
            <g key={index}>
              <line
                x1={fromAgent.position.x + 50}
                y1={fromAgent.position.y + 50}
                x2={toAgent.position.x + 50}
                y2={toAgent.position.y + 50}
                stroke={conn.active ? '#007aff' : '#3a3a3c'}
                strokeWidth={conn.strength * 3}
                strokeDasharray={conn.type === 'data' ? '5,5' : 'none'}
                className={conn.active ? 'connection-active' : 'connection-inactive'}
              />
              {conn.active && (
                <circle
                  r="3"
                  fill="#007aff"
                  className="connection-pulse"
                >
                  <animateMotion
                    dur="2s"
                    repeatCount="indefinite"
                    path={`M${fromAgent.position.x + 50},${fromAgent.position.y + 50} L${toAgent.position.x + 50},${toAgent.position.y + 50}`}
                  />
                </circle>
              )}
            </g>
          );
        })}
      </svg>
      
      {/* Render agents */}
      {agents.map(agent => (
        <div
          key={agent.id}
          className={`agent-node ${selectedAgent?.id === agent.id ? 'selected' : ''}`}
          style={{
            left: agent.position?.x || 0,
            top: agent.position?.y || 0,
            borderColor: getStatusColor(agent.status)
          }}
          onClick={() => setSelectedAgent(agent)}
        >
          <div className="agent-icon">{getAgentIcon(agent.type)}</div>
          <div className="agent-info">
            <div className="agent-name">{agent.name}</div>
            <div className="agent-status" style={{ color: getStatusColor(agent.status) }}>
              {getStatusText(agent.status)}
            </div>
            {agent.status === 'busy' && (
              <div className="agent-progress">
                <div className="progress-bar">
                  <div 
                    className="progress-fill" 
                    style={{ width: `${agent.progress}%` }}
                  ></div>
                </div>
                <span>{agent.progress}%</span>
              </div>
            )}
          </div>
          <div className="agent-pulse" style={{ borderColor: getStatusColor(agent.status) }}></div>
        </div>
      ))}
    </div>
  );

  const renderGridView = () => (
    <div className="grid-view">
      {agents.map(agent => (
        <div
          key={agent.id}
          className={`agent-card ${selectedAgent?.id === agent.id ? 'selected' : ''}`}
          onClick={() => setSelectedAgent(agent)}
        >
          <div className="card-header">
            <div className="agent-icon-large">{getAgentIcon(agent.type)}</div>
            <div className="agent-details">
              <h3>{agent.name}</h3>
              <div className="agent-type">{agent.type}</div>
            </div>
            <div className="status-indicator" style={{ backgroundColor: getStatusColor(agent.status) }}></div>
          </div>
          
          <div className="card-body">
            <div className="current-task">
              <strong>Current Task:</strong>
              <span>{agent.currentTask}</span>
            </div>
            
            {agent.status === 'busy' && (
              <div className="task-progress">
                <div className="progress-bar">
                  <div 
                    className="progress-fill" 
                    style={{ width: `${agent.progress}%` }}
                  ></div>
                </div>
                <span>{agent.progress}%</span>
              </div>
            )}
            
            <div className="agent-metrics">
              <div className="metric">
                <span className="metric-label">Tasks:</span>
                <span className="metric-value">{agent.metrics.tasksCompleted}</span>
              </div>
              <div className="metric">
                <span className="metric-label">Success:</span>
                <span className="metric-value">{agent.metrics.successRate}%</span>
              </div>
              <div className="metric">
                <span className="metric-label">Avg Time:</span>
                <span className="metric-value">{agent.metrics.avgResponseTime}s</span>
              </div>
            </div>
            
            <div className="last-activity">
              Last active: {formatTimeAgo(agent.lastActivity)}
            </div>
          </div>
        </div>
      ))}
      
      <div className="agent-card create-new" onClick={() => setIsCreatingAgent(true)}>
        <div className="create-icon">+</div>
        <div className="create-text">Create New Agent</div>
      </div>
    </div>
  );

  return (
    <div className="sub-agent-network">
      <div className="network-header">
        <div className="header-left">
          <h2>🤖 Sub-Agent Network</h2>
          <div className="agent-count">{agents.length} agents active</div>
        </div>
        
        <div className="header-controls">
          <div className="view-mode-selector">
            <button 
              className={viewMode === 'network' ? 'active' : ''}
              onClick={() => setViewMode('network')}
            >
              🕸️ Network
            </button>
            <button 
              className={viewMode === 'grid' ? 'active' : ''}
              onClick={() => setViewMode('grid')}
            >
              ⊞ Grid
            </button>
            <button 
              className={viewMode === 'hierarchy' ? 'active' : ''}
              onClick={() => setViewMode('hierarchy')}
            >
              🌳 Hierarchy
            </button>
          </div>
          
          <button className="create-agent-btn" onClick={() => setIsCreatingAgent(true)}>
            + Create Agent
          </button>
        </div>
      </div>

      <div className="network-content">
        <div className="network-main">
          {viewMode === 'network' && renderNetworkView()}
          {viewMode === 'grid' && renderGridView()}
          {viewMode === 'hierarchy' && renderGridView()} {/* Placeholder */}
        </div>

        {selectedAgent && (
          <div className="agent-details-panel">
            <div className="panel-header">
              <div className="agent-title">
                <div className="agent-icon-large">{getAgentIcon(selectedAgent.type)}</div>
                <div>
                  <h3>{selectedAgent.name}</h3>
                  <div className="agent-id">ID: {selectedAgent.id}</div>
                </div>
              </div>
              <button 
                className="close-panel"
                onClick={() => setSelectedAgent(null)}
              >
                ✕
              </button>
            </div>

            <div className="panel-content">
              <div className="detail-section">
                <h4>Status</h4>
                <div className="status-info">
                  <div className="status-badge" style={{ backgroundColor: getStatusColor(selectedAgent.status) }}>
                    {getStatusText(selectedAgent.status)}
                  </div>
                  <div className="current-task">{selectedAgent.currentTask}</div>
                  {selectedAgent.status === 'busy' && (
                    <div className="progress-info">
                      <div className="progress-bar">
                        <div 
                          className="progress-fill" 
                          style={{ width: `${selectedAgent.progress}%` }}
                        ></div>
                      </div>
                      <span>{selectedAgent.progress}%</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="detail-section">
                <h4>Capabilities</h4>
                <div className="capabilities-list">
                  {selectedAgent.capabilities.map((capability, index) => (
                    <span key={index} className="capability-tag">
                      {capability}
                    </span>
                  ))}
                </div>
              </div>

              <div className="detail-section">
                <h4>Performance Metrics</h4>
                <div className="metrics-grid">
                  <div className="metric-item">
                    <div className="metric-label">Tasks Completed</div>
                    <div className="metric-value">{selectedAgent.metrics.tasksCompleted}</div>
                  </div>
                  <div className="metric-item">
                    <div className="metric-label">Success Rate</div>
                    <div className="metric-value">{selectedAgent.metrics.successRate}%</div>
                  </div>
                  <div className="metric-item">
                    <div className="metric-label">Avg Response Time</div>
                    <div className="metric-value">{selectedAgent.metrics.avgResponseTime}s</div>
                  </div>
                </div>
              </div>

              <div className="detail-section">
                <h4>Timeline</h4>
                <div className="timeline-info">
                  <div className="timeline-item">
                    <span>Created:</span>
                    <span>{selectedAgent.createdAt.toLocaleString()}</span>
                  </div>
                  <div className="timeline-item">
                    <span>Last Activity:</span>
                    <span>{formatTimeAgo(selectedAgent.lastActivity)}</span>
                  </div>
                </div>
              </div>

              <div className="detail-section">
                <h4>Actions</h4>
                <div className="action-buttons">
                  <button className="action-btn primary">Assign Task</button>
                  <button className="action-btn secondary">View Logs</button>
                  <button className="action-btn secondary">Configure</button>
                  {selectedAgent.id !== 'main-ai' && (
                    <button 
                      className="action-btn danger"
                      onClick={() => removeAgent(selectedAgent.id)}
                    >
                      Remove Agent
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {isCreatingAgent && (
        <div className="create-agent-modal">
          <div className="modal-content">
            <h3>Create New Agent</h3>
            <p>A new sub-agent will be created and added to the network.</p>
            <div className="modal-actions">
              <button onClick={createNewAgent} className="btn-primary">Create</button>
              <button onClick={() => setIsCreatingAgent(false)} className="btn-secondary">Cancel</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubAgentNetwork;
