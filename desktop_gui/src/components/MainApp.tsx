import React, { useState, useEffect } from 'react';
import <PERSON>hanced<PERSON>hat from './EnhancedChat';
import PDFProcessor from './PDFProcessor';
import SubAgentNetwork from './SubAgentNetwork';
import SystemAnalytics from './SystemAnalytics';
import './MainApp.css';

interface NavigationItem {
  id: string;
  name: string;
  icon: string;
  component: React.ComponentType;
  badge?: number;
}

const MainApp: React.FC = () => {
  const [activeView, setActiveView] = useState('chat');
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [notifications, setNotifications] = useState<string[]>([]);
  const [systemStatus, setSystemStatus] = useState({
    online: true,
    agents: 4,
    pdfs: 0,
    uptime: '2h 34m'
  });

  const navigationItems: NavigationItem[] = [
    {
      id: 'chat',
      name: 'AI Chat',
      icon: '💬',
      component: EnhancedChat
    },
    {
      id: 'pdf',
      name: 'PDF Processor',
      icon: '📄',
      component: PDFProcessor,
      badge: systemStatus.pdfs
    },
    {
      id: 'agents',
      name: 'Sub-Agents',
      icon: '🤖',
      component: SubAgentNetwork,
      badge: systemStatus.agents
    },
    {
      id: 'analytics',
      name: 'Analytics',
      icon: '📊',
      component: SystemAnalytics
    }
  ];

  // Simulate system updates
  useEffect(() => {
    const interval = setInterval(() => {
      setSystemStatus(prev => ({
        ...prev,
        uptime: updateUptime(prev.uptime)
      }));
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, []);

  const updateUptime = (currentUptime: string): string => {
    const [hours, minutes] = currentUptime.split('h ').map(part => 
      parseInt(part.replace('m', ''))
    );
    
    let newMinutes = minutes + 1;
    let newHours = hours;
    
    if (newMinutes >= 60) {
      newMinutes = 0;
      newHours += 1;
    }
    
    return `${newHours}h ${newMinutes}m`;
  };

  const addNotification = (message: string) => {
    setNotifications(prev => [message, ...prev.slice(0, 4)]);
    
    // Auto-remove notification after 5 seconds
    setTimeout(() => {
      setNotifications(prev => prev.filter(notif => notif !== message));
    }, 5000);
  };

  const getCurrentComponent = () => {
    const item = navigationItems.find(item => item.id === activeView);
    if (item) {
      const Component = item.component;
      return <Component />;
    }
    return <EnhancedChat />;
  };

  const handleNavigation = (viewId: string) => {
    setActiveView(viewId);
    
    // Add navigation notification
    const item = navigationItems.find(item => item.id === viewId);
    if (item) {
      addNotification(`Switched to ${item.name}`);
    }
  };

  return (
    <div className="main-app">
      {/* Floating Particles Background */}
      <div className="floating-particles">
        {Array.from({ length: 20 }, (_, i) => (
          <div
            key={i}
            className="particle"
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 20}s`,
              animationDuration: `${20 + Math.random() * 10}s`
            }}
          />
        ))}
      </div>

      {/* Sidebar Navigation */}
      <div className={`sidebar ${isCollapsed ? 'collapsed' : ''}`}>
        <div className="sidebar-header">
          <div className="logo">
            <div className="logo-icon">🤖</div>
            {!isCollapsed && (
              <div className="logo-text">
                <div className="logo-title">AI Desktop</div>
                <div className="logo-subtitle">Assistant</div>
              </div>
            )}
          </div>
          
          <button 
            className="collapse-btn"
            onClick={() => setIsCollapsed(!isCollapsed)}
          >
            {isCollapsed ? '→' : '←'}
          </button>
        </div>

        <nav className="navigation">
          {navigationItems.map(item => (
            <button
              key={item.id}
              className={`nav-item ${activeView === item.id ? 'active' : ''}`}
              onClick={() => handleNavigation(item.id)}
              title={isCollapsed ? item.name : undefined}
            >
              <div className="nav-icon">{item.icon}</div>
              {!isCollapsed && (
                <>
                  <span className="nav-label">{item.name}</span>
                  {item.badge !== undefined && item.badge > 0 && (
                    <span className="nav-badge">{item.badge}</span>
                  )}
                </>
              )}
            </button>
          ))}
        </nav>

        <div className="sidebar-footer">
          <div className="system-status">
            <div className="status-indicator">
              <div className={`status-dot ${systemStatus.online ? 'online' : 'offline'}`}></div>
              {!isCollapsed && (
                <div className="status-info">
                  <div className="status-text">
                    {systemStatus.online ? 'Online' : 'Offline'}
                  </div>
                  <div className="uptime">Uptime: {systemStatus.uptime}</div>
                </div>
              )}
            </div>
          </div>

          {!isCollapsed && (
            <div className="quick-stats">
              <div className="stat-item">
                <span className="stat-icon">🤖</span>
                <span className="stat-value">{systemStatus.agents}</span>
                <span className="stat-label">Agents</span>
              </div>
              <div className="stat-item">
                <span className="stat-icon">📄</span>
                <span className="stat-value">{systemStatus.pdfs}</span>
                <span className="stat-label">PDFs</span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main Content Area */}
      <div className="main-content">
        <div className="content-header">
          <div className="header-left">
            <h1 className="page-title">
              {navigationItems.find(item => item.id === activeView)?.name || 'AI Desktop Assistant'}
            </h1>
            <div className="breadcrumb">
              <span>Home</span>
              <span className="breadcrumb-separator">›</span>
              <span>{navigationItems.find(item => item.id === activeView)?.name}</span>
            </div>
          </div>

          <div className="header-right">
            <div className="header-actions">
              <button className="action-btn" title="Settings">
                ⚙️
              </button>
              <button className="action-btn" title="Help">
                ❓
              </button>
              <button className="action-btn" title="Notifications">
                🔔
                {notifications.length > 0 && (
                  <span className="notification-badge">{notifications.length}</span>
                )}
              </button>
            </div>
          </div>
        </div>

        <div className="content-body">
          {getCurrentComponent()}
        </div>
      </div>

      {/* Notifications Panel */}
      {notifications.length > 0 && (
        <div className="notifications-panel">
          <div className="notifications-header">
            <h3>Notifications</h3>
            <button 
              className="clear-notifications"
              onClick={() => setNotifications([])}
            >
              Clear All
            </button>
          </div>
          <div className="notifications-list">
            {notifications.map((notification, index) => (
              <div key={index} className="notification-item">
                <div className="notification-icon">ℹ️</div>
                <div className="notification-text">{notification}</div>
                <button 
                  className="notification-close"
                  onClick={() => setNotifications(prev => 
                    prev.filter((_, i) => i !== index)
                  )}
                >
                  ✕
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Global Keyboard Shortcuts */}
      <div className="keyboard-shortcuts" style={{ display: 'none' }}>
        <div className="shortcut">Ctrl + 1: Chat</div>
        <div className="shortcut">Ctrl + 2: PDF Processor</div>
        <div className="shortcut">Ctrl + 3: Sub-Agents</div>
        <div className="shortcut">Ctrl + 4: Analytics</div>
        <div className="shortcut">Ctrl + /: Toggle Sidebar</div>
      </div>
    </div>
  );
};

// Add keyboard shortcuts
document.addEventListener('keydown', (e) => {
  if (e.ctrlKey) {
    switch (e.key) {
      case '1':
        e.preventDefault();
        // Trigger chat navigation
        break;
      case '2':
        e.preventDefault();
        // Trigger PDF navigation
        break;
      case '3':
        e.preventDefault();
        // Trigger agents navigation
        break;
      case '4':
        e.preventDefault();
        // Trigger analytics navigation
        break;
      case '/':
        e.preventDefault();
        // Toggle sidebar
        break;
    }
  }
});

export default MainApp;
