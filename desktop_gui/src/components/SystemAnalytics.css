.system-analytics {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #000000;
  color: #ffffff;
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
  overflow: hidden;
}

/* Header */
.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: linear-gradient(180deg, #1c1c1e 0%, #2c2c2e 100%);
  border-bottom: 1px solid #3a3a3c;
  backdrop-filter: blur(20px);
}

.header-left h2 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
}

.monitoring-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #8e8e93;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #8e8e93;
}

.status-indicator.active {
  background: #30d158;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.monitor-btn, .export-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 8px 16px;
  color: #ffffff;
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.monitor-btn.active {
  background: #30d158;
  border-color: #30d158;
}

.monitor-btn:hover, .export-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Tabs */
.analytics-tabs {
  display: flex;
  background: #1c1c1e;
  border-bottom: 1px solid #3a3a3c;
}

.analytics-tabs button {
  background: transparent;
  border: none;
  padding: 12px 20px;
  color: #8e8e93;
  cursor: pointer;
  font-size: 12px;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.analytics-tabs button:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.05);
}

.analytics-tabs button.active {
  color: #007aff;
  border-bottom-color: #007aff;
}

/* Content */
.analytics-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

/* Overview Tab */
.overview-tab {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.metric-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 20px;
  transition: all 0.3s;
}

.metric-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.metric-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
}

.metric-value {
  font-size: 18px;
  font-weight: 700;
}

.metric-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.stat-info {
  flex: 1;
}

.stat-label {
  font-size: 11px;
  color: #8e8e93;
  margin-bottom: 2px;
}

.stat-value {
  font-size: 16px;
  font-weight: 700;
  color: #ffffff;
}

/* Security Tab */
.security-tab {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.security-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.security-header h3 {
  margin: 0;
  font-size: 18px;
  color: #ffffff;
}

.security-stats {
  display: flex;
  gap: 16px;
}

.security-stat {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.security-stat.critical {
  background: rgba(255, 59, 48, 0.2);
  color: #ff3b30;
}

.security-stat.warning {
  background: rgba(255, 149, 0, 0.2);
  color: #ff9500;
}

.security-stat.info {
  background: rgba(0, 122, 255, 0.2);
  color: #007aff;
}

.security-events {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 500px;
  overflow-y: auto;
}

.security-event {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s;
}

.security-event.resolved {
  opacity: 0.6;
  background: rgba(255, 255, 255, 0.02);
}

.security-event.critical {
  border-left: 4px solid #ff3b30;
}

.security-event.warning {
  border-left: 4px solid #ff9500;
}

.security-event.info {
  border-left: 4px solid #007aff;
}

.event-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.event-type-icon {
  font-size: 16px;
}

.event-info {
  flex: 1;
}

.event-source {
  font-size: 12px;
  font-weight: 600;
  color: #ffffff;
}

.event-time {
  font-size: 10px;
  color: #8e8e93;
}

.resolve-btn {
  background: #30d158;
  border: none;
  border-radius: 6px;
  padding: 4px 8px;
  color: white;
  font-size: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.resolve-btn:hover {
  background: #28a745;
}

.event-message {
  font-size: 12px;
  color: #ffffff;
  line-height: 1.4;
}

/* Analytics Tab */
.analytics-tab {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.analytics-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 20px;
}

.analytics-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #ffffff;
}

.usage-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.usage-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.usage-label {
  font-size: 12px;
  color: #8e8e93;
}

.usage-value {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
}

.feature-usage {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.feature-name {
  width: 150px;
  font-size: 12px;
  color: #ffffff;
}

.feature-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.feature-fill {
  height: 100%;
  background: linear-gradient(90deg, #007aff, #5856d6);
  border-radius: 4px;
  transition: width 0.5s ease;
}

.feature-percentage {
  width: 50px;
  text-align: right;
  font-size: 11px;
  color: #8e8e93;
}

.error-frequency {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.error-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 59, 48, 0.1);
  border: 1px solid rgba(255, 59, 48, 0.2);
  border-radius: 8px;
}

.error-name {
  font-size: 12px;
  color: #ffffff;
}

.error-count {
  font-size: 12px;
  font-weight: 600;
  color: #ff3b30;
  background: rgba(255, 59, 48, 0.2);
  padding: 2px 6px;
  border-radius: 4px;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .analytics-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-controls {
    width: 100%;
    justify-content: flex-end;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .usage-stats {
    grid-template-columns: 1fr;
  }

  .feature-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .feature-name {
    width: 100%;
  }

  .feature-percentage {
    width: 100%;
    text-align: left;
  }
}
