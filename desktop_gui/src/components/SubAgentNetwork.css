.sub-agent-network {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #000000;
  color: #ffffff;
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
  overflow: hidden;
}

/* Header */
.network-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: linear-gradient(180deg, #1c1c1e 0%, #2c2c2e 100%);
  border-bottom: 1px solid #3a3a3c;
  backdrop-filter: blur(20px);
}

.header-left h2 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
}

.agent-count {
  font-size: 12px;
  color: #8e8e93;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.view-mode-selector {
  display: flex;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.view-mode-selector button {
  background: transparent;
  border: none;
  padding: 8px 16px;
  color: #8e8e93;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s;
}

.view-mode-selector button:hover {
  background: rgba(255, 255, 255, 0.08);
  color: #ffffff;
}

.view-mode-selector button.active {
  background: #007aff;
  color: white;
}

.create-agent-btn {
  background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  color: white;
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.create-agent-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
}

/* Network Content */
.network-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.network-main {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* Network View */
.network-view {
  position: relative;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 50%, rgba(0, 122, 255, 0.05) 0%, transparent 70%);
}

.network-svg {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.connection-active {
  animation: connectionPulse 2s infinite;
}

.connection-pulse {
  filter: drop-shadow(0 0 4px #007aff);
}

@keyframes connectionPulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

.agent-node {
  position: absolute;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 2px solid #3a3a3c;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  z-index: 2;
  padding: 8px;
}

.agent-node:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: scale(1.05);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.agent-node.selected {
  border-color: #007aff;
  box-shadow: 0 0 20px rgba(0, 122, 255, 0.5);
}

.agent-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.agent-info {
  text-align: center;
  width: 100%;
}

.agent-name {
  font-size: 9px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.agent-status {
  font-size: 8px;
  font-weight: 500;
  margin-bottom: 4px;
}

.agent-progress {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 7px;
}

.agent-progress .progress-bar {
  flex: 1;
  height: 3px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.agent-progress .progress-fill {
  height: 100%;
  background: #007aff;
  transition: width 0.3s;
}

.agent-pulse {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid transparent;
  border-radius: 20px;
  animation: agentPulse 2s infinite;
}

@keyframes agentPulse {
  0%, 100% { 
    transform: scale(1);
    opacity: 0.5;
  }
  50% { 
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* Grid View */
.grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  padding: 24px;
  overflow-y: auto;
  height: 100%;
}

.agent-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
  height: fit-content;
}

.agent-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.agent-card.selected {
  border-color: #007aff;
  box-shadow: 0 0 20px rgba(0, 122, 255, 0.3);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.agent-icon-large {
  font-size: 32px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
}

.agent-details h3 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
}

.agent-type {
  font-size: 11px;
  color: #8e8e93;
  text-transform: capitalize;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-left: auto;
  animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.card-body {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.current-task {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.current-task strong {
  font-size: 11px;
  color: #8e8e93;
}

.current-task span {
  font-size: 12px;
  color: #ffffff;
}

.task-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 10px;
}

.task-progress .progress-bar {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
}

.task-progress .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007aff, #5856d6);
  transition: width 0.3s;
}

.agent-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.metric-label {
  font-size: 9px;
  color: #8e8e93;
  margin-bottom: 2px;
}

.metric-value {
  font-size: 12px;
  font-weight: 600;
  color: #ffffff;
}

.last-activity {
  font-size: 10px;
  color: #8e8e93;
  text-align: center;
}

.create-new {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  border: 2px dashed rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.02);
}

.create-new:hover {
  border-color: #007aff;
  background: rgba(0, 122, 255, 0.05);
}

.create-icon {
  font-size: 48px;
  color: #8e8e93;
  margin-bottom: 8px;
}

.create-text {
  font-size: 14px;
  color: #8e8e93;
  font-weight: 500;
}

/* Agent Details Panel */
.agent-details-panel {
  width: 350px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.agent-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.agent-title h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #ffffff;
}

.agent-id {
  font-size: 10px;
  color: #8e8e93;
  font-family: 'Consolas', monospace;
}

.close-panel {
  background: none;
  border: none;
  color: #8e8e93;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.close-panel:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-section h4 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #8e8e93;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  color: white;
  width: fit-content;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 11px;
}

.progress-info .progress-bar {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
}

.capabilities-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.capability-tag {
  background: rgba(0, 122, 255, 0.2);
  color: #64d2ff;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.metric-item .metric-label {
  font-size: 11px;
  color: #8e8e93;
}

.metric-item .metric-value {
  font-size: 12px;
  font-weight: 600;
  color: #ffffff;
}

.timeline-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.timeline-item {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
}

.timeline-item span:first-child {
  color: #8e8e93;
}

.timeline-item span:last-child {
  color: #ffffff;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn.primary {
  background: #007aff;
  color: white;
}

.action-btn.primary:hover {
  background: #0056cc;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.15);
}

.action-btn.danger {
  background: #ff3b30;
  color: white;
}

.action-btn.danger:hover {
  background: #cc2e24;
}

/* Create Agent Modal */
.create-agent-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 24px;
  max-width: 400px;
  text-align: center;
}

.modal-content h3 {
  margin: 0 0 12px 0;
  color: #ffffff;
}

.modal-content p {
  margin: 0 0 20px 0;
  color: #8e8e93;
  font-size: 14px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.btn-primary {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-weight: 600;
  cursor: pointer;
}

.btn-secondary {
  background: transparent;
  color: #8e8e93;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 8px 16px;
  cursor: pointer;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}
