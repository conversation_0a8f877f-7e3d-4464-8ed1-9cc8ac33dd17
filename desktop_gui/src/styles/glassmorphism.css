/* Glassmorphism Design System for AI Desktop Application */

:root {
  /* Color Palette */
  --primary-bg: #000000;
  --secondary-bg: #0a0a0a;
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-bg-hover: rgba(255, 255, 255, 0.08);
  --glass-bg-active: rgba(255, 255, 255, 0.12);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-border-hover: rgba(255, 255, 255, 0.2);
  
  /* Accent Colors */
  --accent-blue: #007aff;
  --accent-blue-hover: #0056cc;
  --accent-purple: #5856d6;
  --accent-green: #30d158;
  --accent-orange: #ff9500;
  --accent-red: #ff3b30;
  --accent-cyan: #64d2ff;
  --accent-pink: #ff2d92;
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--accent-blue) 0%, var(--accent-purple) 100%);
  --gradient-success: linear-gradient(135deg, var(--accent-green) 0%, var(--accent-cyan) 100%);
  --gradient-warning: linear-gradient(135deg, var(--accent-orange) 0%, var(--accent-red) 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  
  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #8e8e93;
  --text-tertiary: #636366;
  --text-accent: var(--accent-blue);
  
  /* Shadows */
  --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.3);
  --shadow-glass-hover: 0 12px 40px rgba(0, 0, 0, 0.4);
  --shadow-glow: 0 0 20px rgba(0, 122, 255, 0.3);
  --shadow-glow-strong: 0 0 30px rgba(0, 122, 255, 0.5);
  
  /* Border Radius */
  --radius-sm: 6px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  --radius-full: 50%;
  
  /* Spacing */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-2xl: 48px;
  
  /* Transitions */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.25s ease-out;
  --transition-slow: 0.35s ease-out;
  
  /* Blur Effects */
  --blur-sm: blur(8px);
  --blur-md: blur(16px);
  --blur-lg: blur(24px);
  --blur-xl: blur(32px);
}

/* Base Glass Components */
.glass {
  background: var(--glass-bg);
  backdrop-filter: var(--blur-md);
  -webkit-backdrop-filter: var(--blur-md);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-glass);
  transition: all var(--transition-normal);
}

.glass:hover {
  background: var(--glass-bg-hover);
  border-color: var(--glass-border-hover);
  box-shadow: var(--shadow-glass-hover);
  transform: translateY(-2px);
}

.glass-strong {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: var(--blur-lg);
  -webkit-backdrop-filter: var(--blur-lg);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.glass-subtle {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: var(--blur-sm);
  -webkit-backdrop-filter: var(--blur-sm);
  border: 1px solid rgba(255, 255, 255, 0.08);
}

/* Animated Glass Cards */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: var(--blur-md);
  -webkit-backdrop-filter: var(--blur-md);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.glass-card:hover::before {
  opacity: 1;
}

.glass-card:hover {
  background: var(--glass-bg-hover);
  border-color: var(--glass-border-hover);
  box-shadow: var(--shadow-glass-hover);
  transform: translateY(-4px);
}

/* Floating Particles Background */
.floating-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: var(--accent-blue);
  border-radius: 50%;
  opacity: 0;
  animation: float 20s infinite linear;
}

.particle:nth-child(2n) {
  background: var(--accent-purple);
  animation-duration: 25s;
}

.particle:nth-child(3n) {
  background: var(--accent-cyan);
  animation-duration: 30s;
}

@keyframes float {
  0% {
    transform: translateY(100vh) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 0.8;
  }
  90% {
    opacity: 0.8;
  }
  100% {
    transform: translateY(-100vh) translateX(100px);
    opacity: 0;
  }
}

/* Gradient Buttons */
.btn-gradient {
  background: var(--gradient-primary);
  border: none;
  border-radius: var(--radius-md);
  color: white;
  font-weight: 600;
  padding: var(--space-sm) var(--space-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-gradient:hover::before {
  left: 100%;
}

.btn-gradient:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow);
}

.btn-gradient:active {
  transform: translateY(0);
}

/* Animated Borders */
.animated-border {
  position: relative;
  background: var(--glass-bg);
  border-radius: var(--radius-md);
  padding: 2px;
}

.animated-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: var(--gradient-primary);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  animation: borderRotate 3s linear infinite;
}

@keyframes borderRotate {
  0% {
    background: var(--gradient-primary);
  }
  25% {
    background: var(--gradient-success);
  }
  50% {
    background: var(--gradient-warning);
  }
  75% {
    background: linear-gradient(135deg, var(--accent-pink) 0%, var(--accent-purple) 100%);
  }
  100% {
    background: var(--gradient-primary);
  }
}

/* Pulsing Elements */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Shimmer Effect */
.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Glow Effects */
.glow {
  box-shadow: var(--shadow-glow);
  transition: box-shadow var(--transition-normal);
}

.glow:hover {
  box-shadow: var(--shadow-glow-strong);
}

.glow-blue {
  box-shadow: 0 0 20px rgba(0, 122, 255, 0.3);
}

.glow-green {
  box-shadow: 0 0 20px rgba(48, 209, 88, 0.3);
}

.glow-purple {
  box-shadow: 0 0 20px rgba(88, 86, 214, 0.3);
}

/* Morphing Shapes */
.morph-circle {
  border-radius: 50%;
  transition: border-radius var(--transition-slow);
}

.morph-circle:hover {
  border-radius: var(--radius-lg);
}

.morph-square {
  border-radius: var(--radius-sm);
  transition: border-radius var(--transition-slow);
}

.morph-square:hover {
  border-radius: 50%;
}

/* Slide Animations */
.slide-in-up {
  animation: slideInUp 0.5s ease-out;
}

.slide-in-down {
  animation: slideInDown 0.5s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Fade Animations */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.fade-in-slow {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Scale Animations */
.scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Typing Indicator */
.typing-dots {
  display: inline-flex;
  gap: 4px;
  align-items: center;
}

.typing-dot {
  width: 6px;
  height: 6px;
  background: var(--text-secondary);
  border-radius: 50%;
  animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingDot {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

/* Loading Spinner */
.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--glass-border);
  border-top: 2px solid var(--accent-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Progress Bars */
.progress-glass {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-full);
  height: 8px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: var(--gradient-primary);
  border-radius: inherit;
  transition: width var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.progress-fill::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  :root {
    --space-md: 12px;
    --space-lg: 16px;
    --space-xl: 24px;
    --radius-md: 8px;
    --radius-lg: 12px;
  }
  
  .glass-card {
    padding: var(--space-md);
  }
  
  .btn-gradient {
    padding: var(--space-xs) var(--space-md);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .particle {
    display: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --glass-border: rgba(255, 255, 255, 0.3);
    --text-secondary: #b0b0b0;
  }
}
