<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sentinel AI Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Base styles and variables */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-bg: #0a0a0a;
            --secondary-bg: #111111;
            --card-bg: rgba(255, 255, 255, 0.03);
            --glass-bg: rgba(255, 255, 255, 0.05);
            --accent-blue: #00d4ff;
            --accent-purple: #8b5cf6;
            --accent-green: #10b981;
            --accent-orange: #f59e0b;
            --text-primary: #ffffff;
            --text-secondary: #a1a1aa;
            --border-color: rgba(255, 255, 255, 0.1);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            position: relative;
        }

        /* Enhanced background effects */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .dashboard {
            display: grid;
            grid-template-columns: 280px 1fr;
            min-height: 100vh;
            max-width: 1400px;
            width: 100%;
            box-shadow:
                0 0 50px rgba(0, 212, 255, 0.2),
                0 0 100px rgba(139, 92, 246, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            overflow: hidden;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .dashboard::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%);
            pointer-events: none;
            z-index: 0;
        }

        .sidebar {
            background: linear-gradient(180deg, var(--glass-bg) 0%, rgba(255, 255, 255, 0.02) 100%);
            backdrop-filter: blur(20px);
            border-right: 1px solid var(--border-color);
            padding: 2rem 1.5rem;
            position: relative;
            z-index: 10;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(180deg, rgba(0, 212, 255, 0.03) 0%, transparent 100%);
            pointer-events: none;
            z-index: -1;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 3rem;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .logo-text {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-link:hover, .nav-link.active {
            color: var(--text-primary);
            background: var(--glass-bg);
            transform: translateX(4px);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.1);
        }

        .nav-link::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 3px;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }

        .nav-link.active::before {
            transform: scaleY(1);
        }

        .main-content {
            padding: 2rem;
            overflow-y: auto;
            background: var(--secondary-bg); /* Slightly different background for main content */
        }

        .page-container {
            display: none;
        }

        .page-container.active {
            display: block;
        }

        /* Enhanced card styles */
        .card {
            background: linear-gradient(135deg, var(--card-bg) 0%, rgba(255, 255, 255, 0.02) 100%);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            border: 1px solid var(--border-color);
            padding: 1.5rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.5), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .card:hover {
            border-color: rgba(0, 212, 255, 0.3);
            box-shadow:
                0 8px 32px rgba(0, 212, 255, 0.1),
                0 0 0 1px rgba(0, 212, 255, 0.1);
            transform: translateY(-2px);
        }

        .card:hover::before {
            opacity: 1;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--border-color);
        }

        .card-header h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .card-header span {
            font-size: 1.5rem;
            opacity: 0.8;
        }

        .card-content {
            position: relative;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            padding: 1.5rem 2rem;
            border-radius: 16px;
            border: 1px solid var(--border-color);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
        }

        .header-left h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header-left p {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .voice-command {
            display: flex;
            gap: 1rem;
            align-items: center;
            background: var(--card-bg);
            padding: 1rem 1.5rem;
            border-radius: 50px;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .voice-command::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .voice-input {
            flex: 1;
            background: none;
            border: none;
            color: var(--text-primary);
            font-size: 1rem;
            outline: none;
        }

        .voice-input::placeholder {
            color: var(--text-secondary);
        }

        .voice-btn {
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            border: none;
            color: white;
            padding: 0.75rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.2);
        }

        .voice-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
        }

        .voice-btn.listening {
            animation: pulse-microphone 1s infinite alternate;
        }

        @keyframes pulse-microphone {
            from { transform: scale(1); box-shadow: 0 0 0 rgba(0, 212, 255, 0.4); }
            to { transform: scale(1.1); box-shadow: 0 0 15px rgba(0, 212, 255, 0.8); }
        }


        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            padding: 1.5rem;
            border-radius: 16px;
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .stat-title {
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-change {
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .stat-change.positive {
            color: var(--accent-green);
        }

        .stat-change.negative {
            color: var(--accent-orange);
        }

        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 2rem;
        }

        .research-panel {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            border: 1px solid var(--border-color);
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .research-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }

        .research-title {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .upload-btn {
            background: linear-gradient(135deg, var(--accent-purple), var(--accent-blue));
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(139, 92, 246, 0.2);
            position: relative;
            overflow: hidden;
        }

        .upload-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
        }

        .upload-btn:hover::before {
            left: 100%;
        }

        .upload-btn:active {
            transform: translateY(0);
            box-shadow: 0 4px 15px rgba(139, 92, 246, 0.2);
        }

        .drop-zone {
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            padding: 3rem 2rem;
            text-align: center;
            color: var(--text-secondary);
            margin-bottom: 2rem;
            transition: all 0.3s ease;
            position: relative;
            background: rgba(0, 0, 0, 0.1);
        }

        .drop-zone:hover {
            border-color: var(--accent-blue);
            background: rgba(0, 212, 255, 0.05);
        }

        .drop-zone.drag-over {
            border-color: var(--accent-blue);
            background: rgba(0, 212, 255, 0.1);
            transform: scale(1.02);
        }

        .agent-network {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            border: 1px solid var(--border-color);
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }

        .network-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
        }

        .agent-node {
            background: var(--card-bg);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid var(--border-color);
            position: relative;
            transition: all 0.3s ease, box-shadow 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .agent-node:hover {
            transform: translateX(8px);
            border-color: var(--accent-blue);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .agent-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--accent-green);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .agent-name {
            font-weight: 500;
        }

        .agent-activity {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: var(--card-bg);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 0.5rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            transition: width 0.3s ease;
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden; /* Ensure particles don't extend beyond viewport */
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: var(--accent-blue);
            border-radius: 50%;
            opacity: 0; /* Start hidden */
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) translateX(0);
                opacity: 0;
            }
            10% {
                opacity: 0.8;
            }
            90% {
                opacity: 0.8;
            }
            100% {
                transform: translateY(-100vh) translateX(100px);
                opacity: 0;
            }
        }

        /* Custom Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .modal-overlay.visible {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: var(--secondary-bg);
            padding: 2rem;
            border-radius: 16px;
            border: 1px solid var(--border-color);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            max-width: 500px;
            width: 90%;
            text-align: center;
            transform: translateY(-20px);
            transition: transform 0.3s ease;
        }

        .modal-overlay.visible .modal-content {
            transform: translateY(0);
        }

        .modal-content h3 {
            margin-bottom: 1rem;
            font-size: 1.5rem;
            color: var(--accent-blue);
        }

        .modal-content p {
            margin-bottom: 1.5rem;
            color: var(--text-secondary);
        }

        .modal-button {
            background: linear-gradient(135deg, var(--accent-purple), var(--accent-blue));
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .modal-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
        }

        /* Loading animations */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes shimmer {
            0% { background-position: -200px 0; }
            100% { background-position: calc(200px + 100%) 0; }
        }

        .loading {
            animation: pulse 2s infinite;
        }

        .slide-in {
            animation: slideInUp 0.5s ease-out;
        }

        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }

        .shimmer {
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            background-size: 200px 100%;
            animation: shimmer 2s infinite;
        }

        /* Enhanced scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--card-bg);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--accent-purple), var(--accent-blue));
        }

        /* Notification system */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, var(--accent-green), var(--accent-blue));
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
            transform: translateX(400px);
            transition: all 0.3s ease;
            z-index: 1000;
            max-width: 300px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.error {
            background: linear-gradient(135deg, var(--accent-orange), #ef4444);
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
        }

        .notification.warning {
            background: linear-gradient(135deg, var(--accent-orange), #f59e0b);
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
        }

        /* Responsive adjustments */
        @media (max-width: 1024px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            .sidebar {
                display: none; /* Hide sidebar on smaller screens */
            }
            .main-content {
                padding: 1.5rem;
            }
            .header {
                flex-direction: column;
                align-items: flex-start;
                padding: 1rem 1.5rem;
            }
            .header-left h1 {
                font-size: 1.75rem;
            }
            .main-grid {
                grid-template-columns: 1fr;
            }
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .dashboard {
                min-height: auto; /* Allow height to adjust */
            }
            .main-content {
                padding: 1rem;
            }
            .header-left h1 {
                font-size: 1.5rem;
            }
            .voice-command {
                flex-direction: column;
                align-items: stretch;
                padding: 0.75rem 1rem;
            }
            .voice-input {
                padding: 0.5rem;
            }
            .voice-btn {
                width: 100%;
                border-radius: 8px;
                padding: 0.5rem;
                margin-top: 0.5rem;
            }
            .stat-card {
                padding: 1rem;
            }
            .stat-value {
                font-size: 1.5rem;
            }
            .research-panel, .agent-network {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-particles"></div>

    <div class="dashboard">
        <aside class="sidebar">
            <div class="logo">
                <div class="logo-icon">S</div>
                <div class="logo-text">Sentinel</div>
            </div>

            <nav>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link active" onclick="showPage('dashboard')">
                            <span>🏠</span>
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showPage('agents')">
                            <span>🤖</span>
                            Agents
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showPage('research')">
                            <span>📚</span>
                            Research
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showPage('system')">
                            <span>🔧</span>
                            System
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showPage('network')">
                            <span>📡</span>
                            Network
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showPage('upgrades')">
                            <span>⚡</span>
                            Upgrades
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showPage('settings')">
                            <span>⚙️</span>
                            Settings
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h1 id="page-title">AI Command Center</h1>
                    <p id="page-subtitle">Multi-agent system running locally • All systems operational</p>
                </div>
                <div class="header-right">
                    <button id="check-updates-btn" class="upload-btn" title="Check for updated dashboard">Check for Updates</button>
                </div>
            </header>

            <div class="voice-command">
                <input type="text" class="voice-input" placeholder="Give me a command or ask me anything...">
                <button class="voice-btn" onclick="toggleVoice()">🎤</button>
            </div>

            <!-- Dashboard Page -->
            <div id="dashboard-page" class="page-container active">

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">System Health</span>
                        <span>💚</span>
                    </div>
                    <div class="stat-value" id="system-health">98%</div>
                    <div class="stat-change positive">↗ <span id="health-status">+2% from last hour</span></div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Active Agents</span>
                        <span>🤖</span>
                    </div>
                    <div class="stat-value" id="active-agents">4</div>
                    <div class="stat-change positive">↗ <span id="agents-status">Research agent online</span></div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Memory Usage</span>
                        <span>🧠</span>
                    </div>
                    <div class="stat-value" id="memory-usage" data-metric="memory">5.2GB</div>
                    <div class="stat-change"><span id="memory-percent">65%</span> of available</div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <span class="stat-title">Network</span>
                        <span>📡</span>
                    </div>
                    <div class="stat-value" id="network-status" data-metric="network">Sentinel</div>
                    <div class="stat-change positive">↗ <span id="network-activity">Hotspot active</span></div>
                </div>
            </div>

            <div class="main-grid">
                <div class="research-panel">
                    <div class="research-header">
                        <h2 class="research-title">Research Engine</h2>
                        <button class="upload-btn" onclick="triggerFileUpload()">Upload PDF</button>
                        <input type="file" id="file-upload-input" accept=".pdf,.txt,.log,.json,.csv" style="display: none;" onchange="handleFileUpload(event)">
                    </div>

                    <div class="drop-zone" ondrop="dropHandler(event)" ondragover="dragOverHandler(event)" ondragleave="dragLeaveHandler(event)">
                        <div style="font-size: 3rem; margin-bottom: 1rem;">📄</div>
                        <h3>Drop PDF files here</h3>
                        <p>AI will analyze and extract key insights for system upgrades</p>
                    </div>

                    <div class="recent-analysis" style="margin-bottom: 1rem;">
                        <h3 style="margin-bottom: 0.5rem;">Recent Analysis</h3>
                        <div style="background: var(--card-bg); padding: 1rem; border-radius: 10px; border: 1px solid var(--border-color); color: var(--text-secondary); text-align: center;">
                            No files uploaded yet. Upload a file to see analysis results here.
                        </div>
                    </div>
                </div>

                <div class="agent-network">
                    <h2 class="network-title">Agent Network</h2>

                    <div class="agent-node">
                        <div class="agent-status">
                            <div class="status-dot"></div>
                            <span class="agent-name">Research Agent</span>
                        </div>
                        <div class="agent-activity">Processing AI breakthrough papers</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 75%"></div>
                        </div>
                    </div>

                    <div class="agent-node">
                        <div class="agent-status">
                            <div class="status-dot"></div>
                            <span class="agent-name">System Agent</span>
                        </div>
                        <div class="agent-activity">Monitoring performance metrics</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 100%"></div>
                        </div>
                    </div>

                    <div class="agent-node">
                        <div class="agent-status">
                            <div class="status-dot"></div>
                            <span class="agent-name">Upgrade Agent</span>
                        </div>
                        <div class="agent-activity">Evaluating model improvements</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 45%"></div>
                        </div>
                    </div>

                    <div class="agent-node">
                        <div class="agent-status">
                            <div class="status-dot"></div>
                            <span class="agent-name">Communication Agent</span>
                        </div>
                        <div class="agent-activity">Managing A2A protocols</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 90%"></div>
                        </div>
                    </div>
                </div>

                <div class="anomaly-panel" style="background: var(--glass-bg); backdrop-filter: blur(20px); border-radius: 16px; border: 1px solid var(--border-color); padding: 2rem; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);">
                    <h2 class="network-title">Anomaly Detections</h2>
                    <div id="anomaly-container" style="height: 400px; overflow-y: auto;">
                        <!-- Anomaly entries will be dynamically inserted here -->
                    </div>
                </div>
            </div>

            <div class="log-analysis-panel" style="margin-top: 2rem;">
                <h2 class="research-title">Log Analysis Dashboard</h2>
                <div class="log-controls" style="display: flex; gap: 1rem; margin-top: 1rem; align-items: center;">
                    <select id="log-file-select" style="padding: 0.5rem; border-radius: 8px; background: var(--card-bg); color: var(--text-primary); border: 1px solid var(--border-color);">
                        <option value="system">System Logs</option>
                        <option value="activity">User Activity Logs</option>
                        <option value="agent">Agent Logs</option>
                        <option value="performance">Performance Logs</option>
                        <option value="security">Security Logs</option>
                        <option value="files">File Logs</option>
                    </select>
                    <select id="log-level-select" style="padding: 0.5rem; border-radius: 8px; background: var(--card-bg); color: var(--text-primary); border: 1px solid var(--border-color);">
                        <option value="">All Levels</option>
                        <option value="INFO">INFO</option>
                        <option value="WARNING">WARNING</option>
                        <option value="ERROR">ERROR</option>
                        <option value="CRITICAL">CRITICAL</option>
                    </select>
                    <input type="text" id="log-search-input" placeholder="Search logs..." style="flex: 1; background: var(--card-bg); border: 1px solid var(--border-color); color: var(--text-primary); font-size: 1rem; outline: none; padding: 0.5rem; border-radius: 8px;">
                    <button id="log-filter-btn" class="upload-btn">Filter</button>
                </div>
                <div class="search-help" style="font-size: 0.8rem; color: var(--text-secondary); margin-top: 0.5rem;">
                    Use advanced search syntax: <code>field:value</code>, <code>"quoted phrase"</code>, <code>*</code> (wildcard), <code>AND</code>, <code>OR</code>, <code>NOT</code>, <code>( )</code> for grouping.
                </div>
                <div id="log-container" style="margin-top: 1rem; background: var(--card-bg); border-radius: 16px; padding: 1.5rem; height: 500px; overflow-y: auto;">
                    <!-- Log entries will be dynamically inserted here -->
                </div>
            </div>

            <div class="export-report-panel" style="margin-top: 2rem;">
                <h2 class="research-title">Log Export and Reporting</h2>
                <div class="export-controls" style="display: flex; gap: 1rem; margin-top: 1rem; align-items: center;">
                    <select id="export-log-file-select" style="padding: 0.5rem; border-radius: 8px; background: var(--card-bg); color: var(--text-primary); border: 1px solid var(--border-color);">
                        <option value="system">System Logs</option>
                        <option value="activity">User Activity Logs</option>
                        <option value="agent">Agent Logs</option>
                        <option value="performance">Performance Logs</option>
                        <option value="security">Security Logs</option>
                        <option value="files">File Logs</option>
                    </select>
                    <select id="export-format-select" style="padding: 0.5rem; border-radius: 8px; background: var(--card-bg); color: var(--text-primary); border: 1px solid var(--border-color);">
                        <option value="json">JSON</option>
                        <option value="csv">CSV</option>
                        <option value="pdf">PDF</option>
                    </select>
                    <button id="export-logs-btn" class="upload-btn">Export Logs</button>
                </div>

                <div class="report-controls" style="margin-top: 2rem;">
                    <h3 style="margin-bottom: 1rem;">Generate Compliance Report</h3>
                    <select id="report-type-select" style="padding: 0.5rem; border-radius: 8px; background: var(--card-bg); color: var(--text-primary); border: 1px solid var(--border-color);">
                        <option value="general_compliance">General Compliance</option>
                        <option value="security_audit">Security Audit</option>
                        <option value="performance_summary">Performance Summary</option>
                    </select>
                    <textarea id="report-template-input" placeholder="Enter custom template (optional). Use {report_type}, {generation_date}, {log_type}, {log_count} placeholders." style="width: 100%; height: 100px; margin-top: 1rem; padding: 0.5rem; border-radius: 8px; background: var(--card-bg); color: var(--text-primary); border: 1px solid var(--border-color);"></textarea>
                    <button id="generate-report-btn" class="upload-btn" style="margin-top: 1rem;">Generate Report</button>
                </div>
            </div>
            </div> <!-- End Dashboard Page -->

            <!-- Agents Page -->
            <div id="agents-page" class="page-container">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-title">Active Agents</span>
                            <span>🤖</span>
                        </div>
                        <div class="stat-value" id="active-agents-count">4</div>
                        <div class="stat-change positive" id="agents-status">↗ All systems operational</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-title">AI Models</span>
                            <span>🧠</span>
                        </div>
                        <div class="stat-value" id="ai-models-count">4</div>
                        <div class="stat-change positive">↗ Models loaded and ready</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-title">Tasks Completed</span>
                            <span>✅</span>
                        </div>
                        <div class="stat-value" id="tasks-completed-count">247</div>
                        <div class="stat-change positive">↗ +12 today</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-title">Response Time</span>
                            <span>⚡</span>
                        </div>
                        <div class="stat-value" id="response-time">0.8s</div>
                        <div class="stat-change positive">↗ 15% faster</div>
                    </div>
                </div>

                <div class="main-grid">
                    <!-- Agent Control Panel -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Agent Control Panel</h3>
                            <span>🎛️</span>
                        </div>
                        <div class="card-content">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1.5rem;">
                                <button class="upload-btn" onclick="startAllAgents()" style="background: var(--accent-green);">
                                    🚀 Start All Agents
                                </button>
                                <button class="upload-btn" onclick="pauseAllAgents()" style="background: var(--accent-orange);">
                                    ⏸️ Pause All Agents
                                </button>
                                <button class="upload-btn" onclick="restartAgents()" style="background: var(--accent-blue);">
                                    🔄 Restart Agents
                                </button>
                                <button class="upload-btn" onclick="optimizeAgents()" style="background: var(--accent-purple);">
                                    ⚡ Optimize Performance
                                </button>
                            </div>

                            <div style="margin-bottom: 1.5rem;">
                                <h4 style="margin-bottom: 1rem; color: var(--text-primary);">Agent Configuration</h4>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                    <div>
                                        <label style="display: block; margin-bottom: 0.5rem; color: var(--text-secondary);">Agent Mode:</label>
                                        <select id="agent-mode-global" style="width: 100%; padding: 0.5rem; border-radius: 8px; background: var(--card-bg); color: var(--text-primary); border: 1px solid var(--border-color);">
                                            <option value="autonomous">Autonomous</option>
                                            <option value="collaborative" selected>Collaborative</option>
                                            <option value="supervised">Supervised</option>
                                            <option value="manual">Manual</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label style="display: block; margin-bottom: 0.5rem; color: var(--text-secondary);">Performance Level:</label>
                                        <select id="performance-level" style="width: 100%; padding: 0.5rem; border-radius: 8px; background: var(--card-bg); color: var(--text-primary); border: 1px solid var(--border-color);">
                                            <option value="eco">Eco Mode</option>
                                            <option value="balanced" selected>Balanced</option>
                                            <option value="performance">High Performance</option>
                                            <option value="maximum">Maximum</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h4 style="margin-bottom: 1rem; color: var(--text-primary);">Quick Actions</h4>
                                <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                    <button class="upload-btn" onclick="deployNewAgent()" style="background: var(--accent-cyan); padding: 0.5rem 1rem; font-size: 0.9rem;">
                                        🤖 Deploy New Agent
                                    </button>
                                    <button class="upload-btn" onclick="runDiagnostics()" style="background: var(--accent-green); padding: 0.5rem 1rem; font-size: 0.9rem;">
                                        🔍 Run Diagnostics
                                    </button>
                                    <button class="upload-btn" onclick="exportAgentLogs()" style="background: var(--accent-blue); padding: 0.5rem 1rem; font-size: 0.9rem;">
                                        📤 Export Logs
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Agent Network -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Agent Network Status</h3>
                            <span>🌐</span>
                        </div>
                        <div class="card-content" id="enhanced-agent-network">
                            <!-- Dynamic agent network will be populated here -->
                        </div>
                    </div>
                </div>

                <div class="main-grid">
                    <!-- AI Model Management -->
                    <div class="card">
                        <div class="card-header">
                            <h3>AI Model Management</h3>
                            <span>🧠</span>
                        </div>
                        <div class="card-content" id="ai-models-panel">
                            <!-- AI models will be populated here -->
                        </div>
                    </div>

                    <!-- Agent Communication -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Agent Communication</h3>
                            <span>💬</span>
                        </div>
                        <div class="card-content">
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; margin-bottom: 0.5rem; color: var(--text-secondary);">Send Command to Agent:</label>
                                <select id="target-agent" style="width: 100%; padding: 0.5rem; border-radius: 8px; background: var(--card-bg); color: var(--text-primary); border: 1px solid var(--border-color); margin-bottom: 0.5rem;">
                                    <option value="all">All Agents</option>
                                    <option value="research">Research Agent</option>
                                    <option value="system">System Agent</option>
                                    <option value="security">Security Agent</option>
                                    <option value="communication">Communication Agent</option>
                                </select>
                                <textarea id="agent-command" placeholder="Enter command or query for the agent..." style="width: 100%; height: 80px; padding: 0.5rem; border-radius: 8px; background: var(--card-bg); color: var(--text-primary); border: 1px solid var(--border-color); resize: vertical; margin-bottom: 0.5rem;"></textarea>
                                <button class="upload-btn" onclick="sendAgentCommand()" style="width: 100%;">
                                    📤 Send Command
                                </button>
                            </div>
                            <div id="agent-responses" style="max-height: 200px; overflow-y: auto; background: var(--glass-bg); padding: 1rem; border-radius: 8px; border: 1px solid var(--border-color);">
                                <div style="color: var(--text-secondary); text-align: center; padding: 1rem;">
                                    Agent responses will appear here...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Agent Performance Analytics -->
                <div class="card">
                    <div class="card-header">
                        <h3>Agent Performance Analytics</h3>
                        <span>📊</span>
                    </div>
                    <div class="card-content">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem;">
                            <div style="text-align: center; padding: 1rem; background: var(--glass-bg); border-radius: 10px;">
                                <div style="font-size: 2rem; color: var(--accent-blue); margin-bottom: 0.5rem;">🤖</div>
                                <div style="font-size: 1.5rem; font-weight: bold; margin-bottom: 0.25rem;" id="research-agent-efficiency">94%</div>
                                <div style="color: var(--text-secondary); font-size: 0.9rem;">Research Agent Efficiency</div>
                            </div>

                            <div style="text-align: center; padding: 1rem; background: var(--glass-bg); border-radius: 10px;">
                                <div style="font-size: 2rem; color: var(--accent-green); margin-bottom: 0.5rem;">⚡</div>
                                <div style="font-size: 1.5rem; font-weight: bold; margin-bottom: 0.25rem;" id="system-agent-uptime">99.8%</div>
                                <div style="color: var(--text-secondary); font-size: 0.9rem;">System Agent Uptime</div>
                            </div>

                            <div style="text-align: center; padding: 1rem; background: var(--glass-bg); border-radius: 10px;">
                                <div style="font-size: 2rem; color: var(--accent-purple); margin-bottom: 0.5rem;">🔒</div>
                                <div style="font-size: 1.5rem; font-weight: bold; margin-bottom: 0.25rem;" id="security-agent-alerts">12</div>
                                <div style="color: var(--text-secondary); font-size: 0.9rem;">Security Alerts Processed</div>
                            </div>

                            <div style="text-align: center; padding: 1rem; background: var(--glass-bg); border-radius: 10px;">
                                <div style="font-size: 2rem; color: var(--accent-orange); margin-bottom: 0.5rem;">📡</div>
                                <div style="font-size: 1.5rem; font-weight: bold; margin-bottom: 0.25rem;" id="communication-throughput">2.4GB</div>
                                <div style="color: var(--text-secondary); font-size: 0.9rem;">Data Throughput Today</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Research Page -->
            <div id="research-page" class="page-container">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-title">Research Status</span>
                            <span>🔬</span>
                        </div>
                        <div class="stat-value" id="research-status">Active</div>
                        <div class="stat-change positive" id="research-progress">↗ 73% complete</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-title">Tasks Completed</span>
                            <span>✅</span>
                        </div>
                        <div class="stat-value" id="tasks-completed">127</div>
                        <div class="stat-change positive">↗ +5 today</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-title">Findings</span>
                            <span>🔍</span>
                        </div>
                        <div class="stat-value" id="findings-count">12</div>
                        <div class="stat-change positive">↗ 3 new vulnerabilities</div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <span class="stat-title">Analysis Queue</span>
                            <span>⏳</span>
                        </div>
                        <div class="stat-value" id="queue-count">8</div>
                        <div class="stat-change">Files pending analysis</div>
                    </div>
                </div>

                <div class="main-grid">
                    <!-- Research Controls -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Research Controls</h3>
                            <span>🎛️</span>
                        </div>
                        <div class="card-content">
                            <!-- Quick Action Buttons -->
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1.5rem;">
                                <button class="upload-btn" onclick="startResearchTask('security_analysis')" style="background: var(--accent-blue);">
                                    🔒 Security Analysis
                                </button>
                                <button class="upload-btn" onclick="startResearchTask('anomaly_detection')" style="background: var(--accent-purple);">
                                    🚨 Anomaly Detection
                                </button>
                                <button class="upload-btn" onclick="startResearchTask('pattern_analysis')" style="background: var(--accent-green);">
                                    📊 Pattern Analysis
                                </button>
                                <button class="upload-btn" onclick="startResearchTask('threat_intelligence')" style="background: var(--accent-orange);">
                                    🛡️ Threat Intelligence
                                </button>
                            </div>

                            <!-- Research Configuration -->
                            <div style="background: var(--glass-bg); padding: 1rem; border-radius: 10px; margin-bottom: 1.5rem; border: 1px solid var(--border-color);">
                                <h4 style="margin-bottom: 1rem; color: var(--accent-cyan);">📋 Research Configuration</h4>

                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                                    <div>
                                        <label style="display: block; margin-bottom: 0.5rem; color: var(--text-secondary);">Research Target:</label>
                                        <select id="research-target" style="width: 100%; padding: 0.5rem; border-radius: 8px; background: var(--card-bg); color: var(--text-primary); border: 1px solid var(--border-color);">
                                            <option value="all_files">All Uploaded Files</option>
                                            <option value="recent_files">Recent Files Only</option>
                                            <option value="log_files">Log Files</option>
                                            <option value="pdf_files">PDF Documents</option>
                                            <option value="json_files">JSON Files</option>
                                            <option value="text_files">Text Files</option>
                                            <option value="custom">Custom Selection</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label style="display: block; margin-bottom: 0.5rem; color: var(--text-secondary);">Research Depth:</label>
                                        <select id="research-depth" style="width: 100%; padding: 0.5rem; border-radius: 8px; background: var(--card-bg); color: var(--text-primary); border: 1px solid var(--border-color);">
                                            <option value="quick">Quick Scan (5-10 min)</option>
                                            <option value="standard" selected>Standard Analysis (15-30 min)</option>
                                            <option value="deep">Deep Analysis (45-60 min)</option>
                                            <option value="comprehensive">Comprehensive (2+ hours)</option>
                                        </select>
                                    </div>
                                </div>

                                <div style="margin-bottom: 1rem;">
                                    <label style="display: block; margin-bottom: 0.5rem; color: var(--text-secondary);">Research Priority:</label>
                                    <select id="research-priority" style="width: 100%; padding: 0.5rem; border-radius: 8px; background: var(--card-bg); color: var(--text-primary); border: 1px solid var(--border-color);">
                                        <option value="low">Low Priority (Background)</option>
                                        <option value="normal" selected>Normal Priority</option>
                                        <option value="high">High Priority</option>
                                        <option value="urgent">Urgent (Immediate)</option>
                                    </select>
                                </div>

                                <div style="margin-bottom: 1rem;">
                                    <label style="display: block; margin-bottom: 0.5rem; color: var(--text-secondary);">Custom Research Query:</label>
                                    <textarea id="research-query" placeholder="Enter specific research objectives, keywords, or patterns to search for..." style="width: 100%; height: 80px; padding: 0.5rem; border-radius: 8px; background: var(--card-bg); color: var(--text-primary); border: 1px solid var(--border-color); resize: vertical;"></textarea>
                                </div>

                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                    <button class="upload-btn" onclick="startCustomResearch()" style="background: var(--accent-cyan);">
                                        🚀 Start Custom Research
                                    </button>
                                    <button class="upload-btn" onclick="scheduleResearch()" style="background: var(--accent-green);">
                                        ⏰ Schedule Research
                                    </button>
                                </div>
                            </div>

                            <!-- Research Control Actions -->
                            <div style="background: var(--glass-bg); padding: 1rem; border-radius: 10px; margin-bottom: 1.5rem; border: 1px solid var(--border-color);">
                                <h4 style="margin-bottom: 1rem; color: var(--accent-orange);">⚡ Research Control Actions</h4>

                                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 0.5rem; margin-bottom: 1rem;">
                                    <button class="upload-btn" onclick="pauseResearch()" style="background: var(--accent-orange); font-size: 0.9rem; padding: 0.5rem;">
                                        ⏸️ Pause
                                    </button>
                                    <button class="upload-btn" onclick="resumeResearch()" style="background: var(--accent-green); font-size: 0.9rem; padding: 0.5rem;">
                                        ▶️ Resume
                                    </button>
                                    <button class="upload-btn" onclick="stopResearch()" style="background: #dc2626; font-size: 0.9rem; padding: 0.5rem;">
                                        ⏹️ Stop
                                    </button>
                                </div>

                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem;">
                                    <button class="upload-btn" onclick="exportFindings()" style="background: var(--accent-purple); font-size: 0.9rem; padding: 0.5rem;">
                                        � Export Findings
                                    </button>
                                    <button class="upload-btn" onclick="clearResearchHistory()" style="background: #6b7280; font-size: 0.9rem; padding: 0.5rem;">
                                        🗑️ Clear History
                                    </button>
                                </div>
                            </div>

                            <!-- Research Agent Settings -->
                            <div style="background: var(--glass-bg); padding: 1rem; border-radius: 10px; border: 1px solid var(--border-color);">
                                <h4 style="margin-bottom: 1rem; color: var(--accent-blue);">🤖 Research Agent Settings</h4>

                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                                    <div>
                                        <label style="display: block; margin-bottom: 0.5rem; color: var(--text-secondary);">Agent Mode:</label>
                                        <select id="agent-mode" style="width: 100%; padding: 0.5rem; border-radius: 8px; background: var(--card-bg); color: var(--text-primary); border: 1px solid var(--border-color);">
                                            <option value="autonomous" selected>Autonomous</option>
                                            <option value="guided">Guided</option>
                                            <option value="manual">Manual</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label style="display: block; margin-bottom: 0.5rem; color: var(--text-secondary);">Confidence Threshold:</label>
                                        <select id="confidence-threshold" style="width: 100%; padding: 0.5rem; border-radius: 8px; background: var(--card-bg); color: var(--text-primary); border: 1px solid var(--border-color);">
                                            <option value="low">Low (60%)</option>
                                            <option value="medium" selected>Medium (75%)</option>
                                            <option value="high">High (85%)</option>
                                            <option value="very_high">Very High (95%)</option>
                                        </select>
                                    </div>
                                </div>

                                <div style="margin-bottom: 1rem;">
                                    <label style="display: flex; align-items: center; color: var(--text-secondary); cursor: pointer;">
                                        <input type="checkbox" id="auto-export" style="margin-right: 0.5rem;" checked>
                                        Auto-export findings when research completes
                                    </label>
                                </div>

                                <div style="margin-bottom: 1rem;">
                                    <label style="display: flex; align-items: center; color: var(--text-secondary); cursor: pointer;">
                                        <input type="checkbox" id="real-time-alerts" style="margin-right: 0.5rem;" checked>
                                        Real-time alerts for critical findings
                                    </label>
                                </div>

                                <button class="upload-btn" onclick="applyAgentSettings()" style="width: 100%; background: var(--accent-blue);">
                                    💾 Apply Settings
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Current Research Task -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Current Research Task</h3>
                            <span>🔄</span>
                        </div>
                        <div class="card-content" id="current-research-task">
                            <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                                <div style="font-size: 3rem; margin-bottom: 1rem;">🔬</div>
                                <p>No active research task</p>
                                <p style="font-size: 0.9rem; margin-top: 0.5rem;">Start a research task to see progress here</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="main-grid">
                    <!-- Research Findings -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Recent Findings</h3>
                            <span>🔍</span>
                        </div>
                        <div class="card-content" id="research-findings">
                            <div style="max-height: 400px; overflow-y: auto;">
                                <!-- Findings will be populated here -->
                            </div>
                        </div>
                    </div>

                    <!-- Research History -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Research History</h3>
                            <span>📚</span>
                        </div>
                        <div class="card-content" id="research-history">
                            <div style="max-height: 400px; overflow-y: auto;">
                                <!-- History will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Research Analytics -->
                <div class="card">
                    <div class="card-header">
                        <h3>Research Analytics</h3>
                        <span>📈</span>
                    </div>
                    <div class="card-content">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                            <div style="text-align: center; padding: 1rem; background: var(--glass-bg); border-radius: 10px;">
                                <div style="font-size: 2rem; color: var(--accent-blue); margin-bottom: 0.5rem;">🔒</div>
                                <div style="font-size: 1.5rem; font-weight: bold; margin-bottom: 0.25rem;" id="security-findings">3</div>
                                <div style="color: var(--text-secondary); font-size: 0.9rem;">Security Vulnerabilities</div>
                            </div>

                            <div style="text-align: center; padding: 1rem; background: var(--glass-bg); border-radius: 10px;">
                                <div style="font-size: 2rem; color: var(--accent-purple); margin-bottom: 0.5rem;">🚨</div>
                                <div style="font-size: 1.5rem; font-weight: bold; margin-bottom: 0.25rem;" id="anomaly-findings">7</div>
                                <div style="color: var(--text-secondary); font-size: 0.9rem;">Anomalies Detected</div>
                            </div>

                            <div style="text-align: center; padding: 1rem; background: var(--glass-bg); border-radius: 10px;">
                                <div style="font-size: 2rem; color: var(--accent-green); margin-bottom: 0.5rem;">📊</div>
                                <div style="font-size: 1.5rem; font-weight: bold; margin-bottom: 0.25rem;" id="pattern-findings">15</div>
                                <div style="color: var(--text-secondary); font-size: 0.9rem;">Patterns Identified</div>
                            </div>

                            <div style="text-align: center; padding: 1rem; background: var(--glass-bg); border-radius: 10px;">
                                <div style="font-size: 2rem; color: var(--accent-orange); margin-bottom: 0.5rem;">🛡️</div>
                                <div style="font-size: 1.5rem; font-weight: bold; margin-bottom: 0.25rem;" id="threat-findings">2</div>
                                <div style="color: var(--text-secondary); font-size: 0.9rem;">Threat Indicators</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> <!-- End Research Page -->

        </main>
    </div>

    <!-- Custom Modal Structure -->
    <div id="custom-modal" class="modal-overlay">
        <div class="modal-content">
            <h3 id="modal-title"></h3>
            <p id="modal-message"></p>
            <button class="modal-button" onclick="closeModal()">OK</button>
        </div>
    </div>

    <script>
        // Global variable to hold the SpeechRecognition instance
        let recognition;
        let isListening = false;

        /**
         * Shows a custom modal with a given title and message.
         * @param {string} title - The title of the modal.
         * @param {string} message - The message content of the modal.
         */
        function showModal(title, message, onOk) {
            const modal = document.getElementById('custom-modal');
            document.getElementById('modal-title').textContent = title;
            document.getElementById('modal-message').textContent = message;
            const btn = document.querySelector('.modal-button');
            btn.onclick = typeof onOk === 'function' ? onOk : closeModal;
            modal.classList.add('visible');
        }

        /**
         * Closes the custom modal.
         */
        function closeModal() {
            const modal = document.getElementById('custom-modal');
            modal.classList.remove('visible');
        }

        /**
         * Creates a specified number of floating particles and appends them to the DOM.
         */
        function createParticles() {
            const container = document.querySelector('.floating-particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                // Randomize initial position
                particle.style.left = Math.random() * 100 + 'vw';
                particle.style.top = Math.random() * 100 + 'vh';
                // Randomize animation delay and duration for a more organic feel
                particle.style.animationDelay = (Math.random() * 20) + 's';
                particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
                container.appendChild(particle);
            }
        }

        /**
         * Handles the drag over event for the drop zone.
         * Prevents default behavior and adds a 'drag-over' class for visual feedback.
         * @param {Event} event - The drag event.
         */
        function dragOverHandler(event) {
            event.preventDefault();
            const dropZone = event.currentTarget;
            dropZone.classList.add('drag-over');
        }

        /**
         * Handles the drag leave event for the drop zone.
         * Removes the 'drag-over' class.
         * @param {Event} event - The drag event.
         */
        function dragLeaveHandler(event) {
            const dropZone = event.currentTarget;
            dropZone.classList.remove('drag-over');
        }

        /**
         * Handles the drop event for the drop zone.
         * Prevents default behavior, removes 'drag-over' class, and processes dropped files.
         * @param {Event} event - The drop event.
         */
        function dropHandler(event) {
            event.preventDefault();
            const dropZone = event.currentTarget;
            dropZone.classList.remove('drag-over');

            // Check if files were dropped
            if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
                console.log('Files dropped:', event.dataTransfer.files);
                // Process the dropped files
                uploadFiles(event.dataTransfer.files);
                // Clear dataTransfer to prevent further processing on subsequent drops
                event.dataTransfer.clearData();
            }
        }

        /**
         * Triggers the hidden file input when the Upload PDF button is clicked.
         */
        function triggerFileUpload() {
            const fileInput = document.getElementById('file-upload-input');
            fileInput.click();
        }

        /**
         * Handles file upload when files are selected via the file input.
         * @param {Event} event - The change event from the file input.
         */
        function handleFileUpload(event) {
            const files = event.target.files;
            if (files && files.length > 0) {
                uploadFiles(files);
            }
        }

        /**
         * Uploads files to the server.
         * @param {FileList} files - The files to upload.
         */
        async function uploadFiles(files) {
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const formData = new FormData();
                formData.append('file', file);

                try {
                    showModal('Uploading', `Uploading ${file.name}...`);

                    const response = await fetch('/api/files/upload', {
                        method: 'POST',
                        body: formData
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const result = await response.json();
                    console.log('Upload successful:', result);

                    showModal('Upload Successful', `File "${file.name}" uploaded successfully!`);

                    // Update the recent analysis section
                    updateRecentAnalysis();

                } catch (error) {
                    console.error('Upload failed:', error);
                    showModal('Upload Failed', `Failed to upload "${file.name}": ${error.message}`);
                }
            }
        }

        /**
         * Updates the recent analysis section with recent files from the server.
         */
        async function updateRecentAnalysis() {
            try {
                const response = await fetch('/api/files/recent');
                const data = await response.json();

                const recentAnalysisContainer = document.querySelector('.research-panel');
                let analysisSection = recentAnalysisContainer.querySelector('.recent-analysis');

                if (!analysisSection) {
                    // Create the recent analysis section if it doesn't exist
                    analysisSection = document.createElement('div');
                    analysisSection.className = 'recent-analysis';
                    analysisSection.style.marginBottom = '1rem';
                    analysisSection.innerHTML = '<h3 style="margin-bottom: 0.5rem;">Recent Analysis</h3>';
                    recentAnalysisContainer.appendChild(analysisSection);
                }

                // Clear existing entries
                const existingEntries = analysisSection.querySelectorAll('div[style*="background: var(--card-bg)"]');
                existingEntries.forEach(entry => entry.remove());

                // Add recent files
                if (data.files && data.files.length > 0) {
                    data.files.forEach(file => {
                        const analysisEntry = document.createElement('div');
                        analysisEntry.style.background = 'var(--card-bg)';
                        analysisEntry.style.padding = '1rem';
                        analysisEntry.style.borderRadius = '10px';
                        analysisEntry.style.border = '1px solid var(--border-color)';
                        analysisEntry.style.marginBottom = '0.5rem';

                        const uploadTime = new Date(file.upload_time * 1000).toLocaleString();
                        const fileSize = (file.size / 1024).toFixed(1) + ' KB';

                        analysisEntry.innerHTML = `
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span>${file.filename}</span>
                                <span style="color: var(--accent-green); font-size: 0.8rem;">✓ Uploaded</span>
                            </div>
                            <div style="color: var(--text-secondary); font-size: 0.8rem; margin-top: 0.5rem;">
                                Uploaded: ${uploadTime} | Size: ${fileSize}
                            </div>
                        `;

                        analysisSection.appendChild(analysisEntry);
                    });
                } else {
                    // Show message if no files
                    const noFilesEntry = document.createElement('div');
                    noFilesEntry.style.color = 'var(--text-secondary)';
                    noFilesEntry.style.fontStyle = 'italic';
                    noFilesEntry.style.padding = '1rem';
                    noFilesEntry.textContent = 'No files uploaded yet.';
                    analysisSection.appendChild(noFilesEntry);
                }
            } catch (error) {
                console.error('Failed to fetch recent files:', error);
            }
        }

        /**
         * Toggles voice recognition on and off.
         * Utilizes the Web Speech API for speech-to-text functionality.
         */
        function toggleVoice() {
            const voiceInput = document.querySelector('.voice-input');
            const voiceBtn = document.querySelector('.voice-btn');

            if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
                showModal('Browser Not Supported', 'Speech recognition is not supported by your browser. Please use Chrome or Edge.');
                return;
            }

            // Initialize SpeechRecognition if not already done
            if (!recognition) {
                recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
                recognition.continuous = false; // Listen for a single utterance
                recognition.interimResults = false; // Only return final results
                recognition.lang = 'en-US'; // Set language

                // Event handler for when speech is recognized
                recognition.onresult = (event) => {
                    const speechResult = event.results[0][0].transcript;
                    voiceInput.value = speechResult;
                    console.log('Speech recognized:', speechResult);
                    showModal('Voice Command', `You said: "${speechResult}"`);
                };

                // Event handler for when recognition ends (either naturally or due to an "error")
                recognition.onend = () => {
                    isListening = false;
                    voiceInput.placeholder = "Give me a command or ask me anything...";
                    voiceBtn.textContent = '🎤';
                    voiceBtn.classList.remove('listening');
                    console.log('Voice recognition ended.');
                };

                // Event handler for errors during recognition
                recognition.onerror = (event) => {
                    console.error('Speech recognition error:', event.error);
                    showModal('Voice Recognition Error', `An error occurred: ${event.error}. Please try again.`);
                    isListening = false;
                    voiceInput.placeholder = "Give me a command or ask me anything...";
                    voiceBtn.textContent = '🎤';
                    voiceBtn.classList.remove('listening');
                };
            }

            // Start or stop recognition based on current state
            if (isListening) {
                recognition.stop(); // Stop listening
            } else {
                voiceInput.value = ''; // Clear previous input
                voiceInput.placeholder = "Listening...";
                voiceBtn.textContent = '🛑';
                voiceBtn.classList.add('listening');
                recognition.start(); // Start listening
                isListening = true;
                console.log('Voice recognition started.');
            }
        }

        /**
         * Fetches agent data from the API and updates the agent network panel.
         */
        async function updateAgentNetwork() {
            try {
                const response = await fetch('/api/agents');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const agents = await response.json();

                const agentNetworkContainer = document.querySelector('.agent-network');
                // Clear existing static content, but keep the title
                const title = agentNetworkContainer.querySelector('.network-title');
                agentNetworkContainer.innerHTML = '';
                agentNetworkContainer.appendChild(title);


                agents.forEach(agent => {
                    const agentNode = document.createElement('div');
                    agentNode.className = 'agent-node';

                    // Make Research Agent and Upgrade Agent clickable
                    const isInteractive = agent.name === 'Research Agent' || agent.name === 'Upgrade Agent';
                    if (isInteractive) {
                        agentNode.style.cursor = 'pointer';
                        agentNode.style.transition = 'all 0.3s ease';
                        agentNode.addEventListener('mouseenter', () => {
                            agentNode.style.transform = 'scale(1.02)';
                            agentNode.style.boxShadow = '0 4px 20px rgba(0, 255, 255, 0.3)';
                        });
                        agentNode.addEventListener('mouseleave', () => {
                            agentNode.style.transform = 'scale(1)';
                            agentNode.style.boxShadow = 'none';
                        });
                        agentNode.addEventListener('click', () => showAgentDetails(agent.name));
                    }

                    agentNode.innerHTML = `
                        <div class="agent-status">
                            <div class="status-dot"></div>
                            <span class="agent-name">${agent.name}</span>
                            ${isInteractive ? '<span style="color: var(--accent-cyan); font-size: 0.8rem; margin-left: 0.5rem;">🔍 Click for details</span>' : ''}
                        </div>
                        <div class="agent-activity">${agent.activity}</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${agent.progress}%"></div>
                        </div>
                        <div style="font-size: 0.8rem; color: var(--text-secondary); margin-top: 0.5rem;">
                            Progress: ${agent.progress}%
                        </div>
                    `;
                    agentNetworkContainer.appendChild(agentNode);
                });

            } catch (error) {
                console.error("Failed to fetch and update agent network:", error);
                const agentNetworkContainer = document.querySelector('.agent-network');
                agentNetworkContainer.innerHTML += '<p style="color: var(--accent-orange);">Failed to load agent data.</p>';
            }
        }

        /**
         * Shows detailed information about a specific agent.
         * @param {string} agentName - The name of the agent to show details for.
         */
        async function showAgentDetails(agentName) {
            try {
                let endpoint = '';
                let title = '';

                if (agentName === 'Research Agent') {
                    endpoint = '/api/agents/research';
                    title = 'Research Agent Details';
                } else if (agentName === 'Upgrade Agent') {
                    endpoint = '/api/agents/upgrade';
                    title = 'Upgrade Agent Details';
                } else {
                    return;
                }

                const response = await fetch(endpoint);
                const agentData = await response.json();

                let content = `
                    <div style="max-height: 400px; overflow-y: auto;">
                        <div style="margin-bottom: 1rem;">
                            <strong>Status:</strong> <span style="color: var(--accent-green);">${agentData.status}</span><br>
                            <strong>Current Task:</strong> ${agentData.current_task}<br>
                            <strong>Progress:</strong> ${agentData.progress}%
                        </div>
                `;

                if (agentName === 'Research Agent') {
                    content += `
                        <div style="margin-bottom: 1rem;">
                            <strong>Tasks Completed:</strong> ${agentData.tasks_completed}
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <strong>Recent Findings:</strong>
                            <ul style="margin: 0.5rem 0; padding-left: 1.5rem;">
                                ${agentData.findings.map(finding => `<li>${finding}</li>`).join('')}
                            </ul>
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <strong>Next Actions:</strong>
                            <ul style="margin: 0.5rem 0; padding-left: 1.5rem;">
                                ${agentData.next_actions.map(action => `<li>${action}</li>`).join('')}
                            </ul>
                        </div>
                        <div style="text-align: center; margin-top: 1rem;">
                            <button onclick="startResearchTask()" style="background: var(--accent-cyan); color: var(--bg-primary); border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">
                                Start New Research Task
                            </button>
                        </div>
                    `;
                } else if (agentName === 'Upgrade Agent') {
                    content += `
                        <div style="margin-bottom: 1rem;">
                            <strong>Upgrades Tested:</strong> ${agentData.upgrades_tested}
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <strong>Recent Improvements:</strong>
                            <ul style="margin: 0.5rem 0; padding-left: 1.5rem;">
                                ${agentData.improvements.map(improvement => `<li>${improvement}</li>`).join('')}
                            </ul>
                        </div>
                        <div style="margin-bottom: 1rem;">
                            <strong>Pending Upgrades:</strong>
                            <ul style="margin: 0.5rem 0; padding-left: 1.5rem;">
                                ${agentData.pending_upgrades.map(upgrade => `<li>${upgrade}</li>`).join('')}
                            </ul>
                        </div>
                        <div style="text-align: center; margin-top: 1rem;">
                            <button onclick="applyUpgrade()" style="background: var(--accent-green); color: var(--bg-primary); border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">
                                Apply Next Upgrade
                            </button>
                        </div>
                    `;
                }

                content += `
                        <div style="margin-top: 1rem; font-size: 0.8rem; color: var(--text-secondary);">
                            Last Updated: ${new Date(agentData.last_update * 1000).toLocaleString()}
                        </div>
                    </div>
                `;

                showModal(title, content);

            } catch (error) {
                console.error('Failed to fetch agent details:', error);
                showModal('Error', 'Failed to load agent details. Please try again.');
            }
        }

        /**
         * Starts a new research task.
         */
        async function startResearchTask() {
            try {
                const response = await fetch('/api/agents/research/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        task_type: 'security_analysis'
                    })
                });

                const result = await response.json();
                showModal('Research Task Started', `${result.message}<br><br>Estimated completion: ${result.estimated_completion}`);

                // Refresh agent network to show updated status
                setTimeout(() => {
                    updateAgentNetwork();
                }, 1000);

            } catch (error) {
                console.error('Failed to start research task:', error);
                showModal('Error', 'Failed to start research task. Please try again.');
            }
        }

        /**
         * Applies the next pending upgrade.
         */
        async function applyUpgrade() {
            try {
                const response = await fetch('/api/agents/upgrade/apply', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        upgrade_type: 'performance_optimization'
                    })
                });

                const result = await response.json();
                showModal('Upgrade Applied', `${result.message}<br><br>Performance Impact: ${result.performance_impact}<br>Restart Required: ${result.restart_required ? 'Yes' : 'No'}`);

                // Refresh agent network to show updated status
                setTimeout(() => {
                    updateAgentNetwork();
                }, 1000);

            } catch (error) {
                console.error('Failed to apply upgrade:', error);
                showModal('Error', 'Failed to apply upgrade. Please try again.');
            }
        }

        /**
         * Shows a specific page and updates navigation.
         * @param {string} pageName - The name of the page to show.
         */
        function showPage(pageName) {
            // Hide all pages
            const pages = document.querySelectorAll('.page-container');
            pages.forEach(page => page.classList.remove('active'));

            // Show selected page
            const targetPage = document.getElementById(pageName + '-page');
            if (targetPage) {
                targetPage.classList.add('active');
            }

            // Update navigation
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => link.classList.remove('active'));
            event.target.classList.add('active');

            // Update page title and subtitle
            const pageTitle = document.getElementById('page-title');
            const pageSubtitle = document.getElementById('page-subtitle');

            switch(pageName) {
                case 'dashboard':
                    pageTitle.textContent = 'AI Command Center';
                    pageSubtitle.textContent = 'Multi-agent system running locally • All systems operational';
                    break;
                case 'research':
                    pageTitle.textContent = 'Research Center';
                    pageSubtitle.textContent = 'AI-powered analysis and threat intelligence • Research Agent active';
                    loadResearchData();
                    break;
                case 'agents':
                    pageTitle.textContent = 'Agent Network';
                    pageSubtitle.textContent = 'Multi-agent coordination and management • 4 agents online';
                    break;
                case 'system':
                    pageTitle.textContent = 'System Monitor';
                    pageSubtitle.textContent = 'Performance metrics and system health • All systems green';
                    break;
                case 'network':
                    pageTitle.textContent = 'Network Operations';
                    pageSubtitle.textContent = 'Network monitoring and security • Connections secure';
                    break;
                case 'upgrades':
                    pageTitle.textContent = 'System Upgrades';
                    pageSubtitle.textContent = 'Model improvements and system enhancements • 3 pending';
                    break;
                case 'settings':
                    pageTitle.textContent = 'System Settings';
                    pageSubtitle.textContent = 'Configuration and preferences • Customize your experience';
                    break;
            }
        }

        /**
         * Loads research data and updates the research page.
         */
        async function loadResearchData() {
            try {
                const response = await fetch('/api/agents/research');
                const data = await response.json();

                // Update research status
                document.getElementById('research-status').textContent = data.status;
                document.getElementById('research-progress').textContent = `↗ ${data.progress}% complete`;
                document.getElementById('tasks-completed').textContent = data.tasks_completed;
                document.getElementById('findings-count').textContent = data.findings.length;

                // Update current research task
                const currentTaskContainer = document.getElementById('current-research-task');
                currentTaskContainer.innerHTML = `
                    <div style="margin-bottom: 1rem;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                            <strong>Current Task:</strong>
                            <span style="color: var(--accent-green); font-size: 0.9rem;">Active</span>
                        </div>
                        <div style="color: var(--text-secondary);">${data.current_task}</div>
                    </div>
                    <div style="margin-bottom: 1rem;">
                        <div style="margin-bottom: 0.5rem;"><strong>Progress:</strong></div>
                        <div class="progress-bar" style="background: var(--card-bg); height: 8px; border-radius: 4px;">
                            <div class="progress-fill" style="width: ${data.progress}%; background: var(--accent-cyan); height: 100%; border-radius: 4px;"></div>
                        </div>
                        <div style="text-align: right; font-size: 0.8rem; color: var(--text-secondary); margin-top: 0.25rem;">${data.progress}%</div>
                    </div>
                    <div>
                        <div style="margin-bottom: 0.5rem;"><strong>Next Actions:</strong></div>
                        <ul style="margin-left: 1rem; color: var(--text-secondary);">
                            ${data.next_actions.map(action => `<li>${action}</li>`).join('')}
                        </ul>
                    </div>
                `;

                // Update research findings
                const findingsContainer = document.getElementById('research-findings');
                findingsContainer.innerHTML = data.findings.map(finding => `
                    <div style="background: var(--card-bg); padding: 1rem; border-radius: 8px; margin-bottom: 0.5rem; border-left: 3px solid var(--accent-orange);">
                        <div style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                            <span style="color: var(--accent-orange); margin-right: 0.5rem;">🔍</span>
                            <strong>Finding</strong>
                        </div>
                        <div style="color: var(--text-secondary);">${finding}</div>
                    </div>
                `).join('');

                // Update research history (mock data for now)
                const historyContainer = document.getElementById('research-history');
                const mockHistory = [
                    { task: 'Security vulnerability scan', time: '2 hours ago', status: 'Completed' },
                    { task: 'Log pattern analysis', time: '4 hours ago', status: 'Completed' },
                    { task: 'Anomaly detection sweep', time: '6 hours ago', status: 'Completed' },
                    { task: 'Threat intelligence update', time: '8 hours ago', status: 'Completed' }
                ];

                historyContainer.innerHTML = mockHistory.map(item => `
                    <div style="background: var(--card-bg); padding: 1rem; border-radius: 8px; margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                            <strong>${item.task}</strong>
                            <span style="color: var(--accent-green); font-size: 0.8rem;">✓ ${item.status}</span>
                        </div>
                        <div style="color: var(--text-secondary); font-size: 0.9rem;">${item.time}</div>
                    </div>
                `).join('');

            } catch (error) {
                console.error('Failed to load research data:', error);
            }
        }

        /**
         * Starts a specific research task.
         * @param {string} taskType - The type of research task to start.
         */
        async function startResearchTask(taskType) {
            try {
                const response = await fetch('/api/agents/research/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        task_type: taskType
                    })
                });

                const result = await response.json();
                showModal('Research Task Started', `${result.message}<br><br>Task Type: ${taskType}<br>Estimated completion: ${result.estimated_completion}`);

                // Refresh research data
                setTimeout(() => {
                    loadResearchData();
                }, 1000);

            } catch (error) {
                console.error('Failed to start research task:', error);
                showModal('Error', 'Failed to start research task. Please try again.');
            }
        }

        /**
         * Starts a custom research task with user-selected parameters.
         */
        async function startCustomResearch() {
            const target = document.getElementById('research-target').value;
            const depth = document.getElementById('research-depth').value;
            const priority = document.getElementById('research-priority').value;
            const query = document.getElementById('research-query').value;

            try {
                const response = await fetch('/api/agents/research/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        task_type: 'custom_analysis',
                        target: target,
                        depth: depth,
                        priority: priority,
                        query: query
                    })
                });

                const result = await response.json();
                showModal('Custom Research Started', `Research task initiated with the following parameters:<br><br>Target: ${target}<br>Depth: ${depth}<br>Priority: ${priority}<br>Query: ${query || 'None'}<br><br>Estimated completion: ${result.estimated_completion}`);

                // Refresh research data
                setTimeout(() => {
                    loadResearchData();
                }, 1000);

            } catch (error) {
                console.error('Failed to start custom research:', error);
                showModal('Error', 'Failed to start custom research. Please try again.');
            }
        }

        /**
         * Schedules a research task for later execution.
         */
        async function scheduleResearch() {
            const target = document.getElementById('research-target').value;
            const depth = document.getElementById('research-depth').value;
            const priority = document.getElementById('research-priority').value;
            const query = document.getElementById('research-query').value;

            // Show scheduling modal
            const scheduleTime = prompt('Enter schedule time (e.g., "2 hours", "tomorrow 9am", "next week"):');
            if (!scheduleTime) return;

            try {
                const response = await fetch('/api/agents/research/schedule', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        task_type: 'scheduled_analysis',
                        target: target,
                        depth: depth,
                        priority: priority,
                        query: query,
                        schedule_time: scheduleTime
                    })
                });

                const result = await response.json();
                showModal('Research Scheduled', `Research task has been scheduled for: ${scheduleTime}<br><br>Task ID: ${result.task_id || 'Generated'}<br>You will be notified when the task begins.`);

            } catch (error) {
                console.error('Failed to schedule research:', error);
                showModal('Error', 'Failed to schedule research. Please try again.');
            }
        }

        /**
         * Pauses the current research task.
         */
        async function pauseResearch() {
            try {
                const response = await fetch('/api/agents/research/pause', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();
                showModal('Research Paused', 'Current research task has been paused. You can resume it at any time.');

                // Refresh research data
                setTimeout(() => {
                    loadResearchData();
                }, 1000);

            } catch (error) {
                console.error('Failed to pause research:', error);
                showModal('Error', 'Failed to pause research. Please try again.');
            }
        }

        /**
         * Resumes a paused research task.
         */
        async function resumeResearch() {
            try {
                const response = await fetch('/api/agents/research/resume', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();
                showModal('Research Resumed', 'Research task has been resumed and will continue from where it left off.');

                // Refresh research data
                setTimeout(() => {
                    loadResearchData();
                }, 1000);

            } catch (error) {
                console.error('Failed to resume research:', error);
                showModal('Error', 'Failed to resume research. Please try again.');
            }
        }

        /**
         * Stops the current research task.
         */
        async function stopResearch() {
            if (!confirm('Are you sure you want to stop the current research task? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch('/api/agents/research/stop', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();
                showModal('Research Stopped', 'Current research task has been stopped. Any partial results have been saved.');

                // Refresh research data
                setTimeout(() => {
                    loadResearchData();
                }, 1000);

            } catch (error) {
                console.error('Failed to stop research:', error);
                showModal('Error', 'Failed to stop research. Please try again.');
            }
        }

        /**
         * Exports research findings to a file.
         */
        async function exportFindings() {
            try {
                const response = await fetch('/api/agents/research/export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        format: 'json',
                        include_history: true
                    })
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `research_findings_${new Date().toISOString().split('T')[0]}.json`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    showModal('Export Complete', 'Research findings have been exported successfully.');
                } else {
                    throw new Error('Export failed');
                }

            } catch (error) {
                console.error('Failed to export findings:', error);
                showModal('Error', 'Failed to export research findings. Please try again.');
            }
        }

        /**
         * Clears research history.
         */
        async function clearResearchHistory() {
            if (!confirm('Are you sure you want to clear all research history? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch('/api/agents/research/clear-history', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();
                showModal('History Cleared', 'Research history has been cleared successfully.');

                // Refresh research data
                setTimeout(() => {
                    loadResearchData();
                }, 1000);

            } catch (error) {
                console.error('Failed to clear research history:', error);
                showModal('Error', 'Failed to clear research history. Please try again.');
            }
        }

        /**
         * Applies research agent settings.
         */
        async function applyAgentSettings() {
            const agentMode = document.getElementById('agent-mode').value;
            const confidenceThreshold = document.getElementById('confidence-threshold').value;
            const autoExport = document.getElementById('auto-export').checked;
            const realTimeAlerts = document.getElementById('real-time-alerts').checked;

            try {
                const response = await fetch('/api/agents/research/settings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        agent_mode: agentMode,
                        confidence_threshold: confidenceThreshold,
                        auto_export: autoExport,
                        real_time_alerts: realTimeAlerts
                    })
                });

                const result = await response.json();
                showModal('Settings Applied', `Research Agent settings have been updated:<br><br>Mode: ${agentMode}<br>Confidence Threshold: ${confidenceThreshold}<br>Auto-export: ${autoExport ? 'Enabled' : 'Disabled'}<br>Real-time Alerts: ${realTimeAlerts ? 'Enabled' : 'Disabled'}`);

            } catch (error) {
                console.error('Failed to apply agent settings:', error);
                showModal('Error', 'Failed to apply agent settings. Please try again.');
            }
        }

        /**
         * Fetches and displays logs based on the selected filters.
         */
        async function updateLogDashboard() {
            const logFile = document.getElementById('log-file-select').value;
            const logLevel = document.getElementById('log-level-select').value;
            const searchQuery = document.getElementById('log-search-input').value;
            const logContainer = document.getElementById('log-container');

            logContainer.innerHTML = '<p>Loading logs...</p>';

            try {
                // Construct the URL with query parameters
                const url = new URL('/api/logs', window.location.origin);
                url.searchParams.append('type', logFile);
                if (logLevel) {
                    url.searchParams.append('level', logLevel);
                }
                if (searchQuery) {
                    url.searchParams.append('query', searchQuery);
                }

                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const logs = await response.json();

                logContainer.innerHTML = ''; // Clear the container

                if (logs.length === 0) {
                    logContainer.innerHTML = '<p>No logs found.</p>';
                    return;
                }

                logs.forEach(log => {
                    const logEntry = document.createElement('div');
                    logEntry.style.borderBottom = '1px solid var(--border-color)';
                    logEntry.style.padding = '0.5rem 0';
                    logEntry.style.fontFamily = 'monospace';
                    logEntry.style.fontSize = '0.9rem';

                    const timestamp = new Date(log.timestamp).toLocaleString();
                    const level = log.levelname || 'UNKNOWN';
                    const message = log.message || '';

                    let levelColor = 'var(--text-secondary)';
                    if (level === 'WARNING') {
                        levelColor = 'var(--accent-orange)';
                    } else if (level === 'ERROR' || level === 'CRITICAL') {
                        levelColor = 'var(--accent-purple)';
                    }

                    logEntry.innerHTML = `
                        <span style="color: var(--accent-blue);">${timestamp}</span>
                        <span style="font-weight: bold; color: ${levelColor};">[${level}]</span>
                        <span>${message}</span>
                    `;
                    logContainer.appendChild(logEntry);
                });

            } catch (error) {
                console.error('Failed to fetch logs:', error);
                logContainer.innerHTML = `<p style="color: var(--accent-orange);">Failed to load logs. Make sure the backend is running.</p>`;
            }
        }

        // Add event listener for the filter button
        document.getElementById('log-filter-btn').addEventListener('click', updateLogDashboard);

        // Add event listener for the filter button
        document.getElementById('log-filter-btn').addEventListener('click', updateLogDashboard);

        /**
         * Handles log export.
         */
        async function exportLogs() {
            const logFile = document.getElementById('export-log-file-select').value;
            const format = document.getElementById('export-format-select').value;
            const logLevel = document.getElementById('log-level-select').value; // Reusing from log analysis
            const searchQuery = document.getElementById('log-search-input').value; // Reusing from log analysis

            try {
                const url = new URL('/api/logs/export', window.location.origin);
                url.searchParams.append('type', logFile);
                url.searchParams.append('format', format);
                if (logLevel) {
                    url.searchParams.append('level', logLevel);
                }
                if (searchQuery) {
                    url.searchParams.append('query', searchQuery);
                }

                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Get filename from content-disposition header
                const contentDisposition = response.headers.get('Content-Disposition');
                let filename = 'logs.json'; // Default
                if (contentDisposition && contentDisposition.indexOf('attachment') !== -1) {
                    const filenameRegex = /filename[^;=\n]*=((['\"]).*?\2|[^;\n]*)/;
                    const matches = filenameRegex.exec(contentDisposition);
                    if (matches != null && matches[1]) {
                        filename = matches[1].replace(/['\"]/g, '');
                    }
                }

                const blob = await response.blob();
                const downloadUrl = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = downloadUrl;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                a.remove();
                window.URL.revokeObjectURL(downloadUrl);

                showModal('Export Successful', `Logs exported as ${filename}!`);

            } catch (error) {
                console.error('Failed to export logs:', error);
                showModal('Export Failed', `Failed to export logs: ${error.message}.`);
            }
        }

        /**
         * Handles compliance report generation.
         */
        async function generateReport() {
            const logFile = document.getElementById('export-log-file-select').value;
            const reportType = document.getElementById('report-type-select').value;
            const customTemplate = document.getElementById('report-template-input').value;
            const logLevel = document.getElementById('log-level-select').value; // Reusing from log analysis
            const searchQuery = document.getElementById('log-search-input').value; // Reusing from log analysis

            try {
                const url = new URL('/api/reports/compliance', window.location.origin);
                url.searchParams.append('type', logFile);
                url.searchParams.append('report_type', reportType);
                if (logLevel) {
                    url.searchParams.append('level', logLevel);
                }
                if (searchQuery) {
                    url.searchParams.append('query', searchQuery);
                }
                if (customTemplate) {
                    url.searchParams.append('template', customTemplate);
                }

                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Get filename from content-disposition header
                const contentDisposition = response.headers.get('Content-Disposition');
                let filename = 'report.pdf'; // Default
                if (contentDisposition && contentDisposition.indexOf('attachment') !== -1) {
                    const filenameRegex = /filename[^;=\n]*=((['\"]).*?\2|[^;\n]*)/;
                    const matches = filenameRegex.exec(contentDisposition);
                    if (matches != null && matches[1]) {
                        filename = matches[1].replace(/['\"]/g, '');
                    }
                }

                const blob = await response.blob();
                const downloadUrl = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = downloadUrl;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                a.remove();
                window.URL.revokeObjectURL(downloadUrl);

                showModal('Report Generated', `Compliance report generated as ${filename}!`);

            } catch (error) {
                console.error('Failed to generate report:', error);
                showModal('Report Generation Failed', `Failed to generate report: ${error.message}.`);
            }
        }

        // Add event listeners for export and report buttons
        document.getElementById('export-logs-btn').addEventListener('click', exportLogs);
        document.getElementById('generate-report-btn').addEventListener('click', generateReport);

        /**
         * Fetches and displays anomalies.
         */
        async function updateAnomalyDashboard() {
            const anomalyContainer = document.getElementById('anomaly-container');
            anomalyContainer.innerHTML = '<p>Loading anomalies...</p>';

            try {
                const response = await fetch('/api/logs/alerts');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const anomalies = await response.json();

                anomalyContainer.innerHTML = ''; // Clear the container

                if (anomalies.length === 0) {
                    anomalyContainer.innerHTML = '<p>No anomalies detected.</p>';
                    return;
                }

                anomalies.forEach(anomaly => {
                    const anomalyEntry = document.createElement('div');
                    anomalyEntry.style.background = 'rgba(255, 0, 0, 0.1)'; // Light red background for anomalies
                    anomalyEntry.style.border = '1px solid var(--accent-orange)';
                    anomalyEntry.style.borderRadius = '8px';
                    anomalyEntry.style.padding = '1rem';
                    anomalyEntry.style.marginBottom = '0.5rem';
                    anomalyEntry.style.fontFamily = 'monospace';
                    anomalyEntry.style.fontSize = '0.9rem';

                    const timestamp = new Date(anomaly.timestamp).toLocaleString();
                    const message = anomaly.message || 'No message';

                    anomalyEntry.innerHTML = `
                        <span style="color: var(--accent-orange); font-weight: bold;">ANOMALY DETECTED!</span><br>
                        <span style="color: var(--text-secondary);">${timestamp}</span><br>
                        <span>${message}</span>
                    `;
                    anomalyContainer.prepend(anomalyEntry); // Add to top
                });

            } catch (error) {
                console.error('Failed to fetch anomalies:', error);
                anomalyContainer.innerHTML = `<p style="color: var(--accent-orange);">Failed to load anomalies. Make sure the backend is running.</p>`;
            }
        }

        // Initialize Socket.IO client
        const socket = io.connect('http://' + document.domain + ':' + location.port);

        socket.on('connect', function() {
            console.log('Socket.IO connected!');
        });

        socket.on('new_anomaly', function(data) {
            console.log('New anomaly received:', data);
            const anomalyContainer = document.getElementById('anomaly-container');
            const anomalyEntry = document.createElement('div');
            anomalyEntry.style.background = 'rgba(255, 0, 0, 0.1)'; // Light red background for anomalies
            anomalyEntry.style.border = '1px solid var(--accent-orange)';
            anomalyEntry.style.borderRadius = '8px';
            anomalyEntry.style.padding = '1rem';
            anomalyEntry.style.marginBottom = '0.5rem';
            anomalyEntry.style.fontFamily = 'monospace';
            anomalyEntry.style.fontSize = '0.9rem';

            const timestamp = new Date(data.anomalies[0].timestamp).toLocaleString(); // Assuming first anomaly in array
            const message = data.anomalies[0].message || 'No message';

            anomalyEntry.innerHTML = `
                <span style="color: var(--accent-orange); font-weight: bold;">REAL-TIME ANOMALY!</span><br>
                <span style="color: var(--text-secondary);">${timestamp}</span><br>
                <span>${message}</span>
            `;
            anomalyContainer.prepend(anomalyEntry); // Add to top
        });

        // Enhanced Agent Management Functions
        async function startAllAgents() {
            showNotification('Starting all agents...', 'info');
            try {
                // Start each agent type
                const agentTypes = ['research', 'system', 'security', 'communication'];
                for (const type of agentTypes) {
                    await fetch(`/api/agents/${type}/start`, { method: 'POST' });
                }
                showNotification('All agents started successfully!', 'success');
                updateEnhancedAgentNetwork();
            } catch (error) {
                showNotification('Failed to start some agents', 'error');
                console.error('Error starting agents:', error);
            }
        }

        async function pauseAllAgents() {
            showNotification('Pausing all agents...', 'info');
            try {
                await fetch('/api/agents/pause-all', { method: 'POST' });
                showNotification('All agents paused', 'warning');
                updateEnhancedAgentNetwork();
            } catch (error) {
                showNotification('Failed to pause agents', 'error');
                console.error('Error pausing agents:', error);
            }
        }

        async function restartAgents() {
            showNotification('Restarting agent network...', 'info');
            try {
                await fetch('/api/agents/restart', { method: 'POST' });
                showNotification('Agent network restarted successfully!', 'success');
                setTimeout(() => updateEnhancedAgentNetwork(), 2000);
            } catch (error) {
                showNotification('Failed to restart agents', 'error');
                console.error('Error restarting agents:', error);
            }
        }

        async function optimizeAgents() {
            showNotification('Optimizing agent performance...', 'info');
            try {
                const response = await fetch('/api/agents/optimize', { method: 'POST' });
                const result = await response.json();
                showNotification(`Performance optimized: ${result.improvement}% improvement`, 'success');
                updateEnhancedAgentNetwork();
            } catch (error) {
                showNotification('Failed to optimize agents', 'error');
                console.error('Error optimizing agents:', error);
            }
        }

        async function deployNewAgent() {
            showNotification('Deploying new agent...', 'info');
            try {
                const response = await fetch('/api/agents/deploy', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ type: 'general', name: 'Agent-' + Date.now() })
                });
                const result = await response.json();
                showNotification(`New agent deployed: ${result.name}`, 'success');
                updateEnhancedAgentNetwork();
            } catch (error) {
                showNotification('Failed to deploy new agent', 'error');
                console.error('Error deploying agent:', error);
            }
        }

        async function runDiagnostics() {
            showNotification('Running agent diagnostics...', 'info');
            try {
                const response = await fetch('/api/agents/diagnostics');
                const result = await response.json();
                showModal('Diagnostics Complete',
                    `System Health: ${result.health}%<br>` +
                    `Active Agents: ${result.active_agents}<br>` +
                    `Memory Usage: ${result.memory_usage}<br>` +
                    `Response Time: ${result.avg_response_time}ms`
                );
            } catch (error) {
                showNotification('Diagnostics failed', 'error');
                console.error('Error running diagnostics:', error);
            }
        }

        async function exportAgentLogs() {
            showNotification('Exporting agent logs...', 'info');
            try {
                const response = await fetch('/api/agents/export-logs', { method: 'POST' });
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `agent-logs-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                a.remove();
                window.URL.revokeObjectURL(url);
                showNotification('Agent logs exported successfully!', 'success');
            } catch (error) {
                showNotification('Failed to export logs', 'error');
                console.error('Error exporting logs:', error);
            }
        }

        async function sendAgentCommand() {
            const targetAgent = document.getElementById('target-agent').value;
            const command = document.getElementById('agent-command').value.trim();

            if (!command) {
                showNotification('Please enter a command', 'warning');
                return;
            }

            try {
                const response = await fetch('/api/agent/interact', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        prompt: command,
                        target: targetAgent
                    })
                });

                const result = await response.json();

                // Add response to the responses panel
                const responsesPanel = document.getElementById('agent-responses');
                const responseDiv = document.createElement('div');
                responseDiv.style.marginBottom = '1rem';
                responseDiv.style.padding = '0.5rem';
                responseDiv.style.background = 'var(--card-bg)';
                responseDiv.style.borderRadius = '8px';
                responseDiv.style.borderLeft = '3px solid var(--accent-blue)';

                responseDiv.innerHTML = `
                    <div style="font-weight: bold; color: var(--accent-blue); margin-bottom: 0.25rem;">
                        ${targetAgent === 'all' ? 'All Agents' : targetAgent.charAt(0).toUpperCase() + targetAgent.slice(1) + ' Agent'}
                    </div>
                    <div style="color: var(--text-primary); margin-bottom: 0.25rem;">
                        <strong>Command:</strong> ${command}
                    </div>
                    <div style="color: var(--text-secondary);">
                        <strong>Response:</strong> ${result.response || 'Command executed successfully'}
                    </div>
                `;

                responsesPanel.insertBefore(responseDiv, responsesPanel.firstChild);

                // Clear the command input
                document.getElementById('agent-command').value = '';

                showNotification('Command sent successfully!', 'success');

            } catch (error) {
                showNotification('Failed to send command', 'error');
                console.error('Error sending command:', error);
            }
        }

        // Live Dashboard Statistics Update Functions
        async function updateLiveDashboardStats() {
            try {
                // Fetch real-time data from multiple endpoints
                const [agentsResponse, diagnosticsResponse, liveStatsResponse] = await Promise.all([
                    fetch('/api/agents'),
                    fetch('/api/agents/diagnostics'),
                    fetch('/api/system/live-stats')
                ]);

                const agents = await agentsResponse.json();
                const diagnostics = await diagnosticsResponse.json();
                const liveStats = await liveStatsResponse.json();

                // Update main dashboard stats
                updateMainDashboardStats(agents, diagnostics, liveStats);

                // Update agents page stats
                updateAgentsPageStats(agents, diagnostics);

                // Show live update indicator
                showLiveUpdateIndicator();
                updateLiveIndicatorActivity();
                updateLastUpdateTime();

            } catch (error) {
                console.error('Failed to update live dashboard stats:', error);
                // Continue with existing data if API fails
            }
        }

        function updateLastUpdateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            const lastUpdateElement = document.getElementById('last-update-time');
            if (lastUpdateElement) {
                lastUpdateElement.textContent = timeString;
            }
        }

        function startUpdateCountdown() {
            let countdown = 5;
            const countdownElement = document.getElementById('next-update-countdown');

            const updateCountdown = () => {
                if (countdownElement) {
                    countdownElement.textContent = `${countdown}s`;
                }
                countdown--;
                if (countdown < 0) {
                    countdown = 5; // Reset for next cycle
                }
            };

            // Update immediately
            updateCountdown();

            // Update every second
            setInterval(updateCountdown, 1000);
        }

        function showLiveUpdateIndicator() {
            // Add a subtle indicator that data was updated
            const indicator = document.createElement('div');
            indicator.style.position = 'fixed';
            indicator.style.top = '20px';
            indicator.style.right = '20px';
            indicator.style.background = 'var(--accent-green)';
            indicator.style.color = 'white';
            indicator.style.padding = '0.5rem 1rem';
            indicator.style.borderRadius = '20px';
            indicator.style.fontSize = '0.8rem';
            indicator.style.zIndex = '10000';
            indicator.style.opacity = '0';
            indicator.style.transition = 'opacity 0.3s ease';
            indicator.textContent = '📊 Live data updated';

            document.body.appendChild(indicator);

            // Fade in
            setTimeout(() => indicator.style.opacity = '1', 100);

            // Fade out and remove
            setTimeout(() => {
                indicator.style.opacity = '0';
                setTimeout(() => document.body.removeChild(indicator), 300);
            }, 2000);
        }

        function updateMainDashboardStats(agents, diagnostics, liveStats) {
            // Update System Health
            const systemHealth = document.getElementById('system-health');
            if (systemHealth) {
                const health = liveStats.system_health?.overall || diagnostics.health || 94;
                systemHealth.textContent = `${health}%`;

                // Update health status message
                const healthStatus = document.getElementById('health-status');
                if (healthStatus) {
                    if (health >= 95) {
                        healthStatus.textContent = 'Excellent';
                        healthStatus.parentElement.className = 'stat-change positive';
                    } else if (health >= 85) {
                        healthStatus.textContent = 'Good';
                        healthStatus.parentElement.className = 'stat-change positive';
                    } else if (health >= 70) {
                        healthStatus.textContent = 'Fair';
                        healthStatus.parentElement.className = 'stat-change';
                    } else {
                        healthStatus.textContent = 'Needs Attention';
                        healthStatus.parentElement.className = 'stat-change negative';
                    }
                }
            }

            // Update Active Agents
            const activeAgents = document.getElementById('active-agents');
            if (activeAgents) {
                activeAgents.textContent = agents.length;

                // Update agents status message
                const agentsStatus = document.getElementById('agents-status');
                if (agentsStatus) {
                    const activeCount = agents.filter(a => a.status === 'active').length;
                    if (activeCount === agents.length) {
                        agentsStatus.textContent = 'All online';
                    } else {
                        agentsStatus.textContent = `${activeCount}/${agents.length} online`;
                    }
                }
            }

            // Update Memory Usage with live data
            const memoryUsage = document.getElementById('memory-usage');
            if (memoryUsage) {
                const memoryPercent = liveStats.system_health?.memory_usage || diagnostics.memory_usage || '67%';
                const memoryValue = parseFloat(memoryPercent) / 100 * 8; // Assume 8GB total
                memoryUsage.textContent = `${memoryValue.toFixed(1)}GB`;

                const memoryPercentSpan = document.getElementById('memory-percent');
                if (memoryPercentSpan) {
                    memoryPercentSpan.textContent = memoryPercent;
                }
            }

            // Update Network Activity with live data
            const networkStatus = document.getElementById('network-status');
            const networkActivity = document.getElementById('network-activity');
            if (networkStatus && networkActivity) {
                const networkThroughput = liveStats.system_health?.network_throughput || '25.5 Mbps';
                networkStatus.textContent = networkThroughput;

                // Update activity based on trend
                const trend = liveStats.performance_trends?.network_trend || 'stable';
                if (trend === 'increasing') {
                    networkActivity.textContent = '↗ Traffic increasing';
                    networkActivity.parentElement.className = 'stat-change positive';
                } else if (trend === 'decreasing') {
                    networkActivity.textContent = '↘ Traffic decreasing';
                    networkActivity.parentElement.className = 'stat-change';
                } else {
                    networkActivity.textContent = '→ Traffic stable';
                    networkActivity.parentElement.className = 'stat-change positive';
                }
            }

            // Update performance indicators with live data
            updatePerformanceIndicators(agents, diagnostics, liveStats);
        }

        function updateAgentsPageStats(agents, diagnostics) {
            // Update active agents count
            const activeAgentsCount = document.getElementById('active-agents-count');
            if (activeAgentsCount) {
                activeAgentsCount.textContent = agents.length;
            }

            // Update tasks completed count
            const tasksCompletedCount = document.getElementById('tasks-completed-count');
            if (tasksCompletedCount) {
                const totalTasks = agents.reduce((sum, agent) => sum + (agent.progress || 0), 0);
                tasksCompletedCount.textContent = Math.floor(totalTasks * 2.5); // Simulate task count
            }

            // Update response time
            const responseTime = document.getElementById('response-time');
            if (responseTime) {
                const avgResponseTime = diagnostics.avg_response_time || 800;
                responseTime.textContent = `${(avgResponseTime / 1000).toFixed(1)}s`;
            }

            // Update individual agent performance metrics
            updateAgentPerformanceMetrics(agents, diagnostics);
        }

        function updatePerformanceIndicators(agents, diagnostics, liveStats = null) {
            // Update CPU usage with live data
            const cpuUsage = document.querySelector('.stat-value[data-metric="cpu"]');
            if (cpuUsage) {
                if (liveStats && liveStats.system_health?.cpu_usage) {
                    cpuUsage.textContent = liveStats.system_health.cpu_usage;
                } else {
                    const avgProgress = agents.reduce((sum, agent) => sum + agent.progress, 0) / agents.length;
                    const cpuPercent = Math.floor(avgProgress * 0.6 + 20); // Convert progress to CPU usage
                    cpuUsage.textContent = `${cpuPercent}%`;
                }
            }

            // Update memory usage with live data
            const memoryUsage = document.querySelector('.stat-value[data-metric="memory"]');
            if (memoryUsage) {
                if (liveStats && liveStats.system_health?.memory_usage) {
                    memoryUsage.textContent = liveStats.system_health.memory_usage;
                } else {
                    const memoryPercent = diagnostics.memory_usage || '67%';
                    memoryUsage.textContent = memoryPercent;
                }
            }

            // Update network activity with live data
            const networkActivity = document.querySelector('.stat-value[data-metric="network"]');
            if (networkActivity) {
                if (liveStats && liveStats.system_health?.network_throughput) {
                    networkActivity.textContent = liveStats.system_health.network_throughput;
                } else {
                    const networkMbps = (Math.random() * 50 + 10).toFixed(1);
                    networkActivity.textContent = `${networkMbps} Mbps`;
                }
            }
        }

        function updateAgentPerformanceMetrics(agents, diagnostics) {
            // Update Research Agent Efficiency
            const researchEfficiency = document.getElementById('research-agent-efficiency');
            if (researchEfficiency) {
                const researchAgent = agents.find(a => a.name.includes('Research'));
                if (researchAgent) {
                    const efficiency = Math.floor(researchAgent.progress * 0.9 + 10);
                    researchEfficiency.textContent = `${efficiency}%`;
                }
            }

            // Update System Agent Uptime
            const systemUptime = document.getElementById('system-agent-uptime');
            if (systemUptime) {
                const systemAgent = agents.find(a => a.name.includes('System'));
                if (systemAgent) {
                    const uptime = (systemAgent.progress * 0.998 + 99).toFixed(1);
                    systemUptime.textContent = `${uptime}%`;
                }
            }

            // Update Security Alerts
            const securityAlerts = document.getElementById('security-agent-alerts');
            if (securityAlerts) {
                const currentAlerts = parseInt(securityAlerts.textContent) || 12;
                const newAlerts = currentAlerts + (diagnostics.errors_detected || 0);
                securityAlerts.textContent = newAlerts;
            }

            // Update Communication Throughput
            const communicationThroughput = document.getElementById('communication-throughput');
            if (communicationThroughput) {
                const commAgent = agents.find(a => a.name.includes('Communication'));
                if (commAgent) {
                    const throughput = (commAgent.progress * 0.05 + 2).toFixed(1);
                    communicationThroughput.textContent = `${throughput}GB`;
                }
            }
        }

        function addLiveIndicator() {
            // Add a permanent live indicator to the header
            const header = document.querySelector('.header-left');
            if (header && !document.getElementById('live-indicator')) {
                const liveIndicator = document.createElement('div');
                liveIndicator.id = 'live-indicator';
                liveIndicator.style.display = 'inline-flex';
                liveIndicator.style.alignItems = 'center';
                liveIndicator.style.gap = '0.5rem';
                liveIndicator.style.marginLeft = '1rem';
                liveIndicator.style.padding = '0.25rem 0.75rem';
                liveIndicator.style.background = 'var(--accent-green)';
                liveIndicator.style.color = 'white';
                liveIndicator.style.borderRadius = '15px';
                liveIndicator.style.fontSize = '0.8rem';
                liveIndicator.style.fontWeight = '500';

                const dot = document.createElement('div');
                dot.style.width = '8px';
                dot.style.height = '8px';
                dot.style.background = 'white';
                dot.style.borderRadius = '50%';
                dot.style.animation = 'pulse 2s infinite';

                const text = document.createElement('span');
                text.textContent = 'LIVE';

                liveIndicator.appendChild(dot);
                liveIndicator.appendChild(text);
                header.appendChild(liveIndicator);

                // Add pulse animation
                if (!document.getElementById('pulse-animation')) {
                    const style = document.createElement('style');
                    style.id = 'pulse-animation';
                    style.textContent = `
                        @keyframes pulse {
                            0% { opacity: 1; }
                            50% { opacity: 0.5; }
                            100% { opacity: 1; }
                        }
                    `;
                    document.head.appendChild(style);
                }
            }
        }

        function updateLiveIndicatorActivity() {
            const indicator = document.getElementById('live-indicator');
            if (indicator) {
                // Flash the indicator briefly to show activity
                indicator.style.background = 'var(--accent-blue)';
                setTimeout(() => {
                    indicator.style.background = 'var(--accent-green)';
                }, 200);
            }
        }

        // Enhanced initialization with animations and loading states
        document.addEventListener('DOMContentLoaded', () => {
            // Add fade-in animation to main elements
            const cards = document.querySelectorAll('.card, .stat-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                    card.classList.add('slide-in');
                }, index * 100);
            });

            // Initialize with enhanced loading states
            createParticles();
            updateAgentNetworkWithLoading();
            updateEnhancedAgentNetworkWithLoading();
            updateAIModelsPanel();
            updateLogDashboardWithLoading();
            updateAnomalyDashboardWithLoading();
            updateRecentAnalysisWithLoading();

            // Initialize live stats
            updateLiveDashboardStats();
            updateLastUpdateTime();

            // Start countdown timer
            startUpdateCountdown();

            // Auto-refresh with smooth transitions and live stats
            setInterval(() => {
                updateAgentNetwork();
                updateEnhancedAgentNetwork();
                updateAIModelsPanel();
                updateLogDashboard();
                updateAnomalyDashboard();
                updateRecentAnalysis();
                updateLiveDashboardStats();
            }, 5000); // Reduced to 5 seconds for highly responsive live updates

            // Add live indicator to header
            addLiveIndicator();

            // Add smooth page transitions
            enhancePageTransitions();
        });

        // Enhanced loading functions
        function updateAgentNetworkWithLoading() {
            const agentNetwork = document.querySelector('.agent-network');
            if (agentNetwork) {
                agentNetwork.classList.add('loading');
                updateAgentNetwork();
                setTimeout(() => agentNetwork.classList.remove('loading'), 1000);
            }
        }

        function updateLogDashboardWithLoading() {
            const logContainer = document.getElementById('log-container');
            if (logContainer) {
                logContainer.classList.add('loading');
                updateLogDashboard();
                setTimeout(() => logContainer.classList.remove('loading'), 1000);
            }
        }

        function updateAnomalyDashboardWithLoading() {
            updateAnomalyDashboard();
        }

        function updateRecentAnalysisWithLoading() {
            const recentAnalysis = document.querySelector('.recent-analysis');
            if (recentAnalysis) {
                recentAnalysis.classList.add('loading');
                updateRecentAnalysis();
                setTimeout(() => recentAnalysis.classList.remove('loading'), 1000);
            }
        }

        function updateEnhancedAgentNetworkWithLoading() {
            const agentNetwork = document.getElementById('enhanced-agent-network');
            if (agentNetwork) {
                agentNetwork.classList.add('loading');
                updateEnhancedAgentNetwork();
                setTimeout(() => agentNetwork.classList.remove('loading'), 1000);
            }
        }

        // Enhanced Agent Network Update Function
        async function updateEnhancedAgentNetwork() {
            try {
                const response = await fetch('/api/agents');
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                const agents = await response.json();

                const container = document.getElementById('enhanced-agent-network');
                container.innerHTML = '';

                agents.forEach((agent, index) => {
                    const agentCard = document.createElement('div');
                    agentCard.className = 'agent-node';
                    agentCard.style.marginBottom = '1rem';
                    agentCard.style.position = 'relative';
                    agentCard.style.overflow = 'hidden';

                    // Determine agent type icon and color
                    let icon = '🤖';
                    let color = 'var(--accent-blue)';

                    if (agent.name.includes('Research')) {
                        icon = '🔬';
                        color = 'var(--accent-purple)';
                    } else if (agent.name.includes('System')) {
                        icon = '⚙️';
                        color = 'var(--accent-green)';
                    } else if (agent.name.includes('Security')) {
                        icon = '🔒';
                        color = 'var(--accent-orange)';
                    } else if (agent.name.includes('Communication')) {
                        icon = '📡';
                        color = 'var(--accent-cyan)';
                    }

                    agentCard.innerHTML = `
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <span style="font-size: 1.5rem;">${icon}</span>
                                <div>
                                    <div style="font-weight: bold; color: var(--text-primary);">${agent.name}</div>
                                    <div style="font-size: 0.8rem; color: var(--text-secondary);">ID: AGT-${String(index + 1).padStart(3, '0')}</div>
                                </div>
                            </div>
                            <div style="text-align: right;">
                                <div style="font-size: 0.9rem; font-weight: bold; color: ${color};">${agent.status.toUpperCase()}</div>
                                <div style="font-size: 0.8rem; color: var(--text-secondary);">CPU: ${Math.floor(Math.random() * 30 + 10)}%</div>
                            </div>
                        </div>

                        <div style="margin-bottom: 0.5rem;">
                            <div style="font-size: 0.9rem; color: var(--text-secondary); margin-bottom: 0.25rem;">Current Activity:</div>
                            <div style="color: var(--text-primary);">${agent.activity}</div>
                        </div>

                        <div style="margin-bottom: 0.5rem;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.25rem;">
                                <span style="font-size: 0.9rem; color: var(--text-secondary);">Progress:</span>
                                <span style="font-size: 0.9rem; color: var(--text-primary);">${agent.progress}%</span>
                            </div>
                            <div class="progress-bar" style="background: var(--card-bg); height: 6px; border-radius: 3px;">
                                <div class="progress-fill" style="width: ${agent.progress}%; background: ${color}; height: 100%; border-radius: 3px; transition: width 0.3s ease;"></div>
                            </div>
                        </div>

                        <div style="display: flex; gap: 0.5rem; margin-top: 1rem;">
                            <button class="upload-btn" onclick="controlAgent('${agent.name}', 'pause')" style="flex: 1; padding: 0.25rem 0.5rem; font-size: 0.8rem; background: var(--accent-orange);">
                                ⏸️ Pause
                            </button>
                            <button class="upload-btn" onclick="controlAgent('${agent.name}', 'restart')" style="flex: 1; padding: 0.25rem 0.5rem; font-size: 0.8rem; background: var(--accent-blue);">
                                🔄 Restart
                            </button>
                            <button class="upload-btn" onclick="showAgentDetails('${agent.name}')" style="flex: 1; padding: 0.25rem 0.5rem; font-size: 0.8rem; background: var(--accent-green);">
                                📊 Details
                            </button>
                        </div>
                    `;

                    container.appendChild(agentCard);
                });

                // Update agent count
                document.getElementById('active-agents-count').textContent = agents.length;

            } catch (error) {
                console.error('Failed to update enhanced agent network:', error);
                const container = document.getElementById('enhanced-agent-network');
                container.innerHTML = '<p style="color: var(--accent-orange);">Failed to load agent data.</p>';
            }
        }

        // AI Models Panel Update Function
        async function updateAIModelsPanel() {
            try {
                // Mock AI models data - in real implementation, this would come from an API
                const models = [
                    {
                        name: 'Granite 4.0 Preview',
                        size: '4.0 GB',
                        status: 'active',
                        type: 'General Purpose',
                        performance: 94,
                        tasks: 156
                    },
                    {
                        name: 'SmolLM 1.7B',
                        size: '1.1 GB',
                        status: 'active',
                        type: 'System Monitoring',
                        performance: 87,
                        tasks: 89
                    },
                    {
                        name: 'Llama 3.2 1B',
                        size: '1.1 GB',
                        status: 'active',
                        type: 'Research Analysis',
                        performance: 91,
                        tasks: 203
                    },
                    {
                        name: 'Phi3 128K',
                        size: '2.4 GB',
                        status: 'active',
                        type: 'Security Analysis',
                        performance: 96,
                        tasks: 67
                    }
                ];

                const container = document.getElementById('ai-models-panel');
                container.innerHTML = '';

                models.forEach(model => {
                    const modelCard = document.createElement('div');
                    modelCard.style.background = 'var(--glass-bg)';
                    modelCard.style.padding = '1rem';
                    modelCard.style.borderRadius = '10px';
                    modelCard.style.border = '1px solid var(--border-color)';
                    modelCard.style.marginBottom = '1rem';

                    const statusColor = model.status === 'active' ? 'var(--accent-green)' : 'var(--accent-orange)';

                    modelCard.innerHTML = `
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                            <div>
                                <div style="font-weight: bold; color: var(--text-primary);">${model.name}</div>
                                <div style="font-size: 0.8rem; color: var(--text-secondary);">${model.type} • ${model.size}</div>
                            </div>
                            <div style="text-align: right;">
                                <div style="font-size: 0.9rem; font-weight: bold; color: ${statusColor};">${model.status.toUpperCase()}</div>
                                <div style="font-size: 0.8rem; color: var(--text-secondary);">${model.tasks} tasks</div>
                            </div>
                        </div>

                        <div style="margin-bottom: 0.5rem;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.25rem;">
                                <span style="font-size: 0.9rem; color: var(--text-secondary);">Performance:</span>
                                <span style="font-size: 0.9rem; color: var(--text-primary);">${model.performance}%</span>
                            </div>
                            <div class="progress-bar" style="background: var(--card-bg); height: 4px; border-radius: 2px;">
                                <div class="progress-fill" style="width: ${model.performance}%; background: var(--accent-blue); height: 100%; border-radius: 2px;"></div>
                            </div>
                        </div>

                        <div style="display: flex; gap: 0.5rem; margin-top: 0.5rem;">
                            <button class="upload-btn" onclick="controlModel('${model.name}', 'optimize')" style="flex: 1; padding: 0.25rem 0.5rem; font-size: 0.8rem; background: var(--accent-purple);">
                                ⚡ Optimize
                            </button>
                            <button class="upload-btn" onclick="controlModel('${model.name}', 'reload')" style="flex: 1; padding: 0.25rem 0.5rem; font-size: 0.8rem; background: var(--accent-blue);">
                                🔄 Reload
                            </button>
                        </div>
                    `;

                    container.appendChild(modelCard);
                });

                // Update models count
                document.getElementById('ai-models-count').textContent = models.length;

            } catch (error) {
                console.error('Failed to update AI models panel:', error);
            }
        }

        // Agent Control Functions
        async function controlAgent(agentName, action) {
            showNotification(`${action.charAt(0).toUpperCase() + action.slice(1)}ing ${agentName}...`, 'info');
            try {
                const response = await fetch(`/api/agents/control`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ agent: agentName, action: action })
                });

                if (response.ok) {
                    showNotification(`${agentName} ${action}ed successfully!`, 'success');
                    updateEnhancedAgentNetwork();
                } else {
                    throw new Error('Control action failed');
                }
            } catch (error) {
                showNotification(`Failed to ${action} ${agentName}`, 'error');
                console.error(`Error controlling agent:`, error);
            }
        }

        async function controlModel(modelName, action) {
            showNotification(`${action.charAt(0).toUpperCase() + action.slice(1)}ing ${modelName}...`, 'info');
            try {
                const response = await fetch(`/api/models/control`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ model: modelName, action: action })
                });

                if (response.ok) {
                    showNotification(`${modelName} ${action}ed successfully!`, 'success');
                    updateAIModelsPanel();
                } else {
                    throw new Error('Model control action failed');
                }
            } catch (error) {
                showNotification(`Failed to ${action} ${modelName}`, 'error');
                console.error(`Error controlling model:`, error);
            }
        }

        // Enhanced page transitions
        function enhancePageTransitions() {
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetPage = this.getAttribute('onclick').match(/'([^']+)'/)[1];
                    showPageWithTransition(targetPage);
                });
            });
        }

        function showPageWithTransition(pageId) {
            const currentPage = document.querySelector('.page-container.active');
            const targetPage = document.getElementById(pageId + '-page');

            if (currentPage && targetPage && currentPage !== targetPage) {
                // Update navigation with smooth animation
                document.querySelectorAll('.nav-link').forEach(link => {
                    link.classList.remove('active');
                    link.style.transform = 'translateX(0)';
                });

                const activeLink = document.querySelector(`[onclick="showPage('${pageId}')"]`);
                if (activeLink) {
                    activeLink.classList.add('active');
                    activeLink.style.transform = 'translateX(4px)';
                }

                // Smooth page transition
                currentPage.style.opacity = '0';
                currentPage.style.transform = 'translateX(-20px)';

                setTimeout(() => {
                    currentPage.classList.remove('active');
                    targetPage.classList.add('active');

                    targetPage.style.opacity = '0';
                    targetPage.style.transform = 'translateX(20px)';

                    setTimeout(() => {
                        targetPage.style.transition = 'all 0.3s ease';
                        targetPage.style.opacity = '1';
                        targetPage.style.transform = 'translateX(0)';

                        // Add slide-in animation to cards in the new page
                        const newPageCards = targetPage.querySelectorAll('.card, .stat-card');
                        newPageCards.forEach((card, index) => {
                            card.style.opacity = '0';
                            card.style.transform = 'translateY(20px)';
                            setTimeout(() => {
                                card.style.transition = 'all 0.3s ease';
                                card.style.opacity = '1';
                                card.style.transform = 'translateY(0)';
                            }, index * 50);
                        });
                    }, 50);
                }, 150);
            }
        }

        // Enhanced notification system
        function showNotification(message, type = 'success', duration = 3000) {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notif => notif.remove());

            // Create new notification
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Show notification
            setTimeout(() => notification.classList.add('show'), 100);

            // Hide notification after duration
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, duration);
        }

        // Enhanced modal with better animations
        function showModal(title, message, type = 'info') {
            const modal = document.getElementById('modal');
            const modalTitle = modal.querySelector('h3');
            const modalMessage = modal.querySelector('p');

            modalTitle.textContent = title;
            modalMessage.textContent = message;

            // Add type-specific styling
            const modalContent = modal.querySelector('.modal-content');
            modalContent.className = 'modal-content';
            if (type === 'error') {
                modalContent.style.borderColor = 'var(--accent-orange)';
                modalTitle.style.color = 'var(--accent-orange)';
            } else if (type === 'success') {
                modalContent.style.borderColor = 'var(--accent-green)';
                modalTitle.style.color = 'var(--accent-green)';
            } else {
                modalContent.style.borderColor = 'var(--accent-blue)';
                modalTitle.style.color = 'var(--accent-blue)';
            }

            modal.classList.add('visible');
        }

        // Override existing functions to use notifications
        const originalStartResearchTask = window.startResearchTask;
        window.startResearchTask = function(taskType) {
            showNotification(`Starting ${taskType.replace('_', ' ')} research task...`, 'success');
            if (originalStartResearchTask) {
                originalStartResearchTask(taskType);
            }
        };

        // Lightweight self-update checker (ETag/Last-Modified)
        (function initSelfUpdate() {
            let baselineSig = null;
            async function headSig() {
                try {
                    const res = await fetch(window.location.pathname + window.location.search, { method: 'HEAD', cache: 'no-store' });
                    return res.headers.get('ETag') || res.headers.get('Last-Modified') || '';
                } catch (e) {
                    console.warn('Update HEAD failed:', e);
                    return '';
                }
            }

            async function poll(showToast) {
                const sig = await headSig();
                if (baselineSig === null) { baselineSig = sig; return; }
                if (sig && sig !== baselineSig) {
                    if (confirm('A new version of the dashboard is available. Reload now?')) {
                        window.location.reload();
                    } else {
                        // keep notifying subtly
                        showNotification('Update available. Click Check for Updates to reload.', 'success', 4000);
                    }
                } else if (showToast) {
                    showNotification('You are running the latest dashboard.', 'success', 2000);
                }
            }

            // Periodic polling
            setInterval(poll, 30000);
            // Manual button
            document.addEventListener('DOMContentLoaded', () => {
                const btn = document.getElementById('check-updates-btn');
                if (btn) btn.addEventListener('click', () => poll(true));
                // Initialize baseline on first load
                headSig().then(sig => { baselineSig = sig; }).catch(() => {});
            });
        })();
    </script>
</body>
</html>
