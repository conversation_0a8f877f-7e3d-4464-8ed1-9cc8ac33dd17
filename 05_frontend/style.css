body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f4f4f4;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    min-height: 100vh;
}

.container {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 800px;
}

.general-buttons {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
}

.general-buttons button {
    padding: 10px 15px;
    border: none;
    background-color: #007bff;
    color: white;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
}

.general-buttons button:hover {
    background-color: #0056b3;
}

.chat-interface {
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 10px;
    background-color: #f9f9f9;
}

.chat-display {
    height: 300px;
    overflow-y: auto;
    border: 1px solid #eee;
    padding: 10px;
    margin-bottom: 10px;
    background-color: #fff;
    border-radius: 5px;
}

.chat-message {
    margin-bottom: 8px;
    padding: 5px 10px;
    border-radius: 5px;
    background-color: #e2f0ff;
    align-self: flex-start;
}

.chat-input {
    width: calc(100% - 70px);
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-right: 10px;
}

.chat-interface button {
    padding: 10px 15px;
    border: none;
    background-color: #28a745;
    color: white;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
}

.chat-interface button:hover {
    background-color: #218838;
}

.pdf-upload-system {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pdf-upload-system input[type="file"] {
    display: none;
}

.pdf-upload-system .upload-button {
    padding: 10px 15px;
    border: none;
    background-color: #ffc107;
    color: #333;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
}

.pdf-upload-system .upload-button:hover {
    background-color: #e0a800;
}

#selected-file-name {
    font-style: italic;
    color: #555;
}