document.addEventListener('DOMContentLoaded', () => {
    const chatDisplay = document.getElementById('chat-display');
    const chatInput = document.getElementById('chat-input');
    const sendChatButton = document.getElementById('send-chat');
    const pdfUploadInput = document.getElementById('pdf-upload');
    const selectedFileNameSpan = document.getElementById('selected-file-name');
    const button1 = document.getElementById('button1');
    const button2 = document.getElementById('button2');

    // Handle general button clicks
    button1.addEventListener('click', () => {
        alert('Button 1 clicked!');
    });

    button2.addEventListener('click', () => {
        alert('Button 2 clicked!');
    });

    // Handle sending chat messages
    sendChatButton.addEventListener('click', () => {
        sendMessage();
    });

    chatInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });

    async function sendMessage() {
        const message = chatInput.value.trim();
        if (message !== '') {
            const messageElement = document.createElement('div');
            messageElement.classList.add('chat-message');
            messageElement.textContent = `You: ${message}`;
            chatDisplay.appendChild(messageElement);
            chatInput.value = '';
            chatDisplay.scrollTop = chatDisplay.scrollHeight; // Scroll to bottom

            // Send message to backend
            try {
                const response = await fetch('http://localhost:8001/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message }),
                });
                const data = await response.json();
                const responseElement = document.createElement('div');
                responseElement.classList.add('chat-message');
                responseElement.textContent = `Bot: ${data.response}`;
                chatDisplay.appendChild(responseElement);
                chatDisplay.scrollTop = chatDisplay.scrollHeight;
            } catch (error) {
                console.error('Error sending chat message:', error);
                const errorElement = document.createElement('div');
                errorElement.classList.add('chat-message', 'error');
                errorElement.textContent = `Bot: Error - Could not connect to backend.`;
                chatDisplay.appendChild(errorElement);
                chatDisplay.scrollTop = chatDisplay.scrollHeight;
            }
        }
    }

    // Handle PDF upload trigger
    pdfUploadInput.addEventListener('change', async (event) => {
        const file = event.target.files; // Get the first file
        if (file) {
            selectedFileNameSpan.textContent = file.name;
            
            const formData = new FormData();
            formData.append('pdf', file);

            try {
                const response = await fetch('http://localhost:8001/upload-pdf', {
                    method: 'POST',
                    body: formData,
                });
                const data = await response.json();
                if (response.ok) {
                    alert(`PDF upload successful: ${data.message}`);
                } else {
                    alert(`PDF upload failed: ${data.error}`);
                }
            } catch (error) {
                console.error('Error uploading PDF:', error);
                alert('Error uploading PDF: Could not connect to backend.');
            }
        } else {
            selectedFileNameSpan.textContent = 'No file chosen';
        }
    });
});