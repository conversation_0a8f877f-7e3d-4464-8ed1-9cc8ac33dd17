#!/usr/bin/env bash
# ~/sentinel/bootstrap.sh
set -euo pipefail
cd "$(dirname "$0")"
exec > >(tee -a logs/bootstrap.log) 2>&1
echo "=== Sentinel SSD bootstrap  $(date) ==="

# 1. Docker images (load once, idempotent)
for img in nats:2.10-alpine chromadb/chroma neo4j:5.19-community; do
  if ! docker image inspect "$img" &>/dev/null; then
     echo "  loading $img ..."
     zcat 02_images/"$(echo "$img" | tr '/:' '-')".tar.gz | docker load
  fi
done

# 2. Python venv (create on first run)
VENV="$HOME/.sentinel-venv"
[[ ! -d "$VENV" ]] && python3 -m venv "$VENV"
source "$VENV/bin/activate"
pip install --find-links 04_pip_wheels -r backend/requirements.txt

# 3. Ollama models (register once)
if ! nc -z 127.0.0.1 11434; then
  echo "  starting Ollama server..."
  ollama serve &
  sleep 10
else
  echo "  Ollama server already running."
fi
for m in phi3-128k smol-1.7b-instruct qwen2-0.5b-instruct llama-3.2-1b-instruct glm-4-9b-chat-int4; do
  [[ -f "06_scripts/modelfiles/$m.Modelfile" ]] \
    && ollama list | grep -q "$m" || ollama create "$m" -f "06_scripts/modelfiles/$m.Modelfile"
done

# 4. Start everything
echo "  starting Sentinel ..."
docker-compose -f 06_scripts/docker-compose.yml up -d
nohup python3 -m http.server 8080 --directory 05_frontend > logs/http.log 2>&1 &
echo "✅  Dashboard → http://$(hostname -I | awk '{print $1}'):8080"
