# 🤖 AI CLI Agent

Advanced command-line interface for the Offline Sentinel AI Desktop Application. This CLI agent provides powerful terminal-based interaction with AI models, file processing, and system monitoring capabilities.

## ✨ Features

### 🔥 **Core Capabilities**
- **Interactive AI Chat**: Real-time conversation with local AI models
- **File Analysis**: Process and analyze PDFs, text files, and code
- **System Monitoring**: Comprehensive status monitoring of all components
- **Command History**: Persistent command history with readline support
- **Colorized Output**: Beautiful terminal interface with color coding
- **Model Switching**: Dynamic AI model selection

### 🚀 **Advanced Features**
- **Multi-format File Support**: PDF, TXT, MD, PY, JS, JSON, YAML
- **Integration with GUI**: Seamless communication with desktop application
- **Offline Operation**: Works completely offline with local models
- **Session Management**: Conversation history and session tracking
- **Error Handling**: Graceful fallbacks and error recovery

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.8+
- Ollama running locally
- Virtual environment activated

### Quick Start
```bash
# Navigate to CLI directory
cd /home/<USER>/offline_sentinel/cli

# Activate virtual environment
source ../venv/bin/activate

# Install dependencies (if needed)
pip install ../04_pip_wheels/requests-2.32.5-py3-none-any.whl

# Make executable
chmod +x ai_agent_cli.py
chmod +x ai-agent

# Test the agent
python3 ai_agent_cli.py --status
```

## 🎯 Usage

### Command Line Options

```bash
# Interactive chat mode (default)
python3 ai_agent_cli.py
python3 ai_agent_cli.py --chat

# Single prompt
python3 ai_agent_cli.py --prompt "Your question here"

# System status
python3 ai_agent_cli.py --status

# File analysis
python3 ai_agent_cli.py --file /path/to/document.pdf

# Change AI model
python3 ai_agent_cli.py --model "qwen3:4b" --chat
```

### Interactive Commands

Once in interactive mode, you can use these commands:

#### **Chat Commands**
- Just type your message to chat with AI
- `help` - Show help information
- `status` - Display comprehensive system status
- `clear` - Clear the terminal screen
- `exit`, `quit`, `bye` - Exit the agent

#### **File Commands**
- `file <path>` - Analyze any supported file
- `model <name>` - Switch to a different AI model

#### **Examples**
```
You: file /home/<USER>/offline_sentinel/uploads/document.pdf
You: model phi3-128k
You: What are the key features of this system?
You: status
You: help
```

## 📊 System Status

The CLI agent monitors and reports on:

- **🟢 Ollama**: Local AI model server status
- **🟢 GUI API**: Desktop application API status  
- **🟢 Docker**: Container runtime status
- **📊 Models**: Available AI models count
- **💻 System**: CPU, memory, disk usage (when psutil available)

Status indicators:
- 🟢 **UP** - Service running normally
- 🔴 **DOWN** - Service not available

## 🔧 Configuration

### Available Models
The agent automatically detects available Ollama models:
- `llama-3.2-1b-instruct` (default)
- `phi3-128k`
- `qwen3:4b`
- `smol-1.7b-instruct`
- `ibm/granite4.0-preview:tiny`

### Environment Variables
```bash
# Optional: Override default settings
export OLLAMA_HOST="http://localhost:11434"
export GUI_API_HOST="http://localhost:8001"
export DEFAULT_MODEL="llama-3.2-1b-instruct"
```

## 📁 File Processing

### Supported Formats

#### **PDF Files**
- Text extraction and analysis
- AI-powered content summarization
- Integration with GUI PDF processor

#### **Text Files**
- `.txt`, `.md` - Plain text and Markdown
- `.py`, `.js` - Code analysis and suggestions
- `.json`, `.yaml` - Configuration file analysis

### File Analysis Features
- **Content Summary**: AI-generated overview
- **Key Insights**: Important findings and patterns
- **Improvement Suggestions**: Recommendations for enhancement
- **Metadata Extraction**: File size, type, and structure

## 🎨 Terminal Interface

### Color Coding
- 🔵 **Blue**: Headers and informational text
- 🟢 **Green**: Success messages and positive status
- 🟡 **Yellow**: Warnings and important notices
- 🔴 **Red**: Errors and critical issues
- 🟣 **Purple**: Headers and banners
- 🟦 **Cyan**: User input prompts

### Interactive Features
- **Command History**: Use ↑/↓ arrows to navigate history
- **Tab Completion**: Auto-complete file paths
- **Ctrl+C**: Graceful exit from any operation
- **Readline Support**: Full terminal editing capabilities

## 🔗 Integration

### With GUI Application
- Shares file processing capabilities
- Can trigger GUI operations via API
- Synchronized model and configuration management

### With System Services
- Docker container monitoring
- Ollama model management
- System resource tracking

## 🚀 Advanced Usage

### Batch Processing
```bash
# Process multiple files
for file in *.pdf; do
    python3 ai_agent_cli.py --file "$file"
done

# System monitoring script
python3 ai_agent_cli.py --status > system_report.txt
```

### Custom Prompts
```bash
# Code review
python3 ai_agent_cli.py --prompt "Review this Python code for security issues" --file script.py

# Document analysis
python3 ai_agent_cli.py --prompt "Summarize the key points" --file report.pdf
```

## 🛡️ Security & Privacy

- **Offline Operation**: No data sent to external servers
- **Local Processing**: All AI inference happens locally
- **Secure File Handling**: Safe file processing with validation
- **Privacy First**: Conversation history stored locally only

## 🐛 Troubleshooting

### Common Issues

#### Ollama Not Running
```bash
# Check Ollama status
ollama list

# Start Ollama if needed
ollama serve
```

#### Model Not Found
```bash
# List available models
ollama list

# Pull a model if needed
ollama pull llama-3.2-1b-instruct
```

#### Permission Issues
```bash
# Fix executable permissions
chmod +x ai_agent_cli.py
chmod +x ai-agent
```

### Debug Mode
```bash
# Run with verbose output
python3 -v ai_agent_cli.py --status
```

## 📈 Performance

- **Response Time**: Typically 1-3 seconds for simple queries
- **Memory Usage**: ~100MB base + model size
- **File Processing**: Handles files up to 100MB efficiently
- **Concurrent Operations**: Supports multiple simultaneous requests

## 🔄 Updates & Maintenance

### Updating Models
```bash
# Update existing model
ollama pull llama-3.2-1b-instruct

# Add new model
ollama pull qwen3:4b
```

### Clearing History
```bash
# Remove command history
rm .ai_agent_history

# Clear conversation logs
rm -rf ~/.ai_agent_sessions/
```

## 🤝 Contributing

The CLI agent is part of the larger Offline Sentinel project. Contributions welcome!

### Development Setup
```bash
# Clone and setup
git clone <repository>
cd offline_sentinel/cli

# Install development dependencies
pip install -r requirements.txt

# Run tests
python3 -m pytest tests/
```

## 📄 License

Part of the Offline Sentinel AI Desktop Application project.

---

**🎉 Enjoy your powerful AI CLI agent!** 

For more information, visit the main project documentation or run `python3 ai_agent_cli.py --help`.
