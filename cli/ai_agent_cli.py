#!/usr/bin/env python3
"""
Advanced AI CLI Agent for Offline Sentinel
Interactive command-line interface with AI chat, file processing, and system integration
"""

import argparse
import json
import subprocess
import sys
import time
import pathlib
import socket
import os
import readline
import atexit
import threading
from datetime import datetime
from typing import List, Dict, Any, Optional
import requests
import tempfile

# Configuration
CLI_DIR = pathlib.Path(__file__).parent
SENTINEL = CLI_DIR.parent
VENV = SENTINEL / "venv"
OLLAMA = "http://localhost:11434"
GUI_API = "http://localhost:8001"
DEFAULT_MODEL = "llama-3.2-1b-instruct"

# Colors for terminal output
class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

class AIAgent:
    """Advanced AI CLI Agent with chat and processing capabilities"""
    
    def __init__(self):
        self.model = DEFAULT_MODEL
        self.conversation_history = []
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.setup_readline()
        
    def setup_readline(self):
        """Setup readline for command history"""
        histfile = CLI_DIR / ".ai_agent_history"
        try:
            readline.read_history_file(histfile)
            readline.set_history_length(1000)
        except FileNotFoundError:
            pass
        atexit.register(readline.write_history_file, histfile)
        
    def print_banner(self):
        """Print welcome banner"""
        print(f"{Colors.HEADER}{Colors.BOLD}")
        print("╔══════════════════════════════════════════════════════════════╗")
        print("║                    🤖 AI CLI Agent                          ║")
        print("║              Advanced Offline Sentinel Assistant            ║")
        print("╚══════════════════════════════════════════════════════════════╝")
        print(f"{Colors.ENDC}")
        print(f"{Colors.OKCYAN}Session ID: {self.session_id}{Colors.ENDC}")
        print(f"{Colors.OKGREEN}Model: {self.model}{Colors.ENDC}")
        print(f"{Colors.WARNING}Type 'help' for commands, 'exit' to quit{Colors.ENDC}\n")
        
    def run_command(self, cmd: List[str], check=True) -> subprocess.CompletedProcess:
        """Execute system command"""
        try:
            return subprocess.run(cmd, check=check, text=True, capture_output=True)
        except subprocess.CalledProcessError as e:
            print(f"{Colors.FAIL}Command failed: {e}{Colors.ENDC}")
            return e
            
    def check_ollama_health(self) -> bool:
        """Check if Ollama is running"""
        try:
            socket.create_connection(("127.0.0.1", 11434), timeout=1).close()
            return True
        except Exception:
            return False
            
    def check_gui_health(self) -> bool:
        """Check if GUI API is running"""
        try:
            response = requests.get(f"{GUI_API}/api/health", timeout=2)
            return response.status_code == 200
        except:
            return False
            
    def ollama_chat(self, prompt: str, system_prompt: str = None) -> str:
        """Chat with Ollama model"""
        if not self.check_ollama_health():
            return f"{Colors.FAIL}❌ Ollama is not running. Please start it first.{Colors.ENDC}"
            
        try:
            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": False
            }
            
            if system_prompt:
                payload["system"] = system_prompt
                
            response = self.run_command([
                "curl", "-s", f"{OLLAMA}/api/generate", 
                "-d", json.dumps(payload),
                "-H", "Content-Type: application/json"
            ])
            
            if response.returncode == 0:
                result = json.loads(response.stdout)
                return result.get("response", "No response received").strip()
            else:
                return f"{Colors.FAIL}❌ Failed to get response from Ollama{Colors.ENDC}"
                
        except Exception as e:
            return f"{Colors.FAIL}❌ Error: {str(e)}{Colors.ENDC}"
            
    def process_file(self, file_path: str) -> str:
        """Process a file and analyze its content"""
        try:
            path = pathlib.Path(file_path)
            if not path.exists():
                return f"{Colors.FAIL}❌ File not found: {file_path}{Colors.ENDC}"
                
            # Read file content
            if path.suffix.lower() == '.pdf':
                return self.process_pdf(path)
            elif path.suffix.lower() in ['.txt', '.md', '.py', '.js', '.json', '.yaml', '.yml']:
                return self.process_text_file(path)
            else:
                return f"{Colors.WARNING}⚠️ Unsupported file type: {path.suffix}{Colors.ENDC}"
                
        except Exception as e:
            return f"{Colors.FAIL}❌ Error processing file: {str(e)}{Colors.ENDC}"
            
    def process_pdf(self, path: pathlib.Path) -> str:
        """Process PDF file"""
        try:
            # Try to use GUI API for PDF processing
            if self.check_gui_health():
                with open(path, 'rb') as f:
                    files = {'file': f}
                    response = requests.post(f"{GUI_API}/api/pdf/analyze", files=files)
                    if response.status_code == 200:
                        result = response.json()
                        return f"{Colors.OKGREEN}✅ PDF Analysis:\n{json.dumps(result, indent=2)}{Colors.ENDC}"
                        
            # Fallback to basic text extraction
            return f"{Colors.WARNING}⚠️ PDF processing requires GUI API or additional libraries{Colors.ENDC}"
            
        except Exception as e:
            return f"{Colors.FAIL}❌ Error processing PDF: {str(e)}{Colors.ENDC}"
            
    def process_text_file(self, path: pathlib.Path) -> str:
        """Process text-based file"""
        try:
            content = path.read_text(encoding='utf-8')
            
            # Analyze with AI
            analysis_prompt = f"""
            Analyze this {path.suffix} file and provide insights:
            
            File: {path.name}
            Size: {len(content)} characters
            
            Content:
            {content[:2000]}{'...' if len(content) > 2000 else ''}
            
            Please provide:
            1. Summary of the content
            2. Key insights or findings
            3. Suggestions for improvement (if applicable)
            """
            
            ai_response = self.ollama_chat(analysis_prompt)
            
            return f"{Colors.OKGREEN}✅ File Analysis for {path.name}:\n{ai_response}{Colors.ENDC}"
            
        except Exception as e:
            return f"{Colors.FAIL}❌ Error processing text file: {str(e)}{Colors.ENDC}"
            
    def system_status(self) -> str:
        """Get comprehensive system status"""
        status = []
        
        # Check Ollama
        ollama_status = "🟢 UP" if self.check_ollama_health() else "🔴 DOWN"
        status.append(f"Ollama: {ollama_status}")
        
        # Check GUI
        gui_status = "🟢 UP" if self.check_gui_health() else "🔴 DOWN"
        status.append(f"GUI API: {gui_status}")
        
        # Check Docker (try without sudo first, then with sudo)
        docker_result = self.run_command(["docker", "ps"], check=False)
        if docker_result.returncode != 0:
            docker_result = self.run_command(["sudo", "docker", "ps"], check=False)
        docker_status = "🟢 UP" if docker_result.returncode == 0 else "🔴 DOWN"
        status.append(f"Docker: {docker_status}")
        
        # Check available models
        if self.check_ollama_health():
            models_result = self.run_command(["ollama", "list"], check=False)
            if models_result.returncode == 0:
                model_count = len(models_result.stdout.strip().splitlines()) - 1
                status.append(f"Available Models: {model_count}")
                
        # System resources
        try:
            import psutil
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            status.append(f"CPU Usage: {cpu_percent:.1f}%")
            status.append(f"Memory Usage: {memory.percent:.1f}%")
            status.append(f"Disk Usage: {disk.percent:.1f}%")
        except ImportError:
            status.append("System metrics: psutil not available")
            
        return f"{Colors.OKBLUE}📊 System Status:\n" + "\n".join(f"  {s}" for s in status) + f"{Colors.ENDC}"
        
    def interactive_chat(self):
        """Start interactive chat session"""
        self.print_banner()
        
        while True:
            try:
                user_input = input(f"{Colors.OKCYAN}You: {Colors.ENDC}").strip()
                
                if not user_input:
                    continue
                    
                if user_input.lower() in ['exit', 'quit', 'bye']:
                    print(f"{Colors.OKGREEN}👋 Goodbye!{Colors.ENDC}")
                    break
                    
                elif user_input.lower() == 'help':
                    self.show_help()
                    continue
                    
                elif user_input.lower() == 'status':
                    print(self.system_status())
                    continue
                    
                elif user_input.lower() == 'clear':
                    os.system('clear' if os.name == 'posix' else 'cls')
                    self.print_banner()
                    continue
                    
                elif user_input.startswith('file '):
                    file_path = user_input[5:].strip()
                    print(self.process_file(file_path))
                    continue
                    
                elif user_input.startswith('model '):
                    new_model = user_input[6:].strip()
                    self.model = new_model
                    print(f"{Colors.OKGREEN}✅ Model changed to: {self.model}{Colors.ENDC}")
                    continue
                    
                # Regular chat
                print(f"{Colors.WARNING}🤖 AI: {Colors.ENDC}", end="", flush=True)
                
                # Add to conversation history
                self.conversation_history.append({"role": "user", "content": user_input})
                
                # Create context from conversation history
                context = "\n".join([
                    f"{'User' if msg['role'] == 'user' else 'Assistant'}: {msg['content']}"
                    for msg in self.conversation_history[-10:]  # Last 10 messages
                ])
                
                system_prompt = """You are an advanced AI assistant for the Offline Sentinel system. 
                You help users with system administration, file analysis, coding, and general questions.
                Be helpful, concise, and technical when appropriate."""
                
                response = self.ollama_chat(user_input, system_prompt)
                print(response)
                
                # Add response to history
                self.conversation_history.append({"role": "assistant", "content": response})
                
                print()  # Empty line for readability
                
            except KeyboardInterrupt:
                print(f"\n{Colors.OKGREEN}👋 Goodbye!{Colors.ENDC}")
                break
            except EOFError:
                print(f"\n{Colors.OKGREEN}👋 Goodbye!{Colors.ENDC}")
                break
                
    def show_help(self):
        """Show help information"""
        help_text = f"""
{Colors.HEADER}{Colors.BOLD}🤖 AI CLI Agent Commands:{Colors.ENDC}

{Colors.OKBLUE}Chat Commands:{Colors.ENDC}
  • Just type your message to chat with AI
  • help          - Show this help
  • status        - Show system status
  • clear         - Clear screen
  • exit/quit/bye - Exit the agent

{Colors.OKBLUE}File Commands:{Colors.ENDC}
  • file <path>   - Analyze a file (PDF, text, code)
  • model <name>  - Change AI model

{Colors.OKBLUE}System Commands:{Colors.ENDC}
  • status        - Comprehensive system status
  
{Colors.OKBLUE}Examples:{Colors.ENDC}
  • file /path/to/document.pdf
  • model llama-3.2-1b-instruct
  • What is the current system status?
  • Analyze this Python code for improvements
  
{Colors.WARNING}Note: Some features require Ollama and GUI API to be running{Colors.ENDC}
        """
        print(help_text)

def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        prog="ai-agent-cli",
        description="Advanced AI CLI Agent for Offline Sentinel"
    )
    
    parser.add_argument(
        "--model", "-m",
        default=DEFAULT_MODEL,
        help="AI model to use"
    )
    
    parser.add_argument(
        "--chat", "-c",
        action="store_true",
        help="Start interactive chat session"
    )
    
    parser.add_argument(
        "--file", "-f",
        help="Analyze a specific file"
    )
    
    parser.add_argument(
        "--status", "-s",
        action="store_true",
        help="Show system status"
    )
    
    parser.add_argument(
        "--prompt", "-p",
        help="Single prompt to AI"
    )
    
    args = parser.parse_args()
    
    # Create agent instance
    agent = AIAgent()
    agent.model = args.model
    
    # Handle different modes
    if args.status:
        print(agent.system_status())
    elif args.file:
        print(agent.process_file(args.file))
    elif args.prompt:
        response = agent.ollama_chat(args.prompt)
        print(f"{Colors.OKGREEN}🤖 AI Response:{Colors.ENDC}\n{response}")
    elif args.chat:
        agent.interactive_chat()
    else:
        # Default to interactive chat
        agent.interactive_chat()

if __name__ == "__main__":
    main()
